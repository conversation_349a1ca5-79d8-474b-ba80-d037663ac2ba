package com.facishare.open.order.contacts.proxy.api.utils;

import org.apache.commons.lang3.StringUtils;

public class EnterpriseUtils {
    /**
     * 动态生成纷享EA
     */
    public static String genEA(String enterpriseName,String prefix) {
        StringBuilder sb = new StringBuilder();
        long timestamps = System.currentTimeMillis();
        String suffix = String.valueOf(timestamps).substring(9);
        String ea = PinyinUtils.converterToFirstSpell(enterpriseName);
        if(StringUtils.isNotEmpty(ea) && ea.length() >= 2) {
            sb.append(StringUtils.substring(ea, 0, 6)).append(suffix);
        } else {
            sb.append(genEAWidthPrefix(prefix));
        }
        LogUtils.info("EnterpriseUtils.genEA,ea={}", sb.toString());
        return sb.toString();
    }

    /**
     * 动态生成纷享EA
     */
    public static String genEA2() {
        return genEAWidthPrefix("aly");
    }

    /**
     * 动态生成纷享EA
     */
    public static String genEAWidthPrefix(String prefix) {
        StringBuilder sb = new StringBuilder();
        long timestamps = System.currentTimeMillis();
        sb.append(prefix).append(timestamps % 10000000);
        LogUtils.info("EnterpriseUtils.genEAWidthPrefix,ea={}", sb.toString());
        return sb.toString();
    }

    public static void main(String[] args) {
        String genEA = genEA("北京纷扬科技有限责任公司","qywx");
        genEA = genEA("Beijing Facishare Technology Co., Ltd.","qywx");
        genEA = genEA("1234云","qywx");
        System.out.println(genEA);
    }
}
