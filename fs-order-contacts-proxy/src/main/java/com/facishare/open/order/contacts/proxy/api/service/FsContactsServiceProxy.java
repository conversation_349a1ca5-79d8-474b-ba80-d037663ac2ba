package com.facishare.open.order.contacts.proxy.api.service;

import com.facishare.open.order.contacts.proxy.api.result.Result;

import java.util.List;

/**
 * 纷享通讯录服务代理
 * <AUTHOR>
 * 2023.02.15
 */
public interface FsContactsServiceProxy {
    /**
     * 批量停用纷享员工，停用当前部门下所有员工包括子部门下的员工
     * @param ei
     * @param fsEa
     * @param fsDepId
     * @param excludeUserIdList 不需要被停用的员工ID列表
     * @return
     */
    Result<List<String>> batchStopFsEmp(int ei, String fsEa, String fsDepId,List<String> excludeUserIdList);

    /**
     * 批量停用纷享部门，停用当前部门以及所有子部门
     * @param ei
     * @param fsEa
     * @param fsDepId
     * @return
     */
    Result<List<String>> batchStopFsDep(int ei, String fsEa, String fsDepId);

    /**
     * 批量启用纷享部门
     * @param ei
     * @param fsEa
     * @param fsDepIdList
     * @return
     */
    Result<List<String>> batchResumeFsDep(int ei, String fsEa, List<String> fsDepIdList);
}
