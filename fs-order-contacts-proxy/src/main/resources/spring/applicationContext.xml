<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:task="http://www.springframework.org/schema/task" xmlns:c="http://www.springframework.org/schema/c"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
       http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">

    <context:annotation-config/>
    <task:annotation-driven executor="taskExecutor"/>
    <context:component-scan base-package="com.facishare.open.order.contacts.proxy"/>
    <import resource="spring-cms.xml"/>
    <import resource="classpath:spring/fs-fsc-rest-client.xml"/>
    <import resource="dubbo-config.xml"/>
    <import resource="dubbo-provider.xml"/>
    <import resource="dubbo-consumer.xml"/>
    <import resource="all-rest-api.xml"/>
    <import resource="classpath:fs-plat-privilege-api-rest-client.xml"/>
<!--    <import resource="spring-job.xml"/>-->
    <import resource="classpath:spring/fs-qixin-rest-client.xml"/>
    <import resource="classpath:/spring/spring-mq.xml"/>
    <!-- 引入license-->
    <import resource="classpath:spring/license-client.xml"/>
    <!--id生成器-->
    <import resource="classpath:spring/vesta-service-property-factory-bean.xml"/>
    <!--监控-->
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <!--redis配置-->
    <bean id="publishRedis" class="com.github.jedis.support.JedisFactoryBean" scope="singleton"
          p:configName="fs-erp-order-contacts-proxy"/>
    <bean id="redisDataSource" class="com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource">
        <property name="jedisCmd" ref="publishRedis"/>
    </bean>

    <!--okHttp-->
    <bean id="okHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"
          p:configName="fs-erp-order-contacts-proxy"/>


<!--    <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">-->
<!--        <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>-->
<!--    </bean>-->


<!--    <bean id="globalConfigService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">-->
<!--        <property name="factory" ref="fsiServiceProxyFactory"/>-->
<!--        <property name="type" value="com.facishare.fsi.proxy.service.GlobalConfigService"/>-->
<!--    </bean>-->
<!--    <bean id="fsiWarehouseProxyFactory" class="com.facishare.fsi.proxy.FsiWarehouseProxyFactory" init-method="init">-->
<!--        <property name="configKey" value="fs-qixin-fsi-proxy"/>-->
<!--        <property name="globalConfigService" ref="globalConfigService"/>-->
<!--    </bean>-->
<!--    <bean id="fsiServiceProxyFactory" class="com.facishare.fsi.proxy.FsiServiceProxyFactory" init-method="init">-->
<!--        <property name="configKey" value="fs-qixin-fsi-proxy"/>-->
<!--    </bean>-->

<!--    <bean id="nFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">-->
<!--        <property name="factory" ref="fsiWarehouseProxyFactory"/>-->
<!--        <property name="type" value="com.facishare.fsi.proxy.service.NFileStorageService"/>-->
<!--    </bean>-->

    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <!-- 蜂眼监控 -->
    <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"/>
    <aop:config>
        <aop:pointcut id="pointcut1" expression="(execution(* com.facishare.open.order.contacts.proxy.service.impl.*.*(..)))
            || (execution(* com.facishare.open.order.contacts.proxy.manager.*.*(..)))"/>
        <aop:pointcut id="CrmRateLimiter"
                      expression="(execution(* com.facishare.privilege.api.UserPrivilegeRestService.*(..)) or
            execution(* com.facishare.organization.adapter.api.permission.service.PermissionService.*(..)) or
            execution(* com.facishare.paas.license.http.LicenseClient.*(..)) or
            execution(* com.facishare.organization.api.service.DepartmentProviderService.*(..)) or
            execution(* com.facishare.open.order.contacts.proxy.service.impl.FsEmployeeAndDepartmentProxy.*(..)) or
            execution(* com.facishare.organization.api.service.EmployeeProviderService.*(..)) or
            execution(* com.fxiaoke.crmrestapi.service.MetadataControllerService.*(..)))"/>
        <aop:aspect ref="crmRateLimiterAspect">
            <aop:around method="around" pointcut-ref="CrmRateLimiter"/>
        </aop:aspect>
        <aop:aspect id="monitor" ref="serviceProfiler">
            <aop:around method="profile" pointcut-ref="pointcut1"/>
        </aop:aspect>
        <aop:aspect ref="serviceAspect">
            <aop:around method="around" pointcut-ref="pointcut1"/>
        </aop:aspect>
    </aop:config>


    <!-- 异步线程池 -->
    <bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <!-- 核心线程数  -->
        <property name="corePoolSize" value="1"/>
        <!-- 最大线程数 -->
        <property name="maxPoolSize" value="32"/>
        <!-- 队列最大长度 >=mainExecutor.maxSize -->
        <property name="queueCapacity" value="1800"/>
        <!-- 线程池维护线程所允许的空闲时间 -->
        <property name="keepAliveSeconds" value="300"/>
        <!--允许核心线程超时销毁-->
        <property name="allowCoreThreadTimeOut" value="true"/>
        <!-- 线程池对拒绝任务(无线程可用)的处理策略 -->
        <property name="rejectedExecutionHandler">
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>

</beans>