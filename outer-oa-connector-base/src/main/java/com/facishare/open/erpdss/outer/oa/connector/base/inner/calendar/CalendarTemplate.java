package com.facishare.open.erpdss.outer.oa.connector.base.inner.calendar;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;

/**
 * 日程模板
 * <AUTHOR>
 * @date 2024-08-20
 */

public abstract class CalendarTemplate {
    /**
     * 创建日程
     * @param context
     */
    public abstract void createCalendar(MethodContext context);

    /**
     * 更新日程
     * @param context
     */
    public abstract void updateCalendar(MethodContext context);

    /**
     * 删除日程
     * @param context
     */
    public abstract void deleteCalendar(MethodContext context);
}
