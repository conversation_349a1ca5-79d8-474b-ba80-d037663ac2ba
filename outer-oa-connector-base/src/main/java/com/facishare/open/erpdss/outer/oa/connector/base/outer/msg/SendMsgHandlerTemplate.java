package com.facishare.open.erpdss.outer.oa.connector.base.outer.msg;

import com.facishare.open.erpdss.outer.oa.connector.base.HandlerTemplate;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import lombok.extern.slf4j.Slf4j;

/**
 * 发送消息处理器模板
 * <AUTHOR>
 * @date 2024-08-20
 */

@Slf4j
public abstract class SendMsgHandlerTemplate extends HandlerTemplate {
    @Override
    public TemplateResult execute(Object data) {
        MethodContext context = MethodContext.newInstance(data);

        buildMsg(context);
        log.info("SendMsgHandlerTemplate.execute,buildMsg,context={}",context);
        if(context.isError()) {
            return context.getResult();
        }

        sendMsg(context);
        log.info("SendMsgHandlerTemplate.execute,sendMsg,context={}",context);

        return context.getResult();
    }

    /**
     * 组装消息
     * @param context
     */
    public abstract void buildMsg(MethodContext context);

    /**
     * 发送消息
     * @param context
     */
    public abstract void sendMsg(MethodContext context);
}
