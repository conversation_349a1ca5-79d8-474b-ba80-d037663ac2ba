<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:aop="http://www.springframework.org/schema/aop" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">


    <import resource="classpath:spring/spring-common.xml"/>
    <import resource="classpath:spring/spring-cms.xml"/>
    <import resource="classpath:spring/ei-ea-converter.xml"/>
    <context:annotation-config/>
<!--    <context:component-scan base-package="com.facishare.open.qywx.accountbind,com.facishare.open.qywx.accountinner,com.facishare.open.qywx.accountsync,com.facishare.open.qywx.messagesend,com.facishare.open.qywx.save,com.facishare.open.qywx.web"/>-->
</beans>