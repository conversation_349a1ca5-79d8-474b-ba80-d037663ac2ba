package com.facishare.open.qywx.web.service.impl;

import com.facishare.open.qywx.web.arg.MQArg;
import com.facishare.open.qywx.web.arg.MessageGeneratingArg;
import com.facishare.open.qywx.save.arg.MessageStorageArg;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.db.dao.MessageGeneratingDao;
import com.facishare.open.qywx.save.enums.ErrorRefer;
import com.facishare.open.qywx.web.mq.sender.QiXinMsgSender;
import com.facishare.open.qywx.web.entity.entity.MessageGeneratingPo;
import com.facishare.open.qywx.save.result.Result;
import com.facishare.open.qywx.save.service.MessageGeneratingService;
import com.facishare.open.qywx.web.utils.RSAUtil;
import com.facishare.open.qywx.web.utils.SecurityUtil;
import com.facishare.open.qywx.save.vo.GenerateSettingVo;
import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/3/25 15:50
 * @Version 1.0
 */
@Service("messageGeneratingService")
@Slf4j
public class MessageGeneratingServiceImpl implements MessageGeneratingService {

    @Autowired
    private MessageGeneratingDao generatingDao;

    @Autowired
    private QiXinMsgSender qiXinMsgSender;

    @Override
    public Result<Integer> saveSetting(GenerateSettingVo generateSettingVo) {
        log.info("message setting vo:{}",generateSettingVo);
        Gson gson = new Gson();
        MessageGeneratingPo messageGeneratingPo = new MessageGeneratingPo();
        BeanUtils.copyProperties(generateSettingVo, messageGeneratingPo);
        String storageLocation = gson.toJson(generateSettingVo.getStorageLocation());
        messageGeneratingPo.setStorageLocation(storageLocation);
        int row = generatingDao.saveInfo(messageGeneratingPo);

        if(row > 0) {
            MessageGeneratingArg messageGeneratingArg = new MessageGeneratingArg();
            messageGeneratingArg.setEa(generateSettingVo.getEa());
            messageGeneratingArg.setOutEa(generateSettingVo.getQywxCorpId());
            //根据业务key进行发送mq
            Map<String, List<String>> msgStorageLocation = gson.fromJson(ConfigCenter.MSG_STORAGE_LOCATION, new TypeToken<Map<String, List<String>>>(){}.getType());
            Set<String> msgKeySet = msgStorageLocation.keySet();
            log.info("MessageGeneratingServiceImpl.saveSetting.msgKeySet={}",msgKeySet);
            for (String msgKey : msgKeySet) {
                if(ObjectUtils.isNotEmpty(generateSettingVo.getStorageLocation())) {
                    Map<String, Integer> allMessageStorageMap = gson.fromJson(storageLocation, new TypeToken<Map<String, Integer>>(){}.getType());
                    List<String> storageLocationFiles = msgStorageLocation.get(msgKey);
                    Map<String, Integer> messageStorageMap = new HashMap<>();
                    for(String storageLocationFile : storageLocationFiles) {
                        messageStorageMap.put(storageLocationFile, allMessageStorageMap.get(storageLocationFile));
                    }
                    messageGeneratingArg.setStorageLocation(gson.fromJson(gson.toJson(messageStorageMap), new TypeToken<MessageStorageArg>(){}.getType()));
                }
                qiXinMsgSender.sendToConversionSetting(messageGeneratingArg, msgKey);
            }
            return new Result<>(row);
        } else {
            return new Result<>(ErrorRefer.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<Integer> saveSecretSetting(GenerateSettingVo generateSettingVo) {
        log.info("MessageGeneratingServiceImpl.saveSecretSettingmessage saveSecretSetting vo:{}",generateSettingVo);
        MessageGeneratingPo messageGeneratingPo=new MessageGeneratingPo();
        BeanUtils.copyProperties(generateSettingVo,messageGeneratingPo);
        int isEa = generatingDao.queryIsAuto(generateSettingVo.getEa(), generateSettingVo.getQywxCorpId());
        int row;
        if(isEa < 1) {
            row = generatingDao.saveSecretInfo(messageGeneratingPo);
            log.info("MessageGeneratingServiceImpl.saveSecretSettingmessage save");
        } else {
            row = generatingDao.updateCorpRepSecret(messageGeneratingPo);
        }
        log.info("MessageGeneratingServiceImpl.saveSecretSettingmessage save success");

        return new Result(row);
    }

    @Override
    public Result<GenerateSettingVo> querySettingByAuto(String ea, Integer version, String outEa) {
        MessageGeneratingPo settingInfo = generatingDao.getByVersion(ea, version, outEa);
        GenerateSettingVo vo=new GenerateSettingVo();
        vo.setIpList(ConfigCenter.IP_LIST);
        if(ObjectUtils.isEmpty(settingInfo)){
            try {
                Map<String, String> keyMap = RSAUtil.initKey();
                vo.setPublicKey(keyMap.get("publicKeyString"));
                vo.setPrivateKey(keyMap.get("privateKeyString"));
            } catch (Exception e) {
                e.printStackTrace();
            }
            vo.setStorageLocation(new MessageStorageArg());
            return new Result<>(vo);
        }

        BeanUtils.copyProperties(settingInfo,vo);
        vo.setStorageLocation(new Gson().fromJson(settingInfo.getStorageLocation(), MessageStorageArg.class));
        vo.setIpList(ConfigCenter.IP_LIST);
//        int auto = generatingDao.getAuto(ea);
//        vo.setAutRetention(auto);
        //不再显示自建应用的secret和agentId
        vo.setCorpSecret(null);
        vo.setAgentId(null);
        return new Result(vo);
    }

    @Override
    public Result<GenerateSettingVo> querySetting(String ea, Integer version, String outEa) {
        MessageGeneratingPo settingInfo = generatingDao.getByVersion(ea, version, outEa);
        GenerateSettingVo vo=new GenerateSettingVo();
        if(ObjectUtils.isEmpty(settingInfo)){
            return new Result<>();
        }
        BeanUtils.copyProperties(settingInfo,vo);
        vo.setStorageLocation(new Gson().fromJson(settingInfo.getStorageLocation(), MessageStorageArg.class));
        vo.setIpList(ConfigCenter.IP_LIST);
        return new Result(vo);
    }

    @Override
    public Result<List<String>> queryAllSetting() {
        return new Result(generatingDao.queryAllEa());
    }

    @Override
    public Result<List<GenerateSettingVo>> queryByEaSetting(String ea) {
        List<MessageGeneratingPo> messageGeneratingPos = generatingDao.queryByEaSetting(ea);
        List<GenerateSettingVo> messageVos= Lists.newArrayList();
        messageGeneratingPos.stream().forEach(item ->{
            GenerateSettingVo settingVo=new GenerateSettingVo();
            BeanUtils.copyProperties(item,settingVo);
            settingVo.setStorageLocation(new Gson().fromJson(item.getStorageLocation(), MessageStorageArg.class));
            messageVos.add(settingVo);
        });
        return new Result<>(messageVos);
    }

    @Override
    public Result<GenerateSettingVo> querySecretSetting(String ea) {
        MessageGeneratingPo messageGeneratingPo = new MessageGeneratingPo();
        messageGeneratingPo.setEa(ea);
        List<String> secretList = generatingDao.getSecret(messageGeneratingPo);
        if(CollectionUtils.isEmpty(secretList)) {
            return new Result<>();
        }
        log.info("MessageGeneratingServiceImpl.querySecretSetting secretList={}, ea={}", secretList, ea);
        secretList = secretList.stream().map(SecurityUtil::decryptStr).collect(Collectors.toList());
        List<String> corpAgentId = generatingDao.getAgentId(messageGeneratingPo);
        if(CollectionUtils.isEmpty(corpAgentId)) {
            return new Result<>();
        }
        log.info("MessageGeneratingServiceImpl.querySecretSetting corpAgentId={}, ea={}", corpAgentId, ea);
        GenerateSettingVo generateSettingVo = new GenerateSettingVo();
        generateSettingVo.setEa(ea);
        generateSettingVo.setCorpSecret(secretList.get(0));
        generateSettingVo.setAgentId(corpAgentId.get(0));
        return new Result<>(generateSettingVo);
    }

    @Override
    public Result<Void> sendMessage(String ea) {
        MQArg mqArg = new MQArg();
        List<String> eaList = new LinkedList<>();
        eaList.add(ea);
        mqArg.setEaList(eaList);
        qiXinMsgSender.sendToSecret(mqArg);
        return new Result<>();
    }

    @Override
    public Result<Integer> updateCorpRepSecret(GenerateSettingVo generateSettingVo) {
        MessageGeneratingPo messageGeneratingPo=new MessageGeneratingPo();
        BeanUtils.copyProperties(generateSettingVo,messageGeneratingPo);
        int row = generatingDao.updateCorpRepSecret(messageGeneratingPo);
        return new Result<>(row);
    }

    @Override
    public Result<Void> updateCorpSetting() {
        //先查询出来，再做更新操作
        List<String> eaList = generatingDao.queryAllEa();
        if(CollectionUtils.isEmpty(eaList)) {
            return new Result<>();
        }
        for(String ea : eaList) {
            List<MessageGeneratingPo> generatingPos = generatingDao.queryByEaSetting(ea);
            if(CollectionUtils.isEmpty(generatingPos)) {
                continue;
            }
            for(MessageGeneratingPo po : generatingPos) {
                int count = generatingDao.updateCorpSecret(po);
                log.info("MessageGeneratingServiceImpl.querySecretSetting count={}, version={}, ea={}", count, po.getVersion(), ea);
            }
        }
        return new Result<>();
    }

    @Override
    public Result<MessageStorageArg> getMessageStorageLocation(String fsEa, Integer version, String serviceKey, String outEa) {
        Gson gson = new Gson();
        //判断serviceKey
        Map<String, List<String>> msgStorageLocationMap = gson.fromJson(ConfigCenter.MSG_STORAGE_LOCATION, new TypeToken<Map<String, List<String>>>(){}.getType());
        if(!msgStorageLocationMap.containsKey(serviceKey)) {
            return new Result<>(ErrorRefer.SYSTEM_ERROR);
        }
        MessageGeneratingPo settingInfo = generatingDao.getByVersion(fsEa, version, outEa);
        if(ObjectUtils.isEmpty(settingInfo)) {
            return new Result<>();
        }
        //获取存储位置
        Map<String, Integer> messageStorageMap = new HashMap<>();
        if(ObjectUtils.isNotEmpty(settingInfo.getStorageLocation())) {
            Map<String, Integer> allMessageStorageMap = gson.fromJson(settingInfo.getStorageLocation(), new TypeToken<Map<String, Integer>>(){}.getType());
            List<String> storageLocationFiles = msgStorageLocationMap.get(serviceKey);
            for(String storageLocationFile : storageLocationFiles) {
                messageStorageMap.put(storageLocationFile, allMessageStorageMap.get(storageLocationFile));
            }
        }
        log.info("MessageGeneratingServiceImpl.getMessageStorageLocation messageStorageMap={}", messageStorageMap);
        return new Result<>(gson.fromJson(messageStorageMap.toString(), MessageStorageArg.class));
    }
}
