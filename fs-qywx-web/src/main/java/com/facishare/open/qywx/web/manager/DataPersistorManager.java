package com.facishare.open.qywx.web.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.web.db.dao.QyweixinAccountEnterpriseBindDao;
import com.facishare.open.qywx.web.db.dao.QyweixinCorpInfoDao;
import com.facishare.open.qywx.accountsync.result.Result;
import com.fxiaoke.cloud.DataPersistor;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 埋点统计管理
 * Created by liuwei on 2018/10/19
 */
@Slf4j
@Component
public class DataPersistorManager {


    ExecutorService dataPersistorManagerThreadPool = Executors.newFixedThreadPool(20);

    @Autowired
    QyweixinCorpInfoDao qyweixinCorpInfoDao;

    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;

    @Autowired
    private QyweixinAccountEnterpriseBindDao qyweixinAccountEnterpriseBindDao;

    /**
     * 纷享用户登录统计
     * @param corpId
     * @param appId
     */
    public void userAppLoginLog(String corpId, String appId, String userId) {
        dataPersistorManagerThreadPool.execute(()->{
            log.info("trace datapersistor userAppLogin threadPool userId:{} corpId:{}",userId, corpId);
            List<Map<String, String>> corpInfoList = qyweixinCorpInfoDao.queryCorpInfoByCorpId(corpId);
            if(corpInfoList.size() > 1){
                log.info("trace datapersistor userAppLogin check corpBind more fsEa userId:{} corpId:{}",userId, corpId);
            }

            String fsAccount = getFsAccountByOutAccount(SourceTypeEnum.QYWX.getSourceType(), corpId, userId);
            if(StringUtils.isBlank(fsAccount)){
                log.info("trace datapersistor userAppLogin_error fsAccount is empty userId:{} corpId:{}", userId, corpId);
                return;
            }
            Map<String, String> corpInfoMap = corpInfoList.get(0);
            Map<String, Object> dataPersistorMap = Maps.newHashMap();
            dataPersistorMap.put("userId", userId);
            dataPersistorMap.put("fsEa", corpInfoMap.get("fsEa"));
            dataPersistorMap.put("corpId", corpId);
            dataPersistorMap.put("fsAccount", fsAccount);
            dataPersistorMap.put("appId", appId);
            dataPersistorMap.put("corpName", corpInfoMap.get("corpName"));
            dataPersistorMap.put("bindType", null == corpInfoMap.get("bindType") ? 0 : corpInfoMap.get("bindType"));
            dataPersistorMap.put("bindTypeName", (null == corpInfoMap.get("bindType") || "0".equals(String.valueOf(corpInfoMap.get("bindType"))) ? "新建渠道":"绑定渠道"));
            DataPersistor.asyncLog("user-app-login", dataPersistorMap);
            log.info("trace datapersistor userAppLogin_success info:{}", JSONObject.toJSONString(dataPersistorMap));
        });
    }

    private String getFsAccountByOutAccount(String sourceType, String corpId, String userId) {
        Result<List<QyweixinAccountEmployeeMapping>> fsUserAccountResult = qyweixinAccountBindInnerService.getEmployeeMapping(corpId,
                userId, 0, null);
        if(!fsUserAccountResult.isSuccess()){
            log.error("trace getFsAccountByOutAccount fail, corpId:{} userId:{} ", corpId, userId);
            return null;
        }

        if(CollectionUtils.isEmpty(fsUserAccountResult.getData())){
            log.info("trace getFsAccountByOutAccount empty, corpId:{} userId:{} ", corpId, userId);
            return null;
        }
        return fsUserAccountResult.getData().get(0).getFsAccount();
    }

    public void installCRM(String corpId, String corpName, String appId, boolean newEnterprise) {
        dataPersistorManagerThreadPool.execute(()->{
            log.info("trace datapersistor installCRM threadPool corpId:{} appId:{} newInstall:{}", corpId, appId, newEnterprise);
            List<Map<String, String>> corpInfoList = qyweixinCorpInfoDao.queryCorpInfoByCorpId(corpId);
            if(corpInfoList.size() > 1){
                log.info("trace datapersistor installCRM check corpBind more fsEa appId:{} corpId:{}", appId, corpId);
            }

            Map<String, String> corpInfoMap = Maps.newHashMap();
            if(!(null == corpInfoList || corpInfoList.isEmpty())){
                corpInfoMap = corpInfoList.get(0);
            }
            Map<String, Object> dataPersistorMap = Maps.newHashMap();
            dataPersistorMap.put("fsEa", (null == corpInfoMap || corpInfoMap.isEmpty()) ? "" :corpInfoMap.get("fsEa"));
            dataPersistorMap.put("corpId", corpId);
            dataPersistorMap.put("corpName", corpName);
            dataPersistorMap.put("appId", appId);

            if(newEnterprise){
                dataPersistorMap.put("bindType", 0);
                dataPersistorMap.put("bindTypeName", "新建渠道");
            } else {
                if(null == corpInfoMap || corpInfoMap.isEmpty()){
                    log.info("trace datapersistor installCRM_error corpInfo is empty corpId:{}", corpId);
                    return;
                }
                dataPersistorMap.put("bindType", null == corpInfoMap.get("bindType") ? 0 : corpInfoMap.get("bindType"));
                dataPersistorMap.put("bindTypeName", (null == corpInfoMap.get("bindType") || "0".equals(String.valueOf(corpInfoMap.get("bindType"))) ? "新建渠道":"绑定渠道"));
            }
            DataPersistor.asyncLog("install-crm", dataPersistorMap);
            log.info("trace datapersistor installCRM_success info:{}", JSONObject.toJSONString(dataPersistorMap));
        });
    }

    /**
     * 统计绑定的纷享企业
     * @param arg
     */
    public void enterpriseAccountBind(QyweixinAccountEnterpriseMapping arg) {
        dataPersistorManagerThreadPool.execute(()->{

            if(StringUtils.isBlank(arg.getOutName())){
                Map<String, String> accountEnterpriseMap = qyweixinAccountEnterpriseBindDao.queryCorpInfoByCorpId(arg.getOutEa());
                try {
                    if(null == accountEnterpriseMap){
                        Thread.sleep(500L);
                        accountEnterpriseMap = qyweixinAccountEnterpriseBindDao.queryCorpInfoByCorpId(arg.getOutEa());
                        if(null != accountEnterpriseMap && !accountEnterpriseMap.isEmpty()){
                            arg.setOutName(accountEnterpriseMap.get("corpName"));
                        } else {
                            log.info("trace enterpriseAccountBind getCorpInfo error");
                        }
                    } else{
                        arg.setOutName(accountEnterpriseMap.get("corpName"));
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }

            Map<String, Object> dataPersistorMap = Maps.newHashMap();
            dataPersistorMap.put("fsEa", arg.getFsEa());
            dataPersistorMap.put("outEa", arg.getOutEa());
            dataPersistorMap.put("outEaName", arg.getOutName());
            dataPersistorMap.put("source", arg.getSource());
            dataPersistorMap.put("bindType", arg.getBindType());
            dataPersistorMap.put("bindTypeName", (null == arg.getBindType() || "0".equals(String.valueOf(arg.getBindType()))) ? "新建渠道":"绑定渠道");
            DataPersistor.asyncLog("enterprise-account-bind", dataPersistorMap);
            log.info("trace datapersistor enterpriseAccountBind_success info:{}", JSONObject.toJSONString(dataPersistorMap));
        });
    }
}
