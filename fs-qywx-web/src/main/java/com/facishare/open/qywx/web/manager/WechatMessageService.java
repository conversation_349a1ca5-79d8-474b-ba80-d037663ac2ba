package com.facishare.open.qywx.web.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.restful.client.FRestApiProxyFactory;
import com.fxiaoke.api.MessageService;
import com.fxiaoke.model.MessageConstants;
import com.fxiaoke.model.MessageRequest;
import com.fxiaoke.model.MessageResponse;
import com.fxiaoke.model.wechat.NewArticle;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by mawen<PERSON>i on 2018/9/12.
 */
@Service
@Slf4j
public class WechatMessageService {

    public void sendMessage(String enterpriseAccount, String title, String url, String picUrl, List<String> reveiverIds) {
        // 发送欢迎消息
        try {
            NewArticle newArticle = new NewArticle();
            newArticle.setTitle(title);
            newArticle.setUrl(url);
            newArticle.setPicurl(picUrl);
            newArticle.setDescription(title);
            newArticle.setBtntxt("更多");

            Map<String, Object> extensionMap = Maps.newHashMap();
            extensionMap.put(MessageConstants.ExtensionKey.WECHAT_NEWS_ARTICLE, JSON.toJSONString(Lists.newArrayList(newArticle)));

            MessageRequest messageRequest = MessageRequest.builder()
                    .messageType(4)
                    .ea(enterpriseAccount)
                    .title(title)
                    .channels(Collections.singletonList(MessageConstants.ChannelType.WECHAT))
                    .receiverIds(reveiverIds)
                    .extensionMap(extensionMap)
                    .build();

            log.info("发送欢迎消息 {}", JSON.toJSONString(messageRequest));

            MessageService messageService = FRestApiProxyFactory.getInstance().create(MessageService.class);
            MessageResponse result = messageService.send(messageRequest);

            log.info("欢迎消息结果 {}", JSON.toJSONString(result));

            if (result.getCode() == MessageResponse.SUCCESS_CODE) {
                log.info("message send success");
            } else {
                log.error("message send failed");
            }
        } catch (Exception ex) {
            log.error("message send failed, ex is {}", ex);
        }
    }
}
