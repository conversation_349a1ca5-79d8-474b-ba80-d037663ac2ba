package com.facishare.open.qywx.web.threadpool;

import java.util.concurrent.*;

/**
 * 线程池专用工具类
 * <AUTHOR>
 * @date 20230224
 */
public class ThreadPoolHelper {
    //企微第三方应用事件专用线程池
    public static ThreadPoolExecutor msgEventThreadPool = new ThreadPoolExecutor(50,
            100,
            30,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>()
    );

    //企微第三方应用数据事件专用线程池
    public static ThreadPoolExecutor dataEventThreadPool = new ThreadPoolExecutor(10,
            30,
            30,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>()
    );

    //企微代开发应用事件专用线程池
    public static ThreadPoolExecutor repEventThreadPool = new ThreadPoolExecutor(10,
            50,
            30,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>()
    );

    //获取代开发应用人员和部门详情专用线程池
    public static ThreadFactory saveOrUpdateUserAndDepartmentInfoThreadFactory = new ThreadFactory() {
        private int count = 0;

        @Override
        public Thread newThread(Runnable r) {
            count++;
            Thread thread = new Thread(r);
            thread.setName("saveOrUpdateUserAndDepartmentInfoThread-" + count);
            return thread;
        }
    };

    public static ExecutorService saveOrUpdateUserAndDepartmentInfoThreadPool = Executors.newFixedThreadPool(30, saveOrUpdateUserAndDepartmentInfoThreadFactory);
}
