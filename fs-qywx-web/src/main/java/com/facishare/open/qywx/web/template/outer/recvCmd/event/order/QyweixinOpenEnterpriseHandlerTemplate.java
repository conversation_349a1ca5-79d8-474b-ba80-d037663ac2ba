package com.facishare.open.qywx.web.template.outer.recvCmd.event.order;

import com.facishare.enterprise.event.EnterpriseAddEvent;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.order.OpenEnterpriseHandlerTemplate;

import com.facishare.open.feishu.syncapi.config.GlobalValue;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.arg.SendTextNoticeArg;
import com.facishare.open.qywx.accountinner.service.NotificationService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;

import com.facishare.open.qywx.accountsync.core.enums.QYWXDataTypeEnum;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.model.OAConnectorOpenDataModel;
import com.facishare.open.qywx.accountsync.model.QyweixinEnterpriseOrder;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinOrderInfoBo;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.ContactsService;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.db.dao.QyweixinOrderInfoDao;
import com.facishare.open.qywx.web.manager.*;
import com.facishare.open.qywx.web.model.qyweixin.QyweixinGetPermenantCodeRsp;
import com.facishare.open.qywx.web.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class QyweixinOpenEnterpriseHandlerTemplate extends OpenEnterpriseHandlerTemplate {
    @Resource
    private OrderService orderService;
    @Resource
    private CorpManager corpManager;

    @Resource
    private QyweixinOrderInfoDao qyweixinOrderInfoDao;

    @Resource
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Resource
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;

    @Resource
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;

    @Resource
    private NotificationService notificationService;
    @Resource
    private ContactsService contactsService;

    @Override
    public void saveOrder(MethodContext context) {
        log.info("QyweixinOpenEnterpriseHandlerTemplate.saveOrder,context={}",context);
        Map<String, Object> appOpenMap = context.getData();

        String appId = (String) appOpenMap.get("appId");
        String authCode = (String) appOpenMap.get("authCode");
        QyweixinGetPermenantCodeRsp corpAuthInfo = (QyweixinGetPermenantCodeRsp) appOpenMap.get("corpAuthInfo");

        //迁移企业账号信息
        String corpId = corpAuthInfo.getAuth_corp_info().getCorpid();//this.refreshEnterpriseAccount(corpAuthInfo.getAuth_corp_info().getCorpid(), appId, null);
        log.info("QyweixinOpenEnterpriseHandlerTemplate.getAuthInfoDoInitCorp,corpId={}", corpId);

        //订单表一定为密文的corpId，这里需要做转换
        //代客下单，订单信息是没有operator_id的，所以，代客下单后，客户在企业微信安装完crm应用，需要更新代客下单的订单上的operator_id字段，因为俊文需要这个字段来创建企业管理员帐号
        QyweixinOrderInfoBo qyweixinOrderInfoBo = qyweixinOrderInfoDao.getLatestPaidOrder(corpId,appId,null);
        log.info("QyweixinOpenEnterpriseHandlerTemplate.getAuthInfoDoInitCorp,qyweixinOrderInfoBo={}",qyweixinOrderInfoBo);
        if(qyweixinOrderInfoBo!=null && qyweixinOrderInfoBo.getOrderFrom()!=0 && StringUtils.isEmpty(qyweixinOrderInfoBo.getOperatorId())) {
            qyweixinOrderInfoBo.setOperatorId(corpAuthInfo.getAuth_user_info().getUserid());
            qyweixinOrderInfoDao.saveOrUpdateOrder(qyweixinOrderInfoBo);
            log.info("QyweixinOpenEnterpriseHandlerTemplate.getAuthInfoDoInitCorp,saveOrUpdateOrder");
        }

        if(corpManager.isManualBinding(corpId)) {
            context.setResult(TemplateResult.newError(ErrorRefer.MANUAL_BINDING_NOT_SUPPORTED.getQywxCode()));
        }

        //未有绑定关系的才创建企业
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> accountBindResult =
                qyweixinAccountBindService.selectEnterpriseBind(SourceTypeEnum.QYWX.getSourceType(), corpId);
        log.info("QyweixinOpenEnterpriseHandlerTemplate.getAuthInfoDoInitCorp,resultAccount={}",accountBindResult);
        boolean isOpen = Boolean.FALSE;
        if(CollectionUtils.isEmpty(accountBindResult.getData()) || accountBindResult.getData().stream()
                .anyMatch(bindInfo -> bindInfo.getStatus() == 100)) {
            isOpen = Boolean.TRUE;
        }

        if(!isOpen) {
            context.setResult(TemplateResult.newError(null));
        }

        new Thread(() -> enterpriseOpenMonitor(corpId, appId)).start();

        Result<QyweixinEnterpriseOrder> result = corpManager.initCorpEvent(corpAuthInfo, appId, authCode);
        log.info("QyweixinOpenEnterpriseHandlerTemplate.getAuthInfoDoInitCorp,initCorpEvent.result={}",result);

        if(result.isSuccess()) {
            context.setData(result.getData());
            context.setResult(TemplateResult.newSuccess());
        } else {
            context.setResult(TemplateResult.newError(result.getErrorMsg()));
        }
        log.info("QyweixinOpenEnterpriseHandlerTemplate.saveOrder,context.end={}",context);
    }

    @Override
    public void initEnterpriseAndAdminMapping(MethodContext context) {
        log.info("QyweixinOpenEnterpriseHandlerTemplate.initEnterpriseAndAdminMapping,context={}",context);
        //暂时不实现
    }

    @Override
    public void createFsCustomerAndUpdateEnterpriseAndAdminMapping(MethodContext context) {
        log.info("QyweixinOpenEnterpriseHandlerTemplate.createFsCustomerAndUpdateEnterpriseAndAdminMapping,context={}",context);

        QyweixinEnterpriseOrder enterpriseOrder = context.getData();

        Result<String> result = orderService.createCustomerAndUpdateMapping(enterpriseOrder);
        log.info("QyweixinOpenEnterpriseHandlerTemplate.createFsCustomerAndUpdateEnterpriseAndAdminMapping,result={}",result);
        if(result.isSuccess()) {
            Map<String,Object> data = new HashMap<>();
            data.put("fsEa",result.getData());
            data.put("enterpriseOrder",enterpriseOrder);
            context.setData(data);
            context.setResult(TemplateResult.newSuccess());
        } else {
            context.setResult(TemplateResult.newError(result.getErrorMsg()));
        }
        log.info("QyweixinOpenEnterpriseHandlerTemplate.createFsCustomerAndUpdateEnterpriseAndAdminMapping,context.end={}",context);
    }

    @Override
    public void createFsOrder(MethodContext context) {
        log.info("QyweixinOpenEnterpriseHandlerTemplate.createFsOrder,context={}",context);
        Map<String,Object> contextMap = context.getData();

        String fsEa = (String) contextMap.get("fsEa");
        QyweixinEnterpriseOrder enterpriseOrder = (QyweixinEnterpriseOrder) contextMap.get("enterpriseOrder");

        Result<Void> result = orderService.createCrmOrder(fsEa, enterpriseOrder);

        log.info("QyweixinOpenEnterpriseHandlerTemplate.createFsOrder,result={}",result);
        if(result.isSuccess()) {
            context.setResult(TemplateResult.newSuccess());
        } else {
            context.setResult(TemplateResult.newError(result.getErrorMsg()));
        }
        log.info("QyweixinOpenEnterpriseHandlerTemplate.createFsOrder,context.end={}",context);
    }

    @Override
    public boolean isEnterpriseBind(String ea) {
        log.info("QyweixinOpenEnterpriseHandlerTemplate.isEnterpriseBind,ea={}",ea);
        return orderService.isEnterpriseBind(ea).getData();
    }

//    @Override
//    public TemplateResult onEnterpriseOpened(Object data) {
//        return super.onEnterpriseOpened(data);
//    }

    @Override
    public void updateEnterpriseAndAdminMapping(MethodContext context) {
        log.info("QyweixinOpenEnterpriseHandlerTemplate.updateEnterpriseAndAdminMapping,context={}",context);
        EnterpriseAddEvent enterpriseAddEvent = context.getData();

        Result<Void> result = orderService.updateEnterpriseAndAdminMapping(enterpriseAddEvent.getEnterpriseAccount(),
                GlobalValue.FS_ADMIN_USER_ID + "");
        log.info("QyweixinOpenEnterpriseHandlerTemplate.updateEnterpriseAndAdminMapping,result={}",result);
    }

    @Override
    public void sendWelcomeMsg(MethodContext context) {
        log.info("QyweixinOpenEnterpriseHandlerTemplate.sendWelcomeMsg,context={}",context);
        EnterpriseAddEvent enterpriseAddEvent = context.getData();
        Result<Void> result = orderService.sendWelcomeMsg(enterpriseAddEvent.getEnterpriseAccount());
        log.info("QyweixinOpenEnterpriseHandlerTemplate.sendWelcomeMsg,result={}",result);
    }

    @Override
    public void initEnterpriseContacts(MethodContext context) {
        log.info("QyweixinOpenEnterpriseHandlerTemplate.initEnterpriseContacts,context={}",context);
        EnterpriseAddEvent enterpriseAddEvent = context.getData();
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEmployeeMapping> employeeMapping
                = qyweixinAccountBindService.getQywxEmployeeMapping2(enterpriseAddEvent.getEnterpriseAccount(), "1000",null);
        Result<Void> result = contactsService.initContactsAsync(employeeMapping.getData().getAppId(),employeeMapping.getData().getOutEa(),enterpriseAddEvent.getEnterpriseAccount());
        log.info("QyweixinOpenEnterpriseHandlerTemplate.initEnterpriseContacts,result={}",result);
    }

    private void enterpriseOpenMonitor(String outEa, String appId) {
        log.info("QyweixinOpenEnterpriseHandlerTemplate.enterpriseOpenMonitor,outEa={},appId={}", outEa, appId);
        //睡眠一分钟
        try {
            Thread.sleep(60 * 1000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        //查库

        Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindInnerService.queryCorpBindByOutEa(outEa, null);
        if(enterpriseMappingResult.isSuccess() && ObjectUtils.isNotEmpty(enterpriseMappingResult.getData())) {
            QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingResult.getData();
            if(enterpriseMapping.getStatus() == 100) {
                //上报
                OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                        .ea(enterpriseMapping.getFsEa())
                        .appId(appId)
                        .channelId(ChannelEnum.qywx.name())
                        .dataTypeId(QYWXDataTypeEnum.ENTERPRISE_CREATE.getDataType())
                        .corpId(outEa)
                        .errorCode("100")
                        .errorMsg("超过一分钟，该企业还未创建成功，请及时关注！")
                        .build();
                oaConnectorOpenDataManager.send(model);
                //告警
                SendTextNoticeArg arg = new SendTextNoticeArg();
                arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                List<String> receivers = new LinkedList<>(ConfigCenter.ENTERPRISE_OPEN_NOTIFICATION_MEMBERS);
                arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                arg.setMsgTitle("企微企业开通失败告警");
                String msg = String.format("超过一分钟，该企业还未创建成功\n纷享企业ea=%s\n请及时关注！", enterpriseMapping.getFsEa());
                arg.setMsg(msg);
                notificationService.sendQYWXNotice(arg);
            }
        } else {
            //没有记录，自己接收告警
            //上报
            OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                    .appId(appId)
                    .channelId(ChannelEnum.qywx.name())
                    .dataTypeId(QYWXDataTypeEnum.ENTERPRISE_CREATE.getDataType())
                    .corpId(outEa)
                    .errorCode("101")
                    .errorMsg("超过一分钟，该企业没有绑定记录，请及时关注！")
                    .build();
            oaConnectorOpenDataManager.send(model);
            //告警
            SendTextNoticeArg arg = new SendTextNoticeArg();
            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
            arg.setMsgTitle("企微企业开通失败告警");
            String msg = String.format("超过一分钟，该企业没有绑定记录\n企微企业ea=%s\n请及时关注！", outEa);
            arg.setMsg(msg);
            notificationService.sendQYWXNotice(arg);
        }
    }
}
