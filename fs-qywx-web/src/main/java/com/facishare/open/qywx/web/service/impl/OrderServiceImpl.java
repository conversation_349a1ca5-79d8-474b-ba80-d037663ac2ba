package com.facishare.open.qywx.web.service.impl;

import com.facishare.open.order.contacts.proxy.api.arg.CreateCrmOrderArg;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCustomerArg;
import com.facishare.open.order.contacts.proxy.api.enums.CustomerSourceEnum;
import com.facishare.open.order.contacts.proxy.api.model.CrmOrderMappingModel;
import com.facishare.open.order.contacts.proxy.api.service.FsOrderServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.EnterpriseUtils;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.model.CreateCrmEnterpriseEventProto;
import com.facishare.open.qywx.accountsync.model.ProductDefine;
import com.facishare.open.qywx.accountsync.model.QyweixinEnterpriseOrder;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinOrderInfoBo;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.ContactsService;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.core.enums.OrderTypeEnum;
import com.facishare.open.qywx.web.db.dao.QyweixinOrderInfoDao;
import com.facishare.open.qywx.web.manager.CorpManager;
import com.facishare.open.qywx.web.manager.OrderManager;
import com.facishare.open.qywx.web.manager.WechatMessageService;
import com.facishare.open.qywx.web.mq.sender.MQSender;
import com.facishare.open.qywx.web.service.OrderService;
import com.facishare.open.qywx.web.utils.DateUtils;
import com.facishare.open.qywx.web.utils.WechatRegisterConfigHelper;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Service("orderService")
@Slf4j
public class OrderServiceImpl implements OrderService {
    @Resource
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Resource
    private FsOrderServiceProxy fsOrderServiceProxy;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;
    @Resource
    private CorpManager corpManager;
    @Resource
    private OrderManager orderManager;
    @Autowired
    private WechatMessageService wechatMessageService;
    @Resource
    private MQSender mqSender;
    @Resource
    private ContactsService contactsService;

    /**
     * 标识试用版本枚举的key
     */
    private static final String TRY_EDITION_ENUM_NAME = "tryEditionEnumName";

    @Override
    public Result<Integer> saveOrder(QyweixinOrderInfoBo qyweixinOrderInfoBo) {

        return null;
    }

    @Override
    public Result<String> createCustomerAndUpdateMapping(QyweixinEnterpriseOrder enterpriseOrder) {
        log.info("OrderManager.openEnterprise,enterpriseOrder={}", enterpriseOrder);

        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> accountBindResult =
                qyweixinAccountBindService.selectEnterpriseBind(SourceTypeEnum.QYWX.getSourceType(), enterpriseOrder.getCorpId());
        log.info("OrderManager.openEnterprise,outEaToFsEa,accountBindResult={}", accountBindResult);

        String fsEa;
        if(CollectionUtils.isEmpty(accountBindResult.getData()) || accountBindResult.getData().get(0).getStatus() == 100) {
            //查询客户是否存在

            com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectData>> listResult = fsOrderServiceProxy.queryCustomer(ConfigCenter.MASTER_EA, enterpriseOrder.getCorpId());
            if(CollectionUtils.isNotEmpty(listResult.getData())) {
                if(CollectionUtils.isEmpty(accountBindResult.getData())) {
                    //有客户，没有绑定记录，查询企业是否创建成功
                    fsEa = listResult.getData().get(0).get(ConfigCenter.CRM_CUSTOM_ENTERPRISE_ID_FILE).toString();
                    try {
                        GetSimpleEnterpriseArg arg = new GetSimpleEnterpriseArg();
                        arg.setEnterpriseAccount(fsEa);
                        enterpriseEditionService.getSimpleEnterprise(arg);
                        corpManager.enterpriseBind(enterpriseOrder.getCorpId(),fsEa,0);
                        corpManager.employeeBind(enterpriseOrder.getCorpId(),fsEa,enterpriseOrder.getAppId(),enterpriseOrder.getUserId(),0);
                    } catch(Exception e) {
                        corpManager.enterpriseBind(enterpriseOrder.getCorpId(),fsEa,100);
                        corpManager.employeeBind(enterpriseOrder.getCorpId(),fsEa,enterpriseOrder.getAppId(),enterpriseOrder.getUserId(),100);
                    }
                } else {
                    fsEa = accountBindResult.getData().get(0).getFsEa();
                }
            } else {
                //创建客户
                String customerName = enterpriseOrder.getCorpName();
                fsEa = EnterpriseUtils.genEA(customerName,"qywx");
                log.info("OrderManager.openEnterprise,fsEa={}", fsEa);

                CreateCustomerArg customerArg = CreateCustomerArg.builder()
                        .source(CustomerSourceEnum.QYWX.getSource())
                        .outEid(enterpriseOrder.getCorpId())
                        .managerName(enterpriseOrder.getUserName())
                        .managerMobile(null)//获取不到手机号
                        .enterpriseName(customerName)
                        .enterpriseAccount(fsEa)
                        .build();

                com.facishare.open.order.contacts.proxy.api.result.Result<Void> createCustomerResult = fsOrderServiceProxy.createCustomer(customerArg);
                log.info("OrderManager.openEnterprise,createCustomer,customerArg={},createCustomerResult={}", customerArg,createCustomerResult);
                if(!createCustomerResult.isSuccess()) {
                    throw new RuntimeException("创建客户失败，客户名称="+customerName+",outEid="+customerArg.getOutEid());
                }
                corpManager.enterpriseBind(enterpriseOrder.getCorpId(),fsEa,100);
                corpManager.employeeBind(enterpriseOrder.getCorpId(),fsEa,enterpriseOrder.getAppId(),enterpriseOrder.getUserId(),100);
            }
        } else {
            fsEa = accountBindResult.getData().get(0).getFsEa();
        }

        return new Result<>(fsEa);
    }

    @Override
    public Result<Void> createCrmOrder(String fsEa, QyweixinEnterpriseOrder enterpriseOrder) {
        Integer allResourceCount = enterpriseOrder.getUserCount();
        Long beginTime = null;
        Long endTime = null;
        Long price = enterpriseOrder.getPrice();
        String orderId = enterpriseOrder.getOrderId();
        ProductDefine productDefine = null;
        Integer orderType = CreateCrmOrderArg.CrmOrderDetailInfo.ORDER_TYPE_BUY;
        if(enterpriseOrder.getOrderType() == OrderTypeEnum.TRYOUT.getCode()) {
            productDefine = orderManager.getProductDefine(enterpriseOrder.getAppId(), TRY_EDITION_ENUM_NAME);
            //试用版创建10000个CRM用户
            allResourceCount = ConfigCenter.createCrmAccount;
            orderType = CreateCrmOrderArg.CrmOrderDetailInfo.ORDER_TYPE_TRY;
            //试用时，开始begin time为空
            beginTime = DateUtils.date2Timestamp(DateUtils.nowDate());
            endTime = DateUtils.date2Timestamp(enterpriseOrder.getEndTime());
            //试用版orderId字段为空，这里用一个组合ID作为订单ID
            orderId = "qywx_" + enterpriseOrder.getCorpId() + "_" + enterpriseOrder.getAppId();
            price = 0L;
        } else {
            productDefine = orderManager.getProductDefine(enterpriseOrder.getAppId(), enterpriseOrder.getEditionId());

            if(StringUtils.isEmpty(enterpriseOrder.getBeginTime())) {
                beginTime = DateUtils.date2Timestamp(DateUtils.nowDate());
            } else {
                beginTime = DateUtils.date2Timestamp(enterpriseOrder.getBeginTime());
            }
            if(StringUtils.isEmpty(enterpriseOrder.getEndTime())) {
                //默认再给7天
                log.info("OrderManager.openEnterprise,createCustomer,set endTime,outEa={}", enterpriseOrder.getCorpId());
                endTime = LocalDateTime.now().plusDays(7).toInstant(ZoneOffset.of("+8")).getEpochSecond();
            } else {
                endTime = DateUtils.date2Timestamp(enterpriseOrder.getEndTime());
            }
        }
        log.info("OrderManager.openEnterprise,productDefine={}", productDefine);

        Integer quality = 1;
        // 企业微信标准版，单套资源数是5个配额，个数需要除以5
        if (Objects.equals(ProductDefine.QYWX_TRY, productDefine) || Objects.equals(ProductDefine.QYWX_BASE, productDefine)) {
            quality = allResourceCount / 5;
        }

        log.info("OrderManager.openEnterprise,quality={},allResourceCount={}", quality,allResourceCount);

        List<String> productIdList = orderManager.getProductIdList(enterpriseOrder.getAppId(),enterpriseOrder.getEditionId());
        List<CreateCrmOrderArg> orderArgList = new ArrayList<>();
        for(String productId : productIdList) {
            CreateCrmOrderArg.CrmOrderDetailInfo orderDetailInfo = CreateCrmOrderArg.CrmOrderDetailInfo.builder()
                    .enterpriseAccount(fsEa)
                    .orderId(orderId)
                    .orderTime(beginTime)
                    .orderTpye(orderType)
                    .build();

            //订单金额单位是 分，需要转换成 元
            BigDecimal orderAmount = BigDecimal.valueOf(price / 100.0);
            CreateCrmOrderArg.CrmOrderProductInfo orderProductInfo = CreateCrmOrderArg.CrmOrderProductInfo.builder()
                    .beginTime(beginTime)
                    .endTime(endTime)
                    .allResourceCount(allResourceCount)
                    .quantity(quality)
                    .orderAmount(orderAmount + "")
                    .productId(productId)
                    .build();

            CreateCrmOrderArg orderArg = CreateCrmOrderArg.builder()
                    .crmOrderDetailInfo(orderDetailInfo)
                    .crmOrderProductInfo(orderProductInfo)
                    .build();
            orderArgList.add(orderArg);
        }

        log.info("OrderManager.openEnterprise,batchCreateCrmOrder,orderArgList={}", orderArgList);

        com.facishare.open.order.contacts.proxy.api.result.Result<List<CrmOrderMappingModel>> batchCreateCrmOrderResult = fsOrderServiceProxy.batchCreateCrmOrder(orderArgList);
        log.info("OrderManager.openEnterprise,batchCreateCrmOrder,batchCreateCrmOrderResult={}", batchCreateCrmOrderResult);
        if(!batchCreateCrmOrderResult.isSuccess()) {
            return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
        }


        for(CrmOrderMappingModel orderMappingModel : batchCreateCrmOrderResult.getData()) {
            if(StringUtils.isEmpty(orderMappingModel.getOrderId())) {
                //批量创建订单，有可能部分订单会创建失败，实际上应该不会发生这种情况
                log.info("OrderManager.openEnterprise,batchCreateCrmOrder,order create failed,productId={}", orderMappingModel.getProductId());
            }
        }
        return new Result<>();
    }

    @Override
    public Result<Boolean> isEnterpriseBind(String fsEa) {
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> fsEaToOutEaResult = qyweixinAccountBindService.fsEaToOutEaResult(SourceTypeEnum.QYWX.getSourceType(), fsEa);
        log.info("FsEventServiceImpl.isEnterpriseBind,fsEaToOutEaResult,fsEaToOutEaResult={}", fsEaToOutEaResult);
        if(fsEaToOutEaResult.getData()!=null) {
            return new Result<>(true);
        }
        return new Result<>(false);
    }

    @Override
    public Result<Void> updateEnterpriseAndAdminMapping(String fsEa, String userId) {
        //更新企业绑定状态为正常
        corpManager.updateEnterpriseBindStatus(fsEa,0);
        //更新管理员绑定状态为正常
        corpManager.updateEmployeeBindStatus("E." + fsEa + ".1000", 0);

        return new Result<>();
    }

    @Override
    public Result<Void> sendWelcomeMsg(String fsEa) {
        //给管理员发送欢迎消息
        sendWelcomeMessage(fsEa, Lists.newArrayList("1000"));

        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEmployeeMapping> employeeMapping
                = qyweixinAccountBindService.getQywxEmployeeMapping2(fsEa, "1000",null);
        log.info("FsEventServiceImpl.onEnterpriseOpened,employeeMapping={}", employeeMapping);

        //发送安装成功的mq
        CreateCrmEnterpriseEventProto eventProto = new CreateCrmEnterpriseEventProto();
        eventProto.setAppId(employeeMapping.getData().getAppId());
        eventProto.setCorpId(employeeMapping.getData().getOutEa());
        eventProto.setFsEa(fsEa);
        eventProto.setFsUserId("1000");
        eventProto.setUserId(employeeMapping.getData().getOutAccount());
        mqSender.sendEnterpriseCreateMQ("tag_create_crm_enterprise", eventProto);
        return new Result<>();
    }

    public void sendWelcomeMessage(String fsEA, List<String> userIds) {
        WechatRegisterConfigHelper.getWechatMessageValue().forEach((key, value) -> {
            wechatMessageService.sendMessage(
                    fsEA,
                    value.getTitle(),
                    value.getUrl(),
                    value.getPicUrl(),
                    userIds
            );
        });
    }
}
