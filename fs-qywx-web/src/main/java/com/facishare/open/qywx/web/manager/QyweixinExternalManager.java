package com.facishare.open.qywx.web.manager;

import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.model.QyweixinExternalContactRsp;
import com.facishare.open.qywx.web.core.enums.QyweixinExternalTransferEnum;
import com.facishare.open.qywx.web.db.dao.QyweixinExternalContactDao;
import com.facishare.open.qywx.web.db.dao.QyweixinExternalContactTransferDao;
import com.facishare.open.qywx.web.db.dao.QyweixinUserDao;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinExternalContactBo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinExternalContactTransferBo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinUserBo;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Component
public class QyweixinExternalManager {

    @Autowired
    private QyweixinUserDao qyweixinUserDao;
    @Autowired
    private QyweixinExternalContactDao qyweixinExternalContactDao;
    @Autowired
    private QyweixinExternalContactTransferDao qyweixinExternalContactTransferDao;
    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Autowired
    private FsManager fsManager;

    public List<QyweixinExternalContactTransferInfo> getExternalContactTransfer(QyweixinQueryTransferInfo transferInfo) {
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> qyweixinCorpIDResult =
                qyweixinAccountBindService.fsEaToOutEaResult2("qywx", transferInfo.getEa(),
                        transferInfo.getOutEa());
        if(!qyweixinCorpIDResult.isSuccess() || ObjectUtils.isEmpty(qyweixinCorpIDResult.getData())) {
            return null;
        }
        List<QyweixinExternalContactTransferInfo> transferMapping = qyweixinExternalContactTransferDao.getExternalContactTransferMapping(transferInfo);
        return transferMapping;
    }

    public void saveExternalContactTransfer(QyweixinTransferInfo transferInfo) {
        //如果名称为空，取CRM的名称
        if(StringUtils.isEmpty(transferInfo.getHandoverUserName())) {
            List<EmployeeDto> employeeInfos = fsManager.getEmployeeInfos(transferInfo.getEa(), 1000, Lists.newArrayList(Integer.parseInt(transferInfo.getHandoverUserId())));
            transferInfo.setHandoverUserName(employeeInfos.get(0).getName());
        }
        if(StringUtils.isEmpty(transferInfo.getTakeoverUserName())) {
            List<EmployeeDto> employeeInfos = fsManager.getEmployeeInfos(transferInfo.getEa(), 1000, Lists.newArrayList(Integer.parseInt(transferInfo.getTakeoverUserId())));
            transferInfo.setTakeoverUserName(employeeInfos.get(0).getName());
        }

        //插入继承表
        if(CollectionUtils.isEmpty(transferInfo.getTransferCustomerResults())) {
            this.saveExternalContactTransfer2(transferInfo);
            return;
        }
        for(QyweixinTransferCustomerResult externalInfo : transferInfo.getTransferCustomerResults()) {
            QyweixinExternalContactTransferBo qyweixinExternalContactTransferBo = new QyweixinExternalContactTransferBo();
            qyweixinExternalContactTransferBo.setEa(transferInfo.getEa());
            qyweixinExternalContactTransferBo.setExternalUserId(externalInfo.getExternalUserId());
            qyweixinExternalContactTransferBo.setExternalApiName(transferInfo.getExternalApiName());
            qyweixinExternalContactTransferBo.setExternalNickname(transferInfo.getExternalNickname());
            qyweixinExternalContactTransferBo.setExternalUserName(transferInfo.getExternalUserName());
            qyweixinExternalContactTransferBo.setHandoverUserId(transferInfo.getHandoverQWUserId());
            qyweixinExternalContactTransferBo.setHandoverUserName(transferInfo.getHandoverUserName());
            qyweixinExternalContactTransferBo.setHandoverUserStatus(transferInfo.getHandoverUserStatus());
            qyweixinExternalContactTransferBo.setHandoverDeptId(transferInfo.getHandoverDeptId());
            qyweixinExternalContactTransferBo.setHandoverDeptName(transferInfo.getHandoverDeptName());
            qyweixinExternalContactTransferBo.setTakeoverUserId(transferInfo.getTakeoverQWUserId());
            qyweixinExternalContactTransferBo.setTakeoverUserName(transferInfo.getTakeoverUserName());
            qyweixinExternalContactTransferBo.setTakeoverDeptId(transferInfo.getTakeoverDeptId());
            qyweixinExternalContactTransferBo.setTakeoverDeptName(transferInfo.getTakeoverDeptName());
            if(externalInfo.getErrCode() != 0) {
                qyweixinExternalContactTransferBo.setSyncStatus(1);
                String errorMsg = this.queryErrorMsg(externalInfo.getErrCode());
                qyweixinExternalContactTransferBo.setErrorMsg(errorMsg);
                qyweixinExternalContactTransferBo.setTransferResult(0);
            } else {
                //默认为0
                qyweixinExternalContactTransferBo.setSyncStatus(0);
                qyweixinExternalContactTransferBo.setTransferResult(2);
            }
            log.info("QyweixinExternalManager.saveExternalContactTransfer,qyweixinExternalContactTransferBo={}.", qyweixinExternalContactTransferBo);
            qyweixinExternalContactTransferDao.insert(qyweixinExternalContactTransferBo);
        }
    }

//    public Map<String, QyweixinUserDetailInfoRsp> updateQyweixinUserInfo(QyweixinTransferCustomerInfo customerInfo, String corpId) {
//        log.info("QyweixinExternalManager.updateQyweixinUserInfo,customerInfo={},corpId={}.", customerInfo, corpId);
//        List<String> userList = Lists.newArrayList(customerInfo.getHandoverUserId(), customerInfo.getTakeoverUserId());
//        Map<String, QyweixinUserDetailInfoRsp> userDetailInfoRspMap = new HashMap<>();
//        for(String userId : userList) {
//            //查询员工详情
//            Result<QyweixinUserDetailInfoRsp> userInfoResult = qyWeixinManager.getUserInfoResult(repAppId, corpId, userId);
//            if(userInfoResult.isSuccess()) {
//                userDetailInfoRspMap.put(userId, userInfoResult.getData());
//            }
//        }
//        return userDetailInfoRspMap;
//    }

    public List<QyweixinExternalContactTransferBo> getHandoverAndTakeover() {
        SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd HH:00:00");
        Calendar c1 = Calendar.getInstance();
        c1.add(Calendar.DATE, -1);
        Date start = c1.getTime();
        String startTime= format1.format(start);//前一天

//        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        Calendar c = Calendar.getInstance();
//        c.add(Calendar.DATE, -1);
//        Date end = c.getTime();
//        String endTime= format.format(end);//前一天
        String endTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        log.info("QyweixinExternalManager.getHandoverAndTakeover,startTime={},endTime={}.", startTime, endTime);
        List<QyweixinExternalContactTransferBo> handoverAndTakeover = qyweixinExternalContactTransferDao.getHandoverAndTakeover(startTime, endTime);
        return handoverAndTakeover;
    }

    public QyweixinUserBo getUserInfo(QyweixinExternalContactTransferBo externalContactTransferBo) {
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> qyweixinCorpIDResult =
                qyweixinAccountBindService.fsEaToOutEaResult("qywx", externalContactTransferBo.getEa());
        if(!qyweixinCorpIDResult.isSuccess() || ObjectUtils.isEmpty(qyweixinCorpIDResult.getData())) {
            return null;
        }
        String corpId = qyweixinCorpIDResult.getData().getOutEa();
        //获取员工信息
        QyweixinUserBo qyweixinUserBo = new QyweixinUserBo();
        qyweixinUserBo.setOutEa(corpId);
        qyweixinUserBo.setUserId(externalContactTransferBo.getHandoverUserId());
        List<QyweixinUserBo> userBoList = qyweixinUserDao.findByEntity(qyweixinUserBo);
        if(CollectionUtils.isEmpty(userBoList)) {
            return null;
        }
        return userBoList.get(0);
    }

    public Integer updateTransferStatus(List<QyweixinCustomer> customerList, QyweixinExternalContactTransferBo externalContactTransferBo) {
        int sum = 0;
        for(QyweixinCustomer customer : customerList) {
            externalContactTransferBo.setExternalUserId(customer.getExternalUserId());
            externalContactTransferBo.setTransferResult(customer.getStatus());
            if(!"null".equals(String.valueOf(customer.getTakeoverTime()))) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                long lt = customer.getTakeoverTime();
                Date date = new Date(lt * 1000L);
                String res = simpleDateFormat.format(date);
                externalContactTransferBo.setTransferTime(Timestamp.valueOf(res));
            }
            int count = qyweixinExternalContactTransferDao.updateTransferStatus(externalContactTransferBo);
            sum += count;
        }
        return sum;
    }

    public Map<String, String> getTransferMappingFields() {
        Map<String, String> transferMappingFields = new HashMap<>();
        transferMappingFields.put("externalApiName", "对象名称");
        transferMappingFields.put("externalNickname", "姓名/公司名称");
        transferMappingFields.put("externalName", "客户名称");
        transferMappingFields.put("handoverUserName", "原负责人");
        transferMappingFields.put("handoverUserStatus", "员工状态");
        transferMappingFields.put("handoverDeptName", "原负责人所属部门");
        transferMappingFields.put("takeoverUserName", "接替成员");
        transferMappingFields.put("takeoverDeptName", "接替成员所属部门");
        transferMappingFields.put("syncStatus", "同步状态");
        transferMappingFields.put("errorMsg", "失败原因");
        transferMappingFields.put("transferResult", "接替状态");
        transferMappingFields.put("transferTime", "接替时间");
        transferMappingFields.put("startTime", "开始日期");
        transferMappingFields.put("endTime", "结束日期");
        transferMappingFields.put("createTime", "同步时间");
        return transferMappingFields;
    }

    public String queryErrorMsg(Integer code) {
        String errorMsg = "其他:" + code;
        if(QyweixinExternalTransferEnum.INVALID_USER_ID.getCode().equals(code)) {
            errorMsg = QyweixinExternalTransferEnum.INVALID_USER_ID.getName();
        }
        if(QyweixinExternalTransferEnum.INVALID_EXTERNAL_USER_ID.getCode().equals(code)) {
            errorMsg = QyweixinExternalTransferEnum.INVALID_EXTERNAL_USER_ID.getName();
        }
        if(QyweixinExternalTransferEnum.USER_NOT_RESIGNED.getCode().equals(code)) {
            errorMsg = QyweixinExternalTransferEnum.USER_NOT_RESIGNED.getName();
        }
        if(QyweixinExternalTransferEnum.USER_NOT_REAL_NAME.getCode().equals(code)) {
            errorMsg = QyweixinExternalTransferEnum.USER_NOT_REAL_NAME.getName();
        }
        if(QyweixinExternalTransferEnum.EXTERNAL_MAX.getCode().equals(code)) {
            errorMsg = QyweixinExternalTransferEnum.EXTERNAL_MAX.getName();
        }
        if(QyweixinExternalTransferEnum.EXTERNAL_TRANSFER_PROCESS.getCode().equals(code)) {
            errorMsg = QyweixinExternalTransferEnum.EXTERNAL_TRANSFER_PROCESS.getName();
        }
        if(QyweixinExternalTransferEnum.EXTERNAL_TRANSFER_FREQUENTLY.getCode().equals(code)) {
            errorMsg = QyweixinExternalTransferEnum.EXTERNAL_TRANSFER_FREQUENTLY.getName();
        }
        if(QyweixinExternalTransferEnum.EXTERNAL_TRANSFERRING.getCode().equals(code)) {
            errorMsg = QyweixinExternalTransferEnum.EXTERNAL_TRANSFERRING.getName();
        }
        if(QyweixinExternalTransferEnum.SAME_USER.getCode().equals(code)) {
            errorMsg = QyweixinExternalTransferEnum.SAME_USER.getName();
        }
        if(QyweixinExternalTransferEnum.USER_NOT_EXTERNAL.getCode().equals(code)) {
            errorMsg = QyweixinExternalTransferEnum.USER_NOT_EXTERNAL.getName();
        }
        if(QyweixinExternalTransferEnum.USER_NOT_EXIT.getCode().equals(code)) {
            errorMsg = QyweixinExternalTransferEnum.USER_NOT_EXIT.getName();
        }
        if(QyweixinExternalTransferEnum.USER_NOT_BIND.getCode().equals(code)) {
            errorMsg = QyweixinExternalTransferEnum.USER_NOT_BIND.getName();
        }
        if(QyweixinExternalTransferEnum.USER_EXTERNAL_NOT_RELATION.getCode().equals(code)) {
            errorMsg = QyweixinExternalTransferEnum.USER_EXTERNAL_NOT_RELATION.getName();
        }
        return errorMsg;
    }

    public Integer deleteExternalContactTransfers(List<Integer> deleteIds) {
        int sum = qyweixinExternalContactTransferDao.deleteByExternalUserIds(deleteIds);
        return sum;
    }

    public void saveExternalContactTransfer2(QyweixinTransferInfo transferInfo) {
        for(String userId : transferInfo.getExternalUserId()) {
            QyweixinExternalContactTransferBo qyweixinExternalContactTransferBo = new QyweixinExternalContactTransferBo();
            qyweixinExternalContactTransferBo.setEa(transferInfo.getEa());
            qyweixinExternalContactTransferBo.setExternalUserId(userId);
            qyweixinExternalContactTransferBo.setExternalApiName(transferInfo.getExternalApiName());
            qyweixinExternalContactTransferBo.setExternalNickname(transferInfo.getExternalNickname());
            qyweixinExternalContactTransferBo.setExternalUserName(transferInfo.getExternalUserName());
            qyweixinExternalContactTransferBo.setHandoverUserId(transferInfo.getHandoverQWUserId());
            qyweixinExternalContactTransferBo.setHandoverUserName(transferInfo.getHandoverUserName());
            qyweixinExternalContactTransferBo.setHandoverUserStatus(transferInfo.getHandoverUserStatus());
            qyweixinExternalContactTransferBo.setHandoverDeptId(transferInfo.getHandoverDeptId());
            qyweixinExternalContactTransferBo.setHandoverDeptName(transferInfo.getHandoverDeptName());
            qyweixinExternalContactTransferBo.setTakeoverUserId(transferInfo.getTakeoverQWUserId());
            qyweixinExternalContactTransferBo.setTakeoverUserName(transferInfo.getTakeoverUserName());
            qyweixinExternalContactTransferBo.setTakeoverDeptId(transferInfo.getTakeoverDeptId());
            qyweixinExternalContactTransferBo.setTakeoverDeptName(transferInfo.getTakeoverDeptName());
            if(transferInfo.getSyncStatus() != 0) {
                qyweixinExternalContactTransferBo.setSyncStatus(1);
                String errorMsg = this.queryErrorMsg(transferInfo.getSyncStatus());
                qyweixinExternalContactTransferBo.setErrorMsg(errorMsg);
                qyweixinExternalContactTransferBo.setTransferResult(0);
            } else {
                //默认为0
                qyweixinExternalContactTransferBo.setSyncStatus(0);
                qyweixinExternalContactTransferBo.setTransferResult(2);
            }
            log.info("QyweixinExternalManager.saveExternalContactTransfer,qyweixinExternalContactTransferBo={}.", qyweixinExternalContactTransferBo);
            qyweixinExternalContactTransferDao.insert(qyweixinExternalContactTransferBo);
        }

    }

    public QyweixinTransferInfo switchQyweixinTransferInfo(QyweixinTransferCustomerInfo customerInfo) {
        QyweixinTransferInfo transferInfo = new QyweixinTransferInfo();
        transferInfo.setEa(customerInfo.getEa());
        transferInfo.setExternalApiName(customerInfo.getExternalApiName());
        transferInfo.setExternalUserId(customerInfo.getExternalUserId());
        transferInfo.setExternalUserName(customerInfo.getExternalUserName());
        transferInfo.setExternalNickname(customerInfo.getExternalNickname());
        transferInfo.setHandoverUserId(customerInfo.getHandoverUserId());
        transferInfo.setHandoverDeptId(customerInfo.getHandoverDeptId());
        transferInfo.setHandoverDeptName(customerInfo.getHandoverDeptName());
        transferInfo.setTakeoverUserId(customerInfo.getTakeoverUserId());
        transferInfo.setTakeoverDeptId(customerInfo.getTakeoverDeptId());
        transferInfo.setTakeoverDeptName(customerInfo.getTakeoverDeptName());
        return transferInfo;
    }

    public void insertOrUpdateExternalContact(String corpId, String userId, List<QyweixinExternalContactRsp> contactList) {
        for(QyweixinExternalContactRsp rsp : contactList) {
            if(rsp.getErrcode()!=0) continue;
            QyweixinExternalContactBo contactBo = QyweixinExternalContactBo.builder()
                    .outEa(corpId)
                    .outUserId(userId)
                    .externalUserId(rsp.getExternal_contact().getExternal_userid())
                    .build();
            List<QyweixinExternalContactBo> entityList = qyweixinExternalContactDao.findByEntity(contactBo);
            //名字有可能会改变，不作为查询条件
            contactBo.setExternalName(rsp.getExternal_contact().getName());
            contactBo.setAvatar(rsp.getExternal_contact().getAvatar());
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(entityList)) {
                int insert = qyweixinExternalContactDao.insert(contactBo);
                log.info("QyweixinExternalManager.insertOrUpdateExternalContact,insert={}",insert);
                continue;
            }
            int update = qyweixinExternalContactDao.updateExternalNameAndAvatar(contactBo.getOutEa(),
                    contactBo.getOutUserId(),
                    contactBo.getExternalUserId(),
                    contactBo.getExternalName(),
                    contactBo.getAvatar());
            log.info("QyweixinExternalManager.insertOrUpdateExternalContact,update={}",update);
        }
    }
}
