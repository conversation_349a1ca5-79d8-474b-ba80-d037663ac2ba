package com.facishare.open.qywx.web.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.excel.FileManager;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinEmployeeInfo;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinExternalContactInfo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinIdToOpenidBo;
import com.facishare.open.qywx.web.mongo.document.MessageSaveDoc;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.save.arg.QueryMessageArg;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.constant.CreateObjectEnum;
import com.facishare.open.qywx.web.constant.FileEnum;
import com.facishare.open.qywx.web.db.dao.QyweixinIdToOpenidDao;
import com.facishare.open.qywx.save.enums.ErrorRefer;
import com.facishare.open.qywx.web.manager.*;
import com.facishare.open.qywx.web.mongo.dao.MessageSaveMongoDao;
import com.facishare.open.qywx.web.entity.entity.QywxMessagePo;
import com.facishare.open.qywx.save.result.FileMessageResult;
import com.facishare.open.qywx.save.result.Pager;
import com.facishare.open.qywx.save.result.Result;
import com.facishare.open.qywx.save.service.MessageGeneratingService;
import com.facishare.open.qywx.save.service.SaveMessageService;
import com.facishare.open.qywx.web.utils.PageUtils;
import com.facishare.open.qywx.save.vo.GenerateSettingVo;
import com.facishare.open.qywx.save.vo.QywxMessageVo;
import com.facishare.restful.common.StopWatch;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/3/26 16:45
 * @Version 1.0
 */
@Slf4j
@Service("saveMessageService")
public class SaveMessageServiceImpl implements SaveMessageService {

//    @Autowired
//    private SaveMessageDao saveMessageDao;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private QYWXFileManager qywxFileManager;
    @Autowired
    private MessageGeneratingService generatingService;
    @Autowired
    private SaveMessageService saveMessageService;
    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;
    @Autowired
    private CrmObjectSupportManager crmObjectSupportManager;
    @Autowired
    private MessageUploadManger messageUploadManger;
    @Autowired
    private MessageSaveMongoDao messageSaveMongoDao;
    @Resource
    private EIEAConverter eieaConverter;
    @Autowired
    private QyweixinIdToOpenidDao qyweixinIdToOpenidDao;

    private static String OUT_ACCOUNT = "OUT.WX.";

    private static String OUT_CONTACT_NAME = "wmwx1m";

    ExecutorService executorService = Executors.newFixedThreadPool(20);

    @Override
    public Result<Integer> batchSaveMessage(List<QywxMessageVo> messageList) {
//        List<QywxMessagePo> poList = new ArrayList<>();
//        messageList.stream().forEach(item -> {
//            QywxMessagePo po = new QywxMessagePo();
//            BeanUtils.copyProperties(item, po);
//            poList.add(po);
//        });
//        int count = saveMessageDao.batchSaveMessage(poList);
//        return new Result(count);
        return new Result();
    }

    @Override
    public Result<Integer> batchUpdateMessage(List<QywxMessageVo> messagePoList, String ea) {
//        List<QywxMessagePo> poList = new ArrayList<>();
//        messagePoList.stream().forEach(item -> {
//            QywxMessagePo po = new QywxMessagePo();
//            BeanUtils.copyProperties(item, po);
//            poList.add(po);
//        });
//        int count = saveMessageDao.batchUpdateMessageIds(poList, ea);
//        return new Result(count);
        return new Result();
    }

    @Override
    public Result<Integer> updateMessageById(QywxMessageVo messageVo) {
//        QywxMessagePo po = new QywxMessagePo();
//        BeanUtils.copyProperties(messageVo, po);
//        int row = saveMessageDao.updateMessage(po);
//        return new Result<>(row);
        return new Result();
    }

    @Override
    public Result<Integer> batchDeleteMessage(List<QywxMessageVo> messagePoList) {

        return null;
    }

    @Override
    public Result<QywxMessageVo> getMessageById(String ea, String messageId) {
//        QywxMessagePo qywxMessagePo = saveMessageDao.queryMessageById(ea, messageId);
//        QywxMessageVo vo = new QywxMessageVo();
//        BeanUtils.copyProperties(qywxMessagePo, vo);
//        return new Result(vo);
        return new Result();
    }

    @Override
    public Result<Pager<FileMessageResult>> conditionQueryMessage(QueryMessageArg arg) {
        //先把密文的账号转换成明文的账号再去查询
        List<QyweixinIdToOpenidBo> idToOpenidPos = new LinkedList<>();
        String senderId = null;
        String receiveId = null;
        Map<String, QyweixinIdToOpenidBo> plaintextIdToOpenidPoMap = new HashMap<>();
        if(StringUtils.isEmpty(arg.getRoomId())) {
            idToOpenidPos = qyweixinIdToOpenidDao.getByOpenIds(arg.getOutEa(), Lists.newArrayList(arg.getSenderIds(), arg.getReceiveIds()));
            if(CollectionUtils.isEmpty(idToOpenidPos) || idToOpenidPos.size() < 2) {
                return new Result<>(ErrorRefer.SWITCH_OPENID_ERROR);
            }
            Map<String, QyweixinIdToOpenidBo> idToOpenidPoMap = idToOpenidPos.stream().collect(Collectors.toMap(QyweixinIdToOpenidBo::getOpenid, Function.identity()));
            plaintextIdToOpenidPoMap = idToOpenidPos.stream().collect(Collectors.toMap(QyweixinIdToOpenidBo::getPlaintextId, Function.identity()));
            senderId = arg.getSenderIds();
            receiveId = arg.getReceiveIds();
            arg.setSenderIds(idToOpenidPoMap.get(arg.getSenderIds()).getPlaintextId());
            arg.setReceiveIds(idToOpenidPoMap.get(arg.getReceiveIds()).getPlaintextId());
        }
        StopWatch stopWatch = StopWatch.create("queryMessage ea" + arg.getFsEa());
        //先计算时间区间
        Integer ei = eieaConverter.enterpriseAccountToId(arg.getFsEa());
        Long nowTime = new Date().getTime();
        Long preMessageTime = getPreMessageTime(ei, arg);
        arg.setLastMessageTime(nowTime);
        arg.setPreMessageTime(preMessageTime);
        arg.setLimit(arg.getPageSize());
        stopWatch.lap("queryMessage..");
        //重新查询消息，返回带有nPATH的数据
        List<MessageSaveDoc> npathMessage = messageSaveMongoDao.pageByFilters(ei, arg);
        log.info("SaveMessageServiceImpl.conditionQueryMessage.npathMessage={}.", npathMessage);
        if(CollectionUtils.isEmpty(npathMessage)) {
            return new Result<>(PageUtils.transformData1(npathMessage, arg.getPageNum(), arg.getPageSize(), Boolean.FALSE));
        }
        stopWatch.lap("upload File time");
        if(StringUtils.isNotEmpty(arg.getRoomId())) {
            Set<String> fromUsers = npathMessage.stream().map(MessageSaveDoc::getFromUser).collect(Collectors.toSet());
            log.info("SaveMessageServiceImpl.conditionQueryMessage.fromUsers={}.", fromUsers);
            idToOpenidPos = qyweixinIdToOpenidDao.getPoByExternalIds(arg.getOutEa(), new LinkedList<>(fromUsers));
            plaintextIdToOpenidPoMap = idToOpenidPos.stream().collect(Collectors.toMap(QyweixinIdToOpenidBo::getPlaintextId, Function.identity()));
        }
        log.info("SaveMessageServiceImpl.conditionQueryMessage.idToOpenidPos={}.", idToOpenidPos);
        //客户消息有大文件，解密会造成服务器oom，为此有两个限制：
        //1、特殊客户不受限制
        //2、普通客户只能解密小于MESSAGE_UPLOAD_MAX的文件
        List<MessageSaveDoc> npathResult = new LinkedList<>();
        List<MessageSaveDoc> updateMessage = new LinkedList<>();
        for(MessageSaveDoc messageSaveDoc : npathMessage) {
            if(!"null".equals(String.valueOf(messageSaveDoc.getFileSize()))) {
                if(!ConfigCenter.MESSAGE_UPLOAD_EA.contains(arg.getFsEa()) &&
                        messageSaveDoc.getFileSize() > Long.parseLong(ConfigCenter.MESSAGE_UPLOAD_MAX)) {
                    log.info("SaveMessageServiceImpl.conditionQueryMessage.ea={},messageId={}.", arg.getFsEa(), messageSaveDoc.getMessageId());
                    continue;
                }
            }
            MessageSaveDoc updateMessageSaveDoc = new MessageSaveDoc();
            BeanUtils.copyProperties(messageSaveDoc, updateMessageSaveDoc);
            updateMessage.add(updateMessageSaveDoc);
            if(plaintextIdToOpenidPoMap.containsKey(messageSaveDoc.getFromUser())) {
                messageSaveDoc.setFromUser(plaintextIdToOpenidPoMap.get(messageSaveDoc.getFromUser()).getOpenid());
            }
            npathResult.add(messageSaveDoc);
        }
        //上传文件
        messageUploadManger.getMessageUpload(updateMessage, arg.getFsEa());
        //重新拼装成crm的消息体
        List<FileMessageResult> results = Lists.newArrayList();
        //获取自建应用密钥
        Result<GenerateSettingVo> generateSettingVoResult = generatingService.querySetting(arg.getFsEa(), null, arg.getOutEa());
        if (ObjectUtils.isEmpty(generateSettingVoResult)) return new Result<>(ErrorRefer.ENTERPRISE_NO_SETTING);
        List<String> openExternalIds = new LinkedList<>();
        if(StringUtils.isEmpty(arg.getRoomId())) {
            openExternalIds.add(senderId);
            openExternalIds.add(receiveId);
        } else {
            List<String> idToOpenids = idToOpenidPos.stream()
                    .map(QyweixinIdToOpenidBo::getOpenid)
                    .collect(Collectors.toList());
            log.info("SaveMessageServiceImpl.conditionQueryMessage.idToOpenids={}.", idToOpenids);
            openExternalIds.addAll(idToOpenids);
        }
        log.info("SaveMessageServiceImpl.conditionQueryMessage.openExternalIds={}.", openExternalIds);
        Map<String, String> nameMap = convertName(arg.getOutEa(), openExternalIds, arg.getFsEa(), generateSettingVoResult.getData().getCorpSecret());
        log.info("SaveMessageServiceImpl.conditionQueryMessage.nameMap={}.", nameMap);
        npathResult.stream().forEach(item -> {
            FileMessageResult fileMessageResult = new FileMessageResult();
            fileMessageResult.setMessageId(item.getMessageId());
            fileMessageResult.setSenderId(OUT_ACCOUNT.concat(item.getFromUser()));
            fileMessageResult.setTimeStamp(item.getMessageTime());
            fileMessageResult.setType(FileEnum.getType(item.getMessageType()).getFsMessageType());
            fileMessageResult.setContent(convertContent(item));
            com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountResult = qyweixinAccountBindService.outAccountToFsAccount(SourceTypeEnum.QYWX.getSourceType(),
                    arg.getFsEa(), StringUtils.EMPTY, item.getFromUser());
            if (CollectionUtils.isNotEmpty(accountResult.getData())) {
                fileMessageResult.setFsFullSenderId(accountResult.getData().get(0).getFsAccount());
            }
            //配置名字
            if(nameMap.containsKey(item.getFromUser())) {
                fileMessageResult.setSenderName(nameMap.get(item.getFromUser()));
            } else {
                fileMessageResult.setSenderName(item.getFromUser());
            }
            results.add(fileMessageResult);
        });
        stopWatch.log();
        //TODO 每次返回20条，可以在异步上传接下来的20条
        Boolean isHasNextPage = Boolean.TRUE;
        if(npathMessage.size() < arg.getLimit()) {
            isHasNextPage = Boolean.FALSE;
        } else {
            messageUploadManger.uploadNextMesssage(ei, arg);
        }
        return new Result<>(PageUtils.transformData1(results, arg.getPageNum(), arg.getPageSize(), isHasNextPage));
    }

    @Override
    public Result<List<FileMessageResult>> test (QueryMessageArg arg) {
//        List<QywxMessagePo> messageResult1 = saveMessageDao.queryMessageByNoRoom(arg);
//        List<QywxMessagePo> messageResult2 = saveMessageDao.queryMessageByRoom(arg, "test");
//        System.out.println(messageResult1);
//        System.out.println(messageResult2);
        return null;
    }

    @Override
    public String getName(String ea, String outEa, String userId) {
        //获取自建应用密钥
        Result<GenerateSettingVo> generateSettingVoResult = generatingService.querySetting(ea, null, outEa);
        if (ObjectUtils.isEmpty(generateSettingVoResult.getData())) return null;
        List<String> userIds = new LinkedList<>();
        userIds.add(userId);
        Map<String, String> nameMap = convertName(outEa, userIds, ea, generateSettingVoResult.getData().getCorpSecret());
        return nameMap.get(userId);
    }

    private List<QywxMessagePo> voToPo(List<QywxMessageVo> npathResult) {
        List<QywxMessagePo> poList = new ArrayList<>();
        npathResult.stream().forEach(item -> {
            QywxMessagePo po = new QywxMessagePo();
            BeanUtils.copyProperties(item, po);
            poList.add(po);
        });
        return poList;
    }

    @Override
    public Result<List<FileMessageResult>> getConditionQueryMessage(QueryMessageArg arg, List<QywxMessageVo> npathResult) {
        StopWatch stopWatch = StopWatch.create("queryMessage ea" + arg.getFsEa());
//        List<QywxMessagePo> messageResult = saveMessageDao.queryMessageByNoRoom(arg);
//
//        stopWatch.lap("queryMessage..");
//        //上传文件
//        getMessageUpload(messageResult, arg.getFsEa());
//        stopWatch.lap("upload File time");
        //重新查询消息，返回带有nPATH的数据
        //List<QywxMessagePo> npathResult = saveMessageDao.queryMessageByNoRoom(arg);
        //重新拼装成crm的消息体

//        List<QywxMessagePo> poList = new ArrayList<>();
//        npathResult.stream().forEach(item -> {
//            QywxMessagePo po = new QywxMessagePo();
//            BeanUtils.copyProperties(item, po);
//            poList.add(po);
//        });
        List<QywxMessagePo> poList = voToPo(npathResult);
        List<FileMessageResult> results = Lists.newArrayList();
        //获取自建应用密钥
        Result<GenerateSettingVo> generateSettingVoResult = generatingService.querySetting(arg.getFsEa(), null, arg.getOutEa());
        if (ObjectUtils.isEmpty(generateSettingVoResult)) return new Result<>(ErrorRefer.ENTERPRISE_NO_SETTING);
        Map<String, String> nameMap = null;//= convertName(arg.getOutEa(), arg.getSenderIds(), arg.getFsEa(), generateSettingVoResult.getData().getCorpSecret());
//        poList.stream().forEach(item -> {
//            FileMessageResult fileMessageResult = new FileMessageResult();
//            fileMessageResult.setMessageId(item.getMessageId());
//            String fromUser = null;
//            if(arg.getSenderIds().contains(item.getFromUser())) {
//                fromUser = item.getFromUser();
//            } else {
//                fromUser = item.getFromEncryptionUser();
//            }
//            fileMessageResult.setSenderId(OUT_ACCOUNT.concat(fromUser));
//            fileMessageResult.setTimeStamp(item.getMessageTime());
//            fileMessageResult.setType(FileEnum.getType(item.getMessageType()).getFsMessageType());
//            fileMessageResult.setContent(convertContent(item));
//            com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountResult = qyweixinAccountBindService.outAccountToFsAccount(SourceTypeEnum.QYWX.getSourceType(),
//                    arg.getFsEa(), StringUtils.EMPTY, fromUser);
//            if (CollectionUtils.isNotEmpty(accountResult.getData())) {
//                fileMessageResult.setFsFullSenderId(accountResult.getData().get(0).getFsAccount());
//            }
//            //配置名字
//            fileMessageResult.setSenderName(nameMap.get(fromUser));
//            results.add(fileMessageResult);
//        });
        stopWatch.log();
        return new Result<>(results);
    }

    @Override
    public Result<List<FileMessageResult>> getConditionQueryMessageByRoom(QueryMessageArg arg, String roomId) {
//        Integer ei = eieaConverter.enterpriseAccountToId(arg.getFsEa());
//        StopWatch stopWatch = StopWatch.create("queryMessage ea" + arg.getFsEa());
//        List<QywxMessagePo> messageResult = saveMessageDao.queryMessageByRoom(arg, roomId);
//
//        stopWatch.lap("queryMessage..");
//        //上传文件
//        //getMessageUpload(messageResult, arg.getFsEa(), Boolean.FALSE);
//        stopWatch.lap("upload File time");
//        //重新查询消息，返回带有nPATH的数据
//        List<MessageSaveDoc> npathResult = messageSaveMongoDao.pageByFilters(ei, arg);
//        //重新拼装成crm的消息体
//        List<FileMessageResult> results = Lists.newArrayList();
//        //获取自建应用密钥
//        Result<GenerateSettingVo> generateSettingVoResult = generatingService.querySetting(arg.getFsEa(), null, arg.getOutEa());
//        if (ObjectUtils.isEmpty(generateSettingVoResult)) return new Result<>(ErrorRefer.ENTERPRISE_NO_SETTING);
//        Map<String, String> nameMap = null;//convertName(arg.getOutEa(), arg.getSenderIds(), arg.getFsEa(), generateSettingVoResult.getData().getCorpSecret());
//        npathResult.stream().forEach(item -> {
//            FileMessageResult fileMessageResult = new FileMessageResult();
//            fileMessageResult.setMessageId(item.getMessageId());
//            fileMessageResult.setSenderId(OUT_ACCOUNT.concat(item.getFromUser()));
//            fileMessageResult.setTimeStamp(item.getMessageTime());
//            fileMessageResult.setType(FileEnum.getType(item.getMessageType()).getFsMessageType());
//            fileMessageResult.setContent(convertContent(item));
//            com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountResult = qyweixinAccountBindService.outAccountToFsAccount(SourceTypeEnum.QYWX.getSourceType(),
//                    arg.getFsEa(), StringUtils.EMPTY, item.getFromUser());
//            if (CollectionUtils.isNotEmpty(accountResult.getData())) {
//                fileMessageResult.setFsFullSenderId(accountResult.getData().get(0).getFsAccount());
//            }
//            //配置名字
//            fileMessageResult.setSenderName(nameMap.get(item.getFromUser()));
//            results.add(fileMessageResult);
//        });
//        stopWatch.log();
//        executorService.execute(new Runnable() {
//            @Override
//            public void run() {
//                log.info("async uploading file....");
//                List<QywxMessagePo> messageResult = saveMessageDao.queryMessageByRoom(arg, roomId);
//                //getMessageUpload(messageResult, arg.getFsEa(), Boolean.FALSE);
//            }
//        });
//        return new Result<>(results);
        return new Result();
    }

    private Map<String, String> convertName(String corpId, List<String> qywxIds, String ea, String appSecret) {
        log.info("convertName  qywxIds:{}", qywxIds);
        //外部联系人的前缀wo/wm，但是不靠谱。还是都请求先请求外部联系人接口有则返回，无则请求员工接口
        Map<String, String> nameMap = Maps.newHashMap();
        qywxIds.stream().forEach(item -> {
            String senderName = Strings.EMPTY;
            //外部联系人
            log.info("convertName  item:{}", item);
                com.facishare.open.qywx.accountsync.result.Result<QyweixinExternalContactInfo> qyweixinExternalContactInfoResult = qyweixinAccountSyncService.queryExternalContactsForSelf(corpId, appSecret, item);
            log.info("convertName  result:{}", qyweixinExternalContactInfoResult);
            if (ObjectUtils.isNotEmpty(qyweixinExternalContactInfoResult.getData())) {
                senderName = qyweixinExternalContactInfoResult.getData().getName();
            } else {
                com.facishare.open.qywx.accountsync.result.Result<List<QyweixinEmployeeInfo>> employeeInfoBatch = qyweixinAccountSyncService.getEmployeeInfoBatch(ea, ConfigCenter.QYWX_REP_APPID, Lists.newArrayList(item));
                if (CollectionUtils.isNotEmpty(employeeInfoBatch.getData())) {
                    senderName = employeeInfoBatch.getData().get(0).getName();
                }
            }
            nameMap.put(item, senderName);
        });
        return nameMap;
    }

    private Long getPreMessageTime(Integer ei, QueryMessageArg queryMessageArg) {
        Long preTime = null;
        if (ObjectUtils.isEmpty(queryMessageArg.getLabelValue())) return null;
        switch (queryMessageArg.getLabelValue()) {
            case 3:
                //最近50条
                queryMessageArg.setLimit(50);
                List<MessageSaveDoc> case3List = messageSaveMongoDao.queryPreMessageTime(ei, queryMessageArg);
                if (CollectionUtils.isNotEmpty(case3List)) preTime = case3List.get(case3List.size() - 1).getMessageTime();
                return preTime;
            case 4:
                //最近100条
                queryMessageArg.setLimit(100);
                List<MessageSaveDoc> case4List = messageSaveMongoDao.queryPreMessageTime(ei, queryMessageArg);
                if (CollectionUtils.isNotEmpty(case4List)) preTime = case4List.get(case4List.size() - 1).getMessageTime();
                return preTime;
            case 5:
                //最近1小时
                preTime = new Date().getTime() - 1000 * 60 * 60;
                return preTime;
            case 6:
                //最近4小时
                preTime = new Date().getTime() - 1000 * 60 * 60 * 4;
                return preTime;
            case 7:
                //最近1天
                preTime = new Date().getTime() - 1000 * 60 * 60 * 24;
                return preTime;
            case 8:
                //最近3天
                preTime = new Date().getTime() - 1000 * 60 * 60 * 24 * 3;
                return preTime;
            default:
                return null;
        }

    }


    @Override
    public Result<Long> queryLastSeq(String ea) {
//        Long seq = saveMessageDao.queryLastSeq(ea);
//        return new Result<>(seq);
        return new Result();
    }

    @Override
    public Integer getIsRoom(QueryMessageArg queryMessageArg) {
//        return saveMessageDao.getIsRoom(queryMessageArg);
        return 0;
    }

    @Override
    public List<String> getRoom(QueryMessageArg queryMessageArg) {
//        return saveMessageDao.getRoom(queryMessageArg);
        return Lists.newArrayList();
    }

    @Override
    public Result<Boolean> preserveWeChatConversionObj(Integer ei) {
        boolean success = crmObjectSupportManager.createDefineObject(ei, CreateObjectEnum.WechatConversionObj.name());
        log.info("SaveMessageServiceImpl.preserveWeChatConversionObj,ei={},success={}",ei,success);
        return new Result<>(success);
    }

    private String convertContent(MessageSaveDoc messageSaveDoc) {
        if (messageSaveDoc.getMessageType().equals(FileEnum.TEXT_TYPE.getMessageType()) ||
                messageSaveDoc.getMessageType().equals(FileEnum.EXTERNAL_REDPACKET.getMessageType()) ||
                messageSaveDoc.getMessageType().equals(FileEnum.LOCATION.getMessageType()) ||
                messageSaveDoc.getMessageType().equals(FileEnum.WEAPP.getMessageType())||
                messageSaveDoc.getMessageType().equals(FileEnum.LINK.getMessageType()))
            return messageSaveDoc.getContent();
        Map<String, Object> contentMap = Maps.newHashMap();
        contentMap.put("path", messageSaveDoc.getNpath());
        contentMap.put("fileSize", messageSaveDoc.getFileSize());
        contentMap.put("name", messageSaveDoc.getFileName());
        contentMap.put("fileExt", messageSaveDoc.getFileExt());
        return JSONObject.toJSON(contentMap).toString();
    }

    @Override
    public Result<Void> saveMessageToMongo(String ea, Long startSeq, Long endSeq) {
//        //查询mysql库有没有消息
//        //迁移
//        List<QywxMessagePo> messagePoList = saveMessageDao.queryMessageBySeq(ea, startSeq, endSeq);
//        if(CollectionUtils.isEmpty(messagePoList)) {
//            return new Result<>();
//        }
//        Integer ei = eieaConverter.enterpriseAccountToId(ea);
//        List<MessageSaveDoc> docList = new LinkedList<>();
//        for (QywxMessagePo po : messagePoList) {
//            MessageSaveDoc doc = new MessageSaveDoc();
//            doc.setId(ObjectId.get());
//            doc.setEi(ei);
//            doc.setFsEa(ea);
//            doc.setMessageId(po.getMessageId());
//            doc.setSeq(po.getSeq());
//            doc.setKeyVersion(po.getKeyVersion());
//            doc.setFromUser(po.getFromUser());
//            doc.setToList(po.getToList());
//            doc.setRoomId(po.getRoomId());
//            doc.setMessageTime(po.getMessageTime());
//            doc.setMessageType(po.getMessageType());
//            doc.setContent(po.getContent());
//            doc.setMd5sum(po.getMd5sum());
//            doc.setSdkFileId(po.getSdkFileId());
//            doc.setFileSize(po.getFileSize());
//            doc.setNpath(po.getNpath());
//            doc.setFileName(po.getFileName());
//            doc.setFileExt(po.getFileExt());
//            doc.setCreateTime(System.currentTimeMillis());
//            doc.setUpdateTime(System.currentTimeMillis());
//            docList.add(doc);
//            if(docList.size() > 200) {
//                BulkWriteResult result = messageSaveMongoDao.batchReplace(ei, docList);
//                log.info("AutoPullMessageServiceImpl.SaveMessageToMongo,ea={},result={}.", ea, result);
//                docList.clear();
//            }
//        }
//        if(com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(docList)) {
//            BulkWriteResult result = messageSaveMongoDao.batchReplace(ei, docList);
//            log.info("AutoPullMessageServiceImpl.SaveMessageToMongo,ea={},result={}.", ea, result);
//        }
        return new Result<>();
    }

    @Override
    public Result<Void> saveAllMessageToMongo() {
        //查询mysql库有没有消息
        //迁移
        Result<List<String>> listResult = generatingService.queryAllSetting();
        for(String ea : listResult.getData()) {
            Thread thread = new Thread(() -> saveAllMessageToMongo2(ea));
            thread.start();
        }
        return new Result<>();
    }

    public void saveAllMessageToMongo2(String ea) {
//        PageInfo<QywxMessagePo> pageInfo;
//        Integer ei = eieaConverter.enterpriseAccountToId(ea);
//        int num = 1;
//        int size = 1000;
//        int sum = 0;
//        do {
//            QueryMessageArg queryMessageArg=new QueryMessageArg();
//            queryMessageArg.setFsEa(ea);
//            queryMessageArg.setPageNum(num ++);
//            queryMessageArg.setPageSize(size);
//            queryMessageArg.setLimit(size);
//            PageHelper.startPage(queryMessageArg.getPageNum(),queryMessageArg.getPageSize());
//            List<QywxMessagePo> npathResult = saveMessageDao.queryAllMessage(queryMessageArg);
//            sum = sum + npathResult.size();
//            List<MessageSaveDoc> docList = new LinkedList<>();
//            for (QywxMessagePo po : npathResult) {
//                MessageSaveDoc doc = new MessageSaveDoc();
//                doc.setId(ObjectId.get());
//                doc.setEi(ei);
//                doc.setFsEa(ea);
//                doc.setMessageId(po.getMessageId());
//                doc.setSeq(po.getSeq());
//                doc.setKeyVersion(po.getKeyVersion());
//                doc.setFromUser(po.getFromUser());
//                doc.setToList(po.getToList());
//                doc.setRoomId(po.getRoomId());
//                doc.setMessageTime(po.getMessageTime());
//                doc.setMessageType(po.getMessageType());
//                doc.setContent(po.getContent());
//                doc.setMd5sum(po.getMd5sum());
//                doc.setSdkFileId(po.getSdkFileId());
//                doc.setFileSize(po.getFileSize());
//                doc.setNpath(po.getNpath());
//                doc.setFileName(po.getFileName());
//                doc.setFileExt(po.getFileExt());
//                doc.setCreateTime(System.currentTimeMillis());
//                doc.setUpdateTime(System.currentTimeMillis());
//                docList.add(doc);
//            }
//            if(com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(docList)) {
//                BulkWriteResult result = messageSaveMongoDao.batchReplace(ei, docList);
//                log.info("AutoPullMessageServiceImpl.SaveMessageToMongo,ea={},result={}.", ea, result);
//            }
//            pageInfo = new PageInfo<>(npathResult);
//        } while (pageInfo.isHasNextPage());
//        log.info("AutoPullMessageServiceImpl.SaveMessageToMongo,ea={},sum={}.", ea, sum);
        return;
    }

    @Override
    public Result<Void> saveMessageAllIds() {
        //查询mysql库有没有消息
        //迁移
        Result<List<String>> listResult = generatingService.queryAllSetting();
        for(String ea : listResult.getData()) {
            Thread thread = new Thread(() -> saveMessageAllIds2(ea));
            thread.start();
        }
        return new Result<>();
    }

    public void saveMessageAllIds2(String ea) {
//        PageInfo<QywxMessagePo> pageInfo;
//        Result<List<GenerateSettingVo>> settingResult = generatingService.queryByEaSetting(ea);
//        int num = 1;
//        int size = 1000;
//        int sum = 0;
//        do {
//            QueryMessageArg queryMessageArg=new QueryMessageArg();
//            queryMessageArg.setFsEa(ea);
//            queryMessageArg.setPageNum(num ++);
//            queryMessageArg.setPageSize(size);
//            queryMessageArg.setLimit(size);
//            PageHelper.startPage(queryMessageArg.getPageNum(),queryMessageArg.getPageSize());
//            List<QywxMessagePo> npathResult = saveMessageDao.queryAllMessage(queryMessageArg);
//            sum = sum + npathResult.size();
//            Set<QyweixinIdToOpenidBo> docList = new LinkedHashSet<>();
//            for (QywxMessagePo po : npathResult) {
//                if(po.getFromUser().equals(po.getFromEncryptionUser())) {
//                    continue;
//                }
//                List<QyweixinIdToOpenidBo> externalIds = qyweixinIdToOpenidDao.getPoByExternalIds(settingResult.getData().get(0).getQywxCorpId(), Lists.newArrayList(po.getFromUser()));
//                if(CollectionUtils.isNotEmpty(externalIds)) {
//                    continue;
//                }
//                QyweixinIdToOpenidBo qyweixinIdToOpenidPo = new QyweixinIdToOpenidBo();
//                qyweixinIdToOpenidPo.setCorpId(settingResult.getData().get(0).getQywxCorpId());
//                qyweixinIdToOpenidPo.setPlaintextId(po.getFromUser());
//                qyweixinIdToOpenidPo.setOpenid(po.getFromEncryptionUser());
//                if(po.getFromUser().length() == 32
//                        && (po.getFromUser().startsWith("wo") || po.getFromUser().startsWith("wm"))) {
//                    qyweixinIdToOpenidPo.setType(1);
//                } else {
//                    qyweixinIdToOpenidPo.setType(0);
//                }
//                docList.add(qyweixinIdToOpenidPo);
//            }
//            if(com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(docList)) {
//                int result = qyweixinIdToOpenidDao.batchSaveInfo(new LinkedList<>(docList));
//                log.info("AutoPullMessageServiceImpl.SaveMessageToMongo,ea={},result={}.", ea, result);
//            }
//            pageInfo = new PageInfo<>(npathResult);
//        } while (pageInfo.isHasNextPage());
//        log.info("AutoPullMessageServiceImpl.SaveMessageToMongo,ea={},sum={}.", ea, sum);
    }

    @Override
    public Result<Long> deleteMongoTableData(String ea) {
        DeleteResult deleteResult = messageSaveMongoDao.deleteTableData(eieaConverter.enterpriseAccountToId(ea), ea);
        long deleteCount = 0;
        if(ObjectUtils.isNotEmpty(deleteResult)) {
            deleteCount = deleteResult.getDeletedCount();
        }
        return new Result<>(deleteCount);
    }

    @Override
    public Result<QywxMessageVo> getMongoDataById(String ea, String id) {
        MessageSaveDoc messageSaveDoc = messageSaveMongoDao.getById(eieaConverter.enterpriseAccountToId(ea), id);
        QywxMessageVo vo = new QywxMessageVo();
        vo.setFsEa(messageSaveDoc.getFsEa());
        vo.setFromUser(messageSaveDoc.getFromUser());
        vo.setToList(messageSaveDoc.getToList());
        vo.setContent(messageSaveDoc.getContent());
        vo.setMessageId(messageSaveDoc.getMessageId());
        vo.setRoomId(messageSaveDoc.getRoomId());
        vo.setFileSize(messageSaveDoc.getFileSize());
        vo.setFileName(messageSaveDoc.getFileName());
        vo.setNpath(messageSaveDoc.getNpath());
        vo.setFileExt(messageSaveDoc.getFileExt());
        vo.setKeyVersion(messageSaveDoc.getKeyVersion());
        vo.setMd5sum(messageSaveDoc.getMd5sum());
        vo.setMessageTime(messageSaveDoc.getMessageTime());
        vo.setMessageType(messageSaveDoc.getMessageType());
        vo.setSeq(messageSaveDoc.getSeq());
        vo.setSdkFileId(messageSaveDoc.getSdkFileId());
        return new Result<>(vo);
    }

    @Override
    public Result<Integer> updateMongoData(String ea, String id, QywxMessageVo vo) {
        MessageSaveDoc messageSaveDoc1 = messageSaveMongoDao.getById(eieaConverter.enterpriseAccountToId(ea), id);
        MessageSaveDoc messageSaveDoc = new MessageSaveDoc();
        messageSaveDoc.setId(messageSaveDoc1.getId());
        messageSaveDoc.setFsEa(vo.getFsEa());
        messageSaveDoc.setFromUser(vo.getFromUser());
        messageSaveDoc.setToList(vo.getToList());
        messageSaveDoc.setContent(vo.getContent());
        messageSaveDoc.setMessageId(vo.getMessageId());
        messageSaveDoc.setRoomId(vo.getRoomId());
        messageSaveDoc.setFileSize(vo.getFileSize());
        messageSaveDoc.setFileName(vo.getFileName());
        messageSaveDoc.setNpath(vo.getNpath());
        messageSaveDoc.setFileExt(vo.getFileExt());
        messageSaveDoc.setKeyVersion(vo.getKeyVersion());
        messageSaveDoc.setMd5sum(vo.getMd5sum());
        messageSaveDoc.setMessageTime(vo.getMessageTime());
        messageSaveDoc.setMessageType(vo.getMessageType());
        messageSaveDoc.setSeq(vo.getSeq());
        messageSaveDoc.setSdkFileId(vo.getSdkFileId());
        BulkWriteResult bulkWriteResult = messageSaveMongoDao.batchUpdate(eieaConverter.enterpriseAccountToId(ea), Lists.newArrayList(messageSaveDoc));
        log.info("AutoPullMessageServiceImpl.updateMongoData,ea={},bulkWriteResult={}.", ea, bulkWriteResult);
        return new Result<>(bulkWriteResult.getModifiedCount());
    }

    @Override
    public Result<Integer> insertMongoData(String ea, QywxMessageVo vo) {
        MessageSaveDoc messageSaveDoc = new MessageSaveDoc();
        messageSaveDoc.setId(ObjectId.get());
        messageSaveDoc.setFsEa(vo.getFsEa());
        messageSaveDoc.setFromUser(vo.getFromUser());
        messageSaveDoc.setToList(vo.getToList());
        messageSaveDoc.setContent(vo.getContent());
        messageSaveDoc.setMessageId(vo.getMessageId());
        messageSaveDoc.setRoomId(vo.getRoomId());
        messageSaveDoc.setFileSize(vo.getFileSize());
        messageSaveDoc.setFileName(vo.getFileName());
        messageSaveDoc.setNpath(vo.getNpath());
        messageSaveDoc.setFileExt(vo.getFileExt());
        messageSaveDoc.setKeyVersion(vo.getKeyVersion());
        messageSaveDoc.setMd5sum(vo.getMd5sum());
        messageSaveDoc.setMessageTime(vo.getMessageTime());
        messageSaveDoc.setMessageType(vo.getMessageType());
        messageSaveDoc.setSeq(vo.getSeq());
        messageSaveDoc.setSdkFileId(vo.getSdkFileId());
        BulkWriteResult bulkWriteResult = messageSaveMongoDao.batchReplace(eieaConverter.enterpriseAccountToId(ea), Lists.newArrayList(messageSaveDoc));
        log.info("AutoPullMessageServiceImpl.insertMongoData,ea={},bulkWriteResult={}.", ea, bulkWriteResult);
        return new Result<>(bulkWriteResult.getModifiedCount());
    }

}
