package com.facishare.open.qywx.web.controller.outer;


import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.qywx.web.template.model.JsApiModel;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.result.EmployeeAccountMatchResult;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.model.AccountSyncConfigModel;
import com.facishare.open.qywx.accountinner.model.CorpInfoModel;
import com.facishare.open.qywx.accountinner.model.QyweixinExternalContactRsp;
import com.facishare.open.qywx.accountinner.service.ExternalContactsService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.model.OaConnectorDataModel;
import com.facishare.open.qywx.accountsync.model.UserMappingModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.web.arg.*;
import com.facishare.open.qywx.web.enums.UserContextSingleton;
import com.facishare.open.qywx.web.model.result.Result;
import com.facishare.open.qywx.messagesend.model.*;
import com.facishare.open.qywx.messagesend.service.QYWeixinMessageSendService;
import com.facishare.open.qywx.web.template.inner.jsapi.QyweixinJsApiTemplate;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

;

/**
 * Created by fengyh on 2020/6/15.
 *
 * 有些接口是dubbo的，通过这里有选择性的开放一些出去。
 */
@Deprecated
@RestController
@Slf4j
@RequestMapping("/open/qyweixin/restProxy")
public class ControllerRestProxy {

    @Autowired
    private  QyweixinAccountBindService  qyweixinAccountBindService;

    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;

    @Autowired
    private QYWeixinMessageSendService qyweixinMessageSendService;

    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;

    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;

    @Autowired
    private ControllerOpenQywxSaveMessage controllerOpenQywxSaveMessage;

    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayService;

    @Resource
    private ExternalContactsService externalContactsService;
    @Resource
    private QyweixinJsApiTemplate qyweixinJsApiTemplate;

    @ReloadableProperty("async.send.msg.to.qywx")
    private boolean asyncSendMsgToQywx;

    ThreadPoolExecutor executor = new ThreadPoolExecutor(50,200,60, TimeUnit.SECONDS,new LinkedBlockingQueue<>());

    private static String QYWXSOURCE = "qywx";

    @ReloadableProperty("repAppId")
    private String repAppId;


    @RequestMapping(value = "/test", method = RequestMethod.GET)
    @ResponseBody
    Result<String> test() {
        return new Result<>("hello, world. ");
    }


    @RequestMapping(value = "/outAccountToFsAccountBatch", method = RequestMethod.GET)
    @ResponseBody
    Result<Map<String,String>> outAccountToFsAccountBatch(@RequestParam("fsEnterpriseAccount") String fsEnterpriseAccount,
                                                          @RequestParam("appId") String appId,
                                                          @RequestParam("outAccountList") List<String> outAccountList) {
        return new Result<Map<String,String>>(qyweixinAccountBindService.outAccountToFsAccountBatch(QYWXSOURCE, fsEnterpriseAccount, appId, outAccountList));
    }

    @Deprecated
    @RequestMapping(value = "/fsAccountToOutAccountBatch", method = RequestMethod.GET)
    @ResponseBody
    Result<Map<String, String>>  fsAccountToOutAccountBatch(@RequestParam("appId")  String appId,
                                                            @RequestParam("fsAccountList")  List<String> fsAccountList) {
        return new Result<>(qyweixinAccountBindService.fsAccountToOutAccountBatch(QYWXSOURCE, appId, fsAccountList));
    }

    @RequestMapping(value = "/fsAccountToOutAccountBatch2", method = RequestMethod.GET)
    @ResponseBody
    Result<List<EmployeeAccountMatchResult>>  fsAccountToOutAccountBatch2(@RequestParam("appId")  String appId,
                                                                          @RequestParam("fsAccountList")  List<String> fsAccountList,
                                                                          @RequestParam(value = "outEa",required = false)  String outEa) {
        return new Result<>(qyweixinAccountBindService.fsAccountToOutAccountBatch2(QYWXSOURCE, appId, fsAccountList,outEa));
    }

    @RequestMapping(value = "/outEaToFsEa", method = RequestMethod.GET)
    @ResponseBody
    Result<String> outEaToFsEa(@RequestParam("outEa")  String outEa) {
         return new Result<>(qyweixinAccountBindService.outEaToFsEa(QYWXSOURCE, outEa,null));
    }

    @RequestMapping(value = "/fsEaToOutEa", method = RequestMethod.GET)
    @ResponseBody
    Result<String> fsEaToOutEa(@RequestParam("fsEa")  String fsEa) {
        return new Result<>(qyweixinAccountBindService.fsEaToOutEa(QYWXSOURCE, fsEa));
    }

    /**
     * 一个CRM对多个企微
     * @param fsEa
     * @return
     */
    @RequestMapping(value = "/fsEaToOutEaList", method = RequestMethod.GET)
    @ResponseBody
    Result<List<String>> fsEaToOutEaList(@RequestParam("fsEa")  String fsEa) {
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> result = qyweixinAccountBindService.fsEaToOutEaResultList(SourceTypeEnum.QYWX.getSourceType(),
                fsEa);
        List<String> outEaList = result.getData().stream()
                .map(QyweixinAccountEnterpriseMapping::getOutEa)
                .collect(Collectors.toList());
        return new Result<>(outEaList);
    }

    @RequestMapping(value = "/createJsapiSignature", method = RequestMethod.GET)
    @ResponseBody
    Result<QyweixinJsapiSignature> createJsapiSignature(@RequestParam("url")  String url,
                                                        @RequestParam("fsEnterpriseAccount")  String fsEnterpriseAccount,
                                                        @RequestParam("appId") String appId,
                                                        @RequestParam(value = "outEa", required = false) String outEa) {
        JsApiModel jsApiModel = new JsApiModel();
        jsApiModel.setAppId(appId);
        jsApiModel.setFsEa(fsEnterpriseAccount);
        jsApiModel.setOutEa(null);
        jsApiModel.setUrl(url);

        MethodContext context = MethodContext.newInstance(jsApiModel);
        qyweixinJsApiTemplate.getJsApiSignature(context);
        Result<QyweixinJsapiSignature> result = (Result<QyweixinJsapiSignature>) context.getResult().getData();
        log.info("QYWeixinManager.createJsapiSignature,result={}",result);
        return result;
    }

    @RequestMapping(value = "/getQyweixinMiniCurrentUser", method = RequestMethod.GET)
    @ResponseBody
    Result<QyweixinUserBind> getQyweixinMiniCurrentUser(@RequestParam Map paramMap) {
        return new Result<>(qyweixinAccountSyncService.getQyweixinMiniCurrentUser(paramMap));
    }

    @RequestMapping(value = "/getCorpBind", method = RequestMethod.GET)
    @ResponseBody
    Result<QyweixinCorpBindInfo> getCorpBind(@RequestParam("ea") String ea,
                                             @RequestParam("appId") String appId,
                                             @RequestParam(value = "outEa", required = false) String outEa) {
        return new Result<>(qyweixinAccountSyncService.getCorpBind2(ea, appId, outEa));
    }

    @RequestMapping(value = "/getQyweixinCurrentUser", method = RequestMethod.GET)
    @ResponseBody
    Result<QyweixinEmployeeInfo> getQyweixinCurrentUser(@RequestParam Map paramMap) {
        return new Result<>(qyweixinAccountSyncService.getQyweixinCurrentUser(paramMap));
    }

    /**
     * 判断企业微信应用是否安装的接口
     * @param fsEa
     * @param appId
     * @return
     */
    @RequestMapping(value = "/isAppInstalled")
    @ResponseBody
    Result<Boolean> isAppInstalled(@RequestParam("fsEa") String fsEa,
                                   @RequestParam("appId") String appId,
                                   @RequestParam(value = "outEa",required = false) String outEa) {
        com.facishare.open.qywx.accountsync.result.Result<QyweixinCorpBindInfo> result = qyweixinAccountSyncService.getCorpBind2(fsEa, appId, outEa);
        if(result.isSuccess()
                && result.getData()!=null
                && StringUtils.isNotEmpty(result.getData().getCorpId())
                && result.getData().getStatus()==0) {
            return new Result<>(true);
        }
        return new Result<>(false);
    }

    /**
     * 获取绑定的企业微信成员帐号
     * @param fsEa
     * @param fsUserId
     * @return
     */
    @RequestMapping(value = "/getQywxUserAccount")
    @ResponseBody
    Result<QyweixinAccountEmployeeMapping> getQywxUserAccount(@RequestParam("fsEa") String fsEa,
                                                              @RequestParam("fsUserId") String fsUserId,
                                                              @RequestParam(value = "outEa",required = false) String outEa) {
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEmployeeMapping> result
                = qyweixinAccountBindService.getQywxEmployeeMapping2(fsEa,fsUserId,outEa);
        if(result.getData()!=null) {
            return new Result<>(result.getData());
        }
        return new Result<QyweixinAccountEmployeeMapping>().addErrorCode("-1",null);
    }

    @RequestMapping(value = "/sendQyWeixinMsg", method = RequestMethod.POST)
    @ResponseBody
    Result<String> sendQyWeixinMsg(@RequestBody String str, @RequestParam("typeVal") String typeVal) {
        log.info("ControllerRestProxy.sendQyWeixinMsg,asyncSendMsgToQywx={},str={},typeVal={}",asyncSendMsgToQywx, str, typeVal);
        if(asyncSendMsgToQywx) {
            executor.submit(()->{
                sendQyWeixinMsg2(str, typeVal);
            });
            SendQyWeixinMsgRsp rsp = new SendQyWeixinMsgRsp();
            rsp.setErrcode("0");
            rsp.setErrmsg("success in async mode");

            log.info("ControllerRestProxy.sendQyWeixinMsg,res={},executor={}",rsp,executor.toString());
            return new Result(rsp);
        } else {
            log.info("ControllerRestProxy.sendQyWeixinMsg,sendQyWeixinMsg2=true");
            return sendQyWeixinMsg2(str, typeVal);
        }
    }

    /**
     * 发送消息到企微
     * @param body
     * @return
     */
    @RequestMapping(value = "/sendMsg2EnterpriseWechat", method = RequestMethod.POST)
    @ResponseBody
    Result<SendQyWeixinMsgRsp> sendMsg2EnterpriseWechat(@RequestBody SendMsg2EnterpriseWechatArg body) {
        if (body == null || body.getMsgType() == null || body.getMsgContent() == null || CollectionUtils.isEmpty(body.getOutUserIdList())) {
            return new Result<>(ErrorRefer.PARAM_ERROR.getCode(), ErrorRefer.PARAM_ERROR.getQywxCode(), null);
        }

        com.facishare.open.qywx.messagesend.result.Result<SendQyWeixinMsgRsp> result = qyweixinMessageSendService.sendMsg(body.getCorpId(),
                body.getAppId(),
                body.getMsgType(),
                body.getMsgContent(),
                body.getOutUserIdList());
        return new Result<>(result.getData());
    }

    Result<String> sendQyWeixinMsg2(@RequestBody String str, @RequestParam("typeVal") String typeVal) {
        try {
            SendQyWeixinMsgReq<?> req = null;

            switch (typeVal) {
                case "NewsMsgContent":
                    req = new Gson().fromJson(str, new TypeToken<SendQyWeixinMsgReq<NewsMsgContent>>(){}.getType());
                    break;
                case "TextCardMsgContent":
                    req = new Gson().fromJson(str, new TypeToken<SendQyWeixinMsgReq<TextCardMsgContent>>(){}.getType());
                    break;
                case  "TextMsgContent":
                    req = new Gson().fromJson(str, new TypeToken<SendQyWeixinMsgReq<TextMsgContent>>(){}.getType());
                    break;
                default:
                    return new  Result<>("", "not supported message type: "+typeVal, null);
            }
            com.facishare.open.qywx.messagesend.result.Result<SendQyWeixinMsgRsp> result =  qyweixinMessageSendService.sendQyWeixinMsg(req);
            if(result.isSuccess()) {
                return new Result(result.getData());
            }
            return new Result(result.getData());
        }catch (Exception e) {
            log.error("trace sendQyWeixinMsg get exception, ", e);
            return new Result<>("errorCode","get exception", null);
        }
    }

    @RequestMapping(value = "/getAccountSyncConfig",method =RequestMethod.GET)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<List<AccountSyncConfigModel>> getAccountSyncConfig(@RequestParam("fsEa") String fsEa,
                                                                                                                @RequestParam(value = "outEa",required = false) String outEa) {
        return controllerOpenQywxSaveMessage.getAccountSyncConfig(fsEa,outEa);
    }

    /**
     * 从外部联系人表查询isv外部联系人ID对应的代开发外部联系人ID，并返回
     * 营销通需求
     * @param isvExternalUserId
     * @return
     */
    @RequestMapping(value = "/getExternalUserId",method =RequestMethod.GET)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<String> getExternalUserId(@RequestParam("isvExternalUserId") String isvExternalUserId) {
        return qyweixinGatewayService.getExternalUserId(isvExternalUserId);
    }

    /**
     * 分配在职成员的客户
     * @param customerInfo
     * @return
     */
    @RequestMapping(value = "/externalContactTransferCustomer",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<List<QyweixinTransferCustomerResult>> externalContactTransferCustomer(@RequestBody QyweixinTransferCustomerInfo customerInfo) {
        return qyweixinAccountSyncService.externalContactTransferCustomer(customerInfo, false);
    }

    /**
     * 查询在职客户接替状态
     * @param customerStatusInfo
     * @return
     */
    @RequestMapping(value = "/externalContactTransferResult",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<QyweixinTransferCustomerStatusResult> externalContactTransferResult(@RequestBody QyweixinTransferCustomerStatusInfo customerStatusInfo) {
        return qyweixinAccountSyncService.externalContactTransferResult(customerStatusInfo, false);
    }

    /**
     * 分配在职成员的客户群
     * @param transferGroupChatInfo
     * @return
     */
    @RequestMapping(value = "/externalContactTransferGroupChat",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<List<QyweixinTransferGroupChatResult>> externalContactTransferGroupChat(@RequestBody QyweixinTransferGroupChatInfo transferGroupChatInfo) {
        return qyweixinAccountSyncService.externalContactTransferGroupChat(transferGroupChatInfo, false);
    }

    /**
     * 获取待分配的离职成员列表
     * @param externalContactInfo
     * @return
     */
    @RequestMapping(value = "/unassignedExternalContact",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<QyweixinUnassignedExternalContactResult> unassignedExternalContact(@RequestBody QyweixinUnassignedExternalContactInfo externalContactInfo) {
        return qyweixinAccountSyncService.unassignedExternalContact(externalContactInfo);
    }

    /**
     * 分配离职成员的客户
     * @param customerInfo
     * @return
     */
    @RequestMapping(value = "/resignedTransferCustomer",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<List<QyweixinTransferCustomerResult>> resignedTransferCustomer(@RequestBody QyweixinTransferCustomerInfo customerInfo) {
        return qyweixinAccountSyncService.externalContactTransferCustomer(customerInfo, true);
    }

    /**
     * 查询离职客户接替状态
     * @param customerStatusInfo
     * @return
     */
    @RequestMapping(value = "/resignedTransferResult",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<QyweixinTransferCustomerStatusResult> resignedTransferResult(@RequestBody QyweixinTransferCustomerStatusInfo customerStatusInfo) {
        return qyweixinAccountSyncService.externalContactTransferResult(customerStatusInfo, true);
    }

    /**
     * 分配离职成员的客户群
     * @param transferGroupChatInfo
     * @return
     */
    @RequestMapping(value = "/resignedTransferGroupChat",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<List<QyweixinTransferGroupChatResult>> resignedTransferGroupChat(@RequestBody QyweixinTransferGroupChatInfo transferGroupChatInfo) {
        return qyweixinAccountSyncService.externalContactTransferGroupChat(transferGroupChatInfo, true);
    }

    /**
     * TODO 需支持分页查询
     * 查询在职离职继承相关信息
     * @return
     */
    @RequestMapping(value = "/getExternalContactTransferInfo",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<List<QyweixinExternalContactTransferInfo>> getExternalContactTransferInfo(@RequestBody QyweixinQueryTransferInfo transferInfo) {
        String ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        transferInfo.setEa(ea);
        return qyweixinAccountSyncService.getExternalContactTransferInfo(transferInfo);
    }

    /**
     * 在职和离职继承的相关的字段
     */
    @RequestMapping(value = "/getTransferMappingFields",method =RequestMethod.GET)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<Map<String, String>> getTransferMappingFields() {
        return qyweixinAccountSyncService.getTransferMappingFields();
    }

    /**
     * 由深研判断员工在企业微信的状态，再去决定判断调用在职还是离职继承接口
     * 分配成员的客户
     * @param customerInfo
     * @return
     */
    @RequestMapping(value = "/transferExternalContact",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<List<QyweixinTransferCustomerResult>> transferExternalContact(@RequestBody QyweixinTransferCustomerInfo customerInfo) {
        return qyweixinAccountSyncService.transferExternalContact(customerInfo);
    }

    @RequestMapping(value = "/deleteExternalContactTransfers",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<Integer> deleteExternalContactTransfers(@RequestBody List<Integer> deleteIds) {
        return qyweixinAccountSyncService.deleteExternalContactTransfers(deleteIds);
    }

    /**
     * 获取客户群列表，不带群详情
     * @param groupChatInfo
     * @return
     */
    @RequestMapping(value = "/GetGroupChat",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<QyweixinGroupChatResult> GetGroupChat(@RequestBody QyweixinGroupChatInfo groupChatInfo) {
        return qyweixinGatewayService.getGroupChat(groupChatInfo);
    }

    /**
     * 获取客户群列表，带群详情
     * @param groupChatInfo
     * @return
     */
    @RequestMapping(value = "/GetGroupChatList",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<GroupChatListResult> GetGroupChatList(@RequestBody QyweixinGroupChatInfo groupChatInfo) {
        Integer userId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
        StringBuilder accountBuilder=new StringBuilder();
        String fsAccount = accountBuilder.append("E.").append(groupChatInfo.getEa()).append(".").append(userId).toString();
        List<String> accountList= Lists.newArrayList();
        accountList.add(fsAccount);
        String mainAppId = qyweixinGatewayInnerService.getMainAppId(groupChatInfo.getOutEa()).getData();
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountResult = qyweixinAccountBindService.fsAccountToOutAccountBatchV21(SourceTypeEnum.QYWX.getSourceType(),
                mainAppId,
                0,
                accountList,
                groupChatInfo.getOutEa());
        if(!accountResult.isSuccess() || CollectionUtils.isEmpty(accountResult.getData())) {
            return new com.facishare.open.qywx.accountsync.result.Result<>();
        }
        String outAccount = accountResult.getData().get(0).getOutAccount();
        groupChatInfo.setUserId(outAccount);
        return qyweixinGatewayService.getGroupChatList(groupChatInfo);
    }

    /**
     * 获取会话存档开启成员列表
     * @param fsEa
     * @return
     */
    @RequestMapping(value = "/getPermitUserList",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<GetPermitUserListResult> getPermitUserList(@RequestParam String fsEa,
                                                                                                        @RequestParam(value = "outEa",required = false) String outEa) {
        return qyweixinGatewayService.getPermitUserList2(fsEa, outEa);
    }

    /**
     * 查询对应roomid里面所有外企业的外部联系人的同意情况
     * @param fsEa
     * @param roomId
     * @return
     */
    @RequestMapping(value = "/checkRoomAgree",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<CheckRoomAgreeResult> checkRoomAgree(@RequestParam String fsEa,
                                                                                                  @RequestParam String roomId,
                                                                                                  @RequestParam(value = "outEa",required = false) String outEa) {
        return qyweixinGatewayService.checkRoomAgree2(fsEa,roomId,outEa);
    }

    /**
     * 批量获取企微用户详细信息
     */
    @RequestMapping(value = "/batchGetEmployeeInfo",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<List<QyweixinEmployeeInfo>> batchGetEmployeeInfo(@RequestBody BatchGetEmployeeInfoArg arg) {
        if(ObjectUtils.isEmpty(arg) || StringUtils.isEmpty(arg.getCorpId()) || CollectionUtils.isEmpty(arg.getUserIdList())) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return qyweixinAccountSyncService.batchGetEmployeeInfo(arg.getCorpId(), arg.getUserIdList());
    }

    /**
     * 查询对应roomid里面所有外企业的外部联系人的同意情况
     * @param arg
     * @return
     */
    @RequestMapping(value = "/checkSingleAgree",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<CheckSingleAgreeResult> checkSingleAgree(@RequestBody QyweixinCheckSingleAgreeArg arg) {
        return qyweixinGatewayService.checkSingleAgree(arg);
    }

    /**
     * 批量获取员工的绑定关系
     * @param fsEa
     * @param appId
     * @return
     */
    @RequestMapping(value = "/batchGetEmployeeMapping",method =RequestMethod.GET)
    @ResponseBody
    public com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> batchGetEmployeeMapping(@RequestParam("fsEa") String fsEa,
                                                                                                                           @RequestParam(value = "appId", required = false) String appId,
                                                                                                                           @RequestParam(value = "outEa", required = false) String outEa) {
        return qyweixinAccountBindService.batchGetEmployeeMapping2(fsEa, appId, outEa);
    }

    /**
     * 获取员工和客户的账号明文和密文关系
     */
    @RequestMapping(value = "/getOpenIds",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<List<QyweixinGetOpenIdResult>> getOpenIds(@RequestBody BatchGetOpenIdArg arg) {
        return qyweixinAccountSyncService.getOpenIds2(arg.getFsEa(), arg.getPlaintextIds(), arg.getOpenids(), arg.getOutEa());
    }

    /**
     * 获取企业信息
     * @param corpId
     * @param appId
     * @return
     */
    @RequestMapping(value = "/getCorpInfo",method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<CorpInfoModel> getCorpInfo(@RequestParam("corpId") String corpId,
                                                                                        @RequestParam(value = "appId", required = false) String appId) {
        return qyweixinGatewayService.getCorpInfo(corpId, appId);
    }

    /**
     * 获取企业用户信息
     * @param corpId
     * @param appId
     * @param userId
     * @return
     */
    @RequestMapping(value = "/getUserInfo",method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> getUserInfo(@RequestParam("corpId") String corpId,
                                                                                                     @RequestParam("appId") String appId,
                                                                                                     @RequestParam("userId") String userId) {
        return qyweixinGatewayService.getUserInfo(corpId, appId, userId);
    }

    /**
     * 根据手机号获取企微用户信息
     * @param corpId
     * @param appId
     * @param phone
     * @return
     */
    @RequestMapping(value = "/getUserInfoByPhone", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> getUserInfoByPhone(@RequestParam("corpId") String corpId,
                                                                                                            @RequestParam("appId") String appId,
                                                                                                            @RequestParam("phone") String phone) {
        return qyweixinGatewayService.getUserInfoByPhone(corpId, appId, phone);
    }

    /**
     * 监控上报
     * @param oaConnectorDataModel
     * @return
     */
    @RequestMapping(value = "/uploadOaConnectorData",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<Void> uploadOaConnectorData(@RequestBody OaConnectorDataModel oaConnectorDataModel) {
        return qyweixinAccountSyncService.uploadOaConnectorData(oaConnectorDataModel);
    }

    /**
     * 批量获取企微用户详细信息
     * 1、区别于/batchGetEmployeeInfo，需要返回每个员工的错误信息
     * 2、金典用于在职离职继承相关功能
     */
    @RequestMapping(value = "/batchGetEmployeeInfo2",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<List<QyweixinEmployeeInfoResult>> batchGetEmployeeInfo2(@RequestBody BatchGetEmployeeInfoArg2 arg) {
        if(ObjectUtils.isEmpty(arg) || StringUtils.isEmpty(arg.getFsEa()) || CollectionUtils.isEmpty(arg.getUserIds())) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return qyweixinAccountSyncService.batchGetEmployeeInfo21(arg.getFsEa(), arg.getUserIds(), arg.getOutEa());
    }

    /**
     * 获取部门信息
     */
    @RequestMapping(value = "/getDepartmentInfo",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<QyweixinDepartmentInfo> batchGetEmployeeInfo2(@RequestBody GetDepartmentInfoArg arg) {
        if(ObjectUtils.isEmpty(arg) || StringUtils.isAnyEmpty(arg.getFsEa(), arg.getDepartmentId())) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return qyweixinAccountSyncService.getDepartmentInfo(arg.getFsEa(), arg.getDepartmentId());
    }

    @RequestMapping(value = "/getExternalDetail",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<QyweixinExternalContactRsp> getExternalDetail(@RequestParam String fsEa,
                                                                                                           @RequestParam String externalUserId,
                                                                                                           @RequestParam(value = "outEa", required = false) String outEa) {
        if(StringUtils.isAnyEmpty(fsEa, externalUserId)) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return externalContactsService.getDetail2(fsEa,externalUserId,outEa);
    }

    @RequestMapping(value = "/getExternalChat",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<QyweixinGroupChatDetail> getExternalChat(@RequestParam String fsEa,
                                                                                                      @RequestParam String chatId,
                                                                                                      @RequestParam(value = "outEa", required = false) String outEa) {
        if(StringUtils.isAnyEmpty(fsEa, chatId)) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return qyweixinGatewayService.getGroupChatDetail2(fsEa, chatId, repAppId, outEa);
    }

    /**
     * 企业可通过此接口获取指定成员添加的客户列表。客户是指配置了客户联系功能的成员所添加的外部联系人。没有配置客户联系功能的成员，所添加的外部联系人将不会作为客户返回。
     * @param fsEa
     * @param fsUserId
     * @return
     */
    @RequestMapping(value = "/getExternalContactList",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<List<String>> getExternalContactList(@RequestParam String fsEa,
                                                                                                  @RequestParam String fsUserId,
                                                                                                  @RequestParam(value = "outEa", required = false) String outEa) {
        if(StringUtils.isAnyEmpty(fsEa, fsUserId)) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return externalContactsService.getExternalContactListEx(fsEa, fsUserId, outEa);
    }

    /**
     * 企业可通过此接口获取指定成员添加的客户列表。客户是指配置了客户联系功能的成员所添加的外部联系人。没有配置客户联系功能的成员，所添加的外部联系人将不会作为客户返回。
     * @param fsEa
     * @param outUserId
     * @return
     */
    @RequestMapping(value = "/getExternalContactList2",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<List<String>> getExternalContactList2(@RequestParam String fsEa,
                                                                                                   @RequestParam String outUserId,
                                                                                                   @RequestParam(value = "outEa", required = false) String outEa) {
        if(StringUtils.isAnyEmpty(fsEa, outUserId)) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return externalContactsService.getExternalContactList21(fsEa, outUserId, outEa);
    }

    //针对企微对external_userid的改造，通过fs_ea获取external_userid
    @RequestMapping(value = "/switchExternalContactEmployeeId",method =RequestMethod.POST)
    public com.facishare.open.qywx.accountsync.result.Result<List<QyweixinExternalUserIdInfo>> switchExternalContactEmployeeId(@RequestBody QyweixinOldExternalUserIdInfo qyweixinOldExternalUserIdInfos){
        if(StringUtils.isEmpty(qyweixinOldExternalUserIdInfos.getEa())
                || ObjectUtils.isEmpty(qyweixinOldExternalUserIdInfos.getExternalUserIds())) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinExternalUserIdInfo>> result = qyweixinAccountSyncService.switchExternalContactEmployeeId2(qyweixinOldExternalUserIdInfos.getEa(),
                qyweixinOldExternalUserIdInfos.getExternalUserIds(),
                qyweixinOldExternalUserIdInfos.getOutEa());
        if(result.getErrorCode().equals("s120050000")) {
            return new com.facishare.open.qywx.accountsync.result.Result<>(result.getData());
        } else {
            return new com.facishare.open.qywx.accountsync.result.Result<List<QyweixinExternalUserIdInfo>>().addError(ErrorRefer.INTERNAL_ERROR.getCode(), result.getErrorMsg(),null);
        }

    }

    //针对企微对userid的改造，通过fs_ea获取userid
    @RequestMapping(value = "/switchEmployeeId",method =RequestMethod.POST)
    public com.facishare.open.qywx.accountsync.result.Result<List<QyweixinOpenUserIdInfo>> switchEmployeeId(@RequestBody QyweixinOldUserIdInfo qyweixinOldUserIdInfos){
        if(StringUtils.isEmpty(qyweixinOldUserIdInfos.getEa())
                || ObjectUtils.isEmpty(qyweixinOldUserIdInfos.getUserIds())) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
       return qyweixinAccountSyncService.switchEmployeeId2(qyweixinOldUserIdInfos.getEa(),
               qyweixinOldUserIdInfos.getUserIds(),
               qyweixinOldUserIdInfos.getOutEa());
    }

    /**
     * 获取企微应用access token
     * 1、蒙牛客开
     */
    @RequestMapping(value = "/getAppAccessTokenByFsEa", method = RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<String> getAppAccessTokenByFsEa(@RequestParam String fsEa,
                                                                                             @RequestParam(required = false) String appId) {
        if (org.apache.commons.lang.StringUtils.isEmpty(fsEa)) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return qyweixinAccountSyncService.getAppAccessTokenByFsEa(fsEa);
    }

    /**
     * 获取企微应用access token
     * 1、蒙牛客开
     */
    @RequestMapping(value = "/getOutUserId", method = RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result<List<UserMappingModel>> getOutUserId(@RequestParam String fsEa,
                                                                                                  @RequestParam String fsUserId) {
        if (StringUtils.isEmpty(fsEa) || StringUtils.isEmpty(fsUserId)) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinAccountEmployeeMapping>> employeeMappingListFromFsUserId = qyweixinAccountBindInnerService.queryEmployeeMappingListFromFsUserId("qywx",
                fsEa,
                fsUserId);
        List<UserMappingModel> userMappingList = new ArrayList<>();
        for(QyweixinAccountEmployeeMapping mapping : employeeMappingListFromFsUserId.getData()) {
            UserMappingModel model = new UserMappingModel();
            model.setFsEa(fsEa);
            model.setFsUserId(fsUserId);
            model.setOutEa(mapping.getOutEa());
            model.setOutUserId(mapping.getOutAccount());

            userMappingList.add(model);
        }

        return new com.facishare.open.qywx.accountsync.result.Result<>(userMappingList);
    }
}
