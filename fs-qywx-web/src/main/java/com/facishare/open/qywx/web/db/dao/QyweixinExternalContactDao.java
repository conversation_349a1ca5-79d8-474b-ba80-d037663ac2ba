package com.facishare.open.qywx.web.db.dao;

import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinExternalContactBo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QyweixinExternalContactDao extends ICrudMapper<QyweixinExternalContactBo> {
    @Update("<script>" + "update qyweixin_external_contact set external_name = #{externalName}, avatar = #{avatar} where out_ea=#{outEa} and out_user_id=#{outUserId} and external_user_id=#{externalUserId}" +"</script>")
    int updateExternalNameAndAvatar(@Param("outEa") String outEa,
                                @Param("outUserId") String outUserId,
                                @Param("externalUserId") String externalUserId,
                                @Param("externalName") String externalName,
                                @Param("avatar") String avatar);

    @Select("<script>" + "select * from qyweixin_external_contact where out_ea=#{outEa} and (external_user_id=#{externalUserId} or isv_external_user_id=#{externalUserId})" +"</script>")
    List<QyweixinExternalContactBo> findByExternalUserId(@Param("outEa") String outEa,
                                                         @Param("externalUserId") String externalUserId);

    @Select("<script>" + "select * from qyweixin_external_contact where out_ea=#{outEa} " +
            "and (external_user_id in " +
            "<foreach collection='externalUserIds' item='item' open='(' close=')'  separator=','>" +
            "#{item} </foreach>" +
            "or isv_external_user_id in " +
            "<foreach collection='externalUserIds' item='item' open='(' close=')'  separator=','>" +
            "#{item} </foreach>" +
            ")" +"</script>")
    List<QyweixinExternalContactBo> findByExternalUserIds(@Param("outEa") String outEa,
                                                         @Param("externalUserIds") List<String> externalUserIds);

    @Delete("<script>" + "delete from qyweixin_external_contact where out_ea=#{outEa} or out_ea=#{openOutEa}" + "</script>")
    int deleteAllExternalUserIds(@Param("outEa") String outEa,
                                @Param("openOutEa") String openOutEa);

    @Select("<script>" + "select * from qyweixin_external_contact where " + "out_ea=#{corpId} and " +
            "out_user_id in "  +
            "<foreach collection='outUserIdList' item='userId' open='(' close=')'  separator=','>" +
            "#{userId} </foreach>" + "</script>")
    List<QyweixinExternalContactBo> selectBatchExternalContact(@Param("corpId") String corpId, @Param("outUserIdList") List<String> outUserIdList);

    @Delete("<script>" + "delete from qyweixin_external_contact where out_ea=#{outEa} and out_user_id=#{out_user_id} and external_user_id=#{external_user_id}" + "</script>")
    int deleteExternalUserIds(@Param("outEa") String outEa,
                              @Param("out_user_id") String out_user_id,
                              @Param("external_user_id") String external_user_id);
}
