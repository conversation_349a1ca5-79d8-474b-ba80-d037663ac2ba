package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

/**
 * Created by fengyh on 2018/4/20.
 */

@XStreamAlias("xml")
@Data
public class ContactsXml  {

    @XStreamAlias("SuiteId")
    private String SuiteId;

    @XStreamAlias("InfoType")
    private String InfoType;

    @XStreamAlias("TimeStamp")
    private String TimeStamp;

    @XStreamAlias("AuthCorpId")
    private String AuthCorpId;

    @XStreamAlias("ChangeType")
    private String ChangeType;

    @XStreamAlias("UserID")
    private String UserID;

    @XStreamAlias("NewUserID")
    private String NewUserID;

    @XStreamAlias("Id")
    private String Id;

    @XStreamAlias("TagId")
    private String TagId;

    @XStreamAlias("AddUserItems")
    private String AddUserItems;

    @XStreamAlias("DelUserItems")
    private String DelUserItems;

    @XStreamAlias("AddPartyItems")
    private String AddPartyItems;

    @XStreamAlias("DelPartyItems")
    private String DelPartyItems;

}
