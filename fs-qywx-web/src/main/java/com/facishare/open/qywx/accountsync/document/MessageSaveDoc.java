package com.facishare.open.qywx.accountsync.document;

import lombok.*;
import lombok.experimental.Accessors;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.util.List;

/**
 * 会话存档mongo文档模板
 * <AUTHOR>
 * @date 2022/12/28
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@Builder
@AllArgsConstructor
public class MessageSaveDoc implements Serializable {
    /**
     * 唯一标识
     */
    @BsonId
    private ObjectId id;
    private Integer ei;
    private String fsEa;
    private String messageId;
    private Long seq;//也是企业唯一
    private Integer keyVersion;//密钥版本。解密数据需要依赖对应的版本
    private String fromUser;//消息 发送方id
    //private String fromEncryptionUser;//发送方的密文id
    private List<String> toList;//消息接收方
    //private List<String> toEncryptionList;//消息接收方的密文id
    private String roomId;//群聊消息的群id
    private Long messageTime;//消息发送时间戳
    private String messageType;//消息类型
    private String content;//消息内容。存储文本
    private String md5sum;//对于图片，视频.音频的作为名字
    private String sdkFileId;//资源id，解密资源需要
    private Long fileSize;
    private String npath;
    private String fileName;//文件名
    private String fileExt;//拓展名
    private String messageData;//json数据，保存会话原始数据
    /**
     * 数据创建时间
     */
    private Long createTime;
    /**
     * 数据更新时间
     */
    private Long updateTime;
}
