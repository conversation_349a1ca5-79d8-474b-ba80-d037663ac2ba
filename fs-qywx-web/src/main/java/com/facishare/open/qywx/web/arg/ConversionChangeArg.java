package com.facishare.open.qywx.web.arg;

import com.facishare.common.fsi.ProtoBase;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ConversionChangeArg extends ProtoBase implements Serializable {

    private String ea;
    private List<ChangeModel> changeModelList;

    @Data
    public static class ChangeModel implements Serializable {
        private String roomId;
        private String fromCipherId;
        private String toCipherId;
        private String fromPlaintextId;
        private String toPlaintextId;
    }
}
