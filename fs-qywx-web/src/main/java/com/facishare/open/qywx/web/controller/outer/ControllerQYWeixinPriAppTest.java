package com.facishare.open.qywx.web.controller.outer;

import com.facishare.open.qywx.accountinner.model.QyweixinUserInfoRsp;
import com.facishare.open.qywx.web.model.qyweixin.QyweixinGetCorptokenRsp;
import com.facishare.open.qywx.web.utils.HttpHelper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by fengyh on 2018/4/25.
 * 企业微信自建应用
 */
@Deprecated
@Controller
@RequestMapping("/webhook/qyweixin/priapp")
@Slf4j
public class ControllerQYWeixinPriAppTest {

    private static String agentID="1000029";
    private static String internal_secret = "nn0Dj_oXNr6o7HhMR_ZiIuU5JunGpcWP0ETqtg3mZx0";
    private static String external_contact_secret="37quHt1l0Odb78GjjTm6_M90K8mEcwl3lXyKBv8TPu8";
    private static String corpID="wxbbe44d073ff6c715";
    private HttpHelper httpHelper = new HttpHelper();

    @RequestMapping(value = "/hello", method = RequestMethod.GET)
    public String hello() {
        log.info("trace get hello request from user");
        return "redirect: http://10.113.32.32:8010/userContext.html";
    }



    //换企业accesstoken
    private String getAccesstoken(Boolean userInternalSecret) {
        String curSecret;
        if(null == userInternalSecret || !userInternalSecret) {
            curSecret = external_contact_secret;
        } else {
            curSecret = internal_secret;
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=" + corpID + "&corpsecret=" + curSecret;

        try {
            log.info("trace getAccesstoken url : {} ", url);
            String httpRspStr = httpHelper.doGet(url);
            Gson gson = new Gson();
            QyweixinGetCorptokenRsp httpRsp = gson.fromJson(httpRspStr, new TypeToken<QyweixinGetCorptokenRsp>(){}.getType());
            return httpRsp.getAccess_token();
        } catch (Exception e) {
            log.error("trace getAccesstoken get exception,", e);
        }
        return null;
    }

    /**
     *外部联系人详情页内容填充
     * */
    @RequestMapping(value = "/getExternalContact")
    @ResponseBody
    private String getExternalContact(@RequestParam String code) {
        String accesstoken = getAccesstoken(null);
        String url ="https://qyapi.weixin.qq.com/cgi-bin/crm/get_external_contact?access_token=" + accesstoken+"&code="+code;
        try {
            log.info("trace getExternalContact url : {} ", url);
            String httpRsp = httpHelper.doGet(url);
            log.info("trace getExternalContact rsp: {} ", httpRsp);
            return httpRsp;
        }catch (Exception e) {
            log.error("trace getExternalContact get exception,", e);
        }
        return null;
    }
    /**
     *外部联系人详情页内容填充
     * */
    @RequestMapping(value = "/fillUserContext")
    public String fillUserContext(@RequestParam String code) {
        log.info("trace fillUserContext get code:{} ", code);
        return "redirect: http://10.113.32.32:8010/userContext.html?code="+code;
    }


    /**
     *外部联系人详情页内容填充
     * */
    @RequestMapping(value = "/getCurrentUser")
    @ResponseBody
    public String getCurrentUser(@RequestParam String code) {
        log.info("trace getCurrentUser get code:{} ", code);

        String accessToken = getAccesstoken(true);
        String url="https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token="+accessToken+"&code="+code;
        String httpRspStr;
        try {
            httpRspStr = httpHelper.doGet(url);
            log.info("trace getCurrentUser httpRsp:{} ", httpRspStr);

            Gson gson = new Gson();
            QyweixinUserInfoRsp qyweixinUserInfoRsp = gson.fromJson(httpRspStr, new TypeToken<QyweixinUserInfoRsp>(){}.getType());
            log.info("trace getCurrentUser get qyweixinUserInfoRsp:{}, ", qyweixinUserInfoRsp);

            String url2 = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserdetail?access_token="+accessToken;
            Map<String, String> form = new HashMap<String, String>();
            form.put("user_ticket", qyweixinUserInfoRsp.getUser_ticket());
            httpRspStr = httpHelper.postJsonData(url2, form);
            log.info("trace getCurrentUser get httpRspStr:{}, ", httpRspStr);
        } catch (Exception e) {
            log.info("trace getCurrentUser get exception, ", e);
            httpRspStr="getCurrentUser get exception, "+e.getMessage();
        }

        return "<html> 当前用户信息： "+httpRspStr + " </html>";
    }

    @RequestMapping(value = "/getExternalContactList")
    @ResponseBody
    public String getExternalContactList(@RequestParam(required = false) String userid) {
        if(StringUtils.isEmpty(userid)) {
            userid="13026658937"; //如果外部没有输入，就用hardy的账号。
        }
        String accessToken = getAccesstoken(null);
        String url="https://qyapi.weixin.qq.com/cgi-bin/crm/get_external_contact_list?access_token="+accessToken+"&userid="+userid;

        String httpRsp;
        try {
            httpRsp = httpHelper.doGet(url);
            log.info("trace getExternalContactList httpRsp:{} ", httpRsp);
        } catch (Exception e) {
            log.info("trace qyweixinDepartmentListRsp get exception, ", e);
            httpRsp="qyweixinDepartmentListRsp get exception, "+e.getMessage();
        }
        return httpRsp;
    }
}
