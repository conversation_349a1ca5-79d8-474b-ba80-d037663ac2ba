package com.facishare.open.qywx.web.job;

import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.qywx.save.service.AutoPullMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;


@JobHander(value = "AutoSynchronizationMessageJobHandler")
@Component
@Slf4j
public class AutoSynchronizationMessageJobHandler extends IJobHandler {
    @Autowired
    private AutoPullMessageService autoPullMessageService;


    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        try {
            String traceId = TraceUtils.getTraceId();
            if(StringUtils.isEmpty(traceId)) {
                TraceUtils.initTraceId(UUID.randomUUID()+"");
            }
            log.info("scan AutoEndAndTransfer start the job!");
            autoPullMessageService.getAutoSynchronizationMessage();
            log.info("scan AutoEndAndTransfer end the job!");
            return new ReturnT(ReturnT.SUCCESS_CODE, "定时任务调用成功");
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
    }
}