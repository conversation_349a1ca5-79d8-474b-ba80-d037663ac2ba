package com.facishare.open.qywx.web.template.outer.repMsg.event.app;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.app.AppEventTemplate;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountinner.model.QyweixinGetAuthInfoRsp;
import com.facishare.open.qywx.accountinner.service.ContactBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountsync.constant.EnterpriseWeChatEventTag;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpBindBo;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.service.ContactsService;
import com.facishare.open.qywx.accountsync.utils.xml.AuthCompanyXml;
import com.facishare.open.qywx.accountsync.utils.xml.QyweixinMsgBaseXml;
import com.facishare.open.qywx.accountsync.utils.xml.TagChangeEventXml;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.core.enums.QyweixinBindStatusEnum;
import com.facishare.open.qywx.web.db.dao.QyweixinCorpBindDao;
import com.facishare.open.qywx.web.manager.CorpManager;
import com.facishare.open.qywx.web.manager.EventCloudProxyManager;
import com.facishare.open.qywx.web.manager.QYWeixinManager;
import com.facishare.open.qywx.web.manager.SyncEventDataManger;
import com.facishare.open.qywx.web.model.QyweixinEventCreateAuthModel;
import com.facishare.open.qywx.web.model.qyweixin.QyweixinGetPermenantCodeRsp;
import com.facishare.open.qywx.web.mongo.document.SyncEventDataDoc;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mongodb.bulk.BulkWriteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 飞书应用事件处理器
 * <AUTHOR>
 * @date 2024-08-20
 */

@Component
@Slf4j
public class QyweixinRepAppEventTemplate extends AppEventTemplate {
    @Resource
    private QYWeixinManager qyWeixinManager;
    @Resource
    private EventCloudProxyManager eventCloudProxyManager;
    @Resource
    private CorpManager corpManager;
    @Resource
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Resource
    private SyncEventDataManger syncEventDataManger;
    @Resource
    private ContactBindInnerService contactBindInnerService;
    @Resource
    private QyweixinCorpBindDao qyweixinCorpBindDao;
    @Resource
    private ContactsService contactsService;

    @Override
    public void onAppOpen(MethodContext context) {
        LogUtils.info("QyweixinRepAppEventTemplate.onAppOpen,context={}",context);
        QyweixinEventCreateAuthModel createAuthModel = context.getData();

        String authCode = createAuthModel.getAuthCode();
        String fsEa = createAuthModel.getFsEa();
        String appId = createAuthModel.getAppId();

        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetPermenantCodeRsp> corpAuthInfoResult = qyWeixinManager.getPermanentCode(authCode, appId);
        log.info("QyweixinRepAppEventTemplate.getAuthInfo,corpAuthInfo,corpAuthInfo={}", corpAuthInfoResult);
        if (!corpAuthInfoResult.isSuccess()) {
            context.setResult(TemplateResult.newError(ErrorRefer.INTERNAL_ERROR.getQywxCode()));
            return;
        }
        QyweixinGetPermenantCodeRsp corpAuthInfo = corpAuthInfoResult.getData();
        //保存应用信息

        eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, appId, EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_EVENT_4_REP, corpAuthInfo.getAuth_corp_info().getCorpid(), "create_auth", new Gson().toJson(corpAuthInfo), null);

        corpManager.saveRepInfoTask(corpAuthInfo, appId);

        context.setResult(TemplateResult.newSuccess("success"));
    }

    @Override
    public void onAppStatusChange(MethodContext context) {
        Map<String, Object> data = context.getData();

        AuthCompanyXml authCompanyXml = (AuthCompanyXml) data.get("authCompanyXml");
        String infoType = (String) data.get("infoType");
        String plainMsg = (String) data.get("plainMsg");
        //这里的appId，上层写死为crm的appId
        String appId = (String) data.get("appId");

        saveAppEvent(authCompanyXml.getAuthCorpId(), authCompanyXml.getSuiteId(), infoType, plainMsg);
        changeCorpInfo(authCompanyXml.getAuthCorpId(), authCompanyXml.getSuiteId());
        //代开发应用可见范围变更事件，触发CRM应用可见范围变更逻辑
        updateCorpInfo(authCompanyXml.getAuthCorpId(),appId);
        context.setResult(TemplateResult.newSuccess("success"));
    }

    @Override
    public void onAppVisibleRangeChange(MethodContext context) {
        LogUtils.info("QyweixinRepAppEventTemplate.onAppVisibleRangeChange,context={}",context);

        Map<String, Object> dataMap = context.getData();
        QyweixinMsgBaseXml baseMsgXml = (QyweixinMsgBaseXml) dataMap.get("baseMsgXml");
        TagChangeEventXml tagChangeEventXml = (TagChangeEventXml) dataMap.get("tagChangeEventXml");
        String plainMsg = (String) dataMap.get("plainMsg");
        String infoType = (String) dataMap.get("infoType");

        contactsService.repVisibleRangeChange(baseMsgXml, tagChangeEventXml, infoType, plainMsg);

        context.setResult(TemplateResult.newSuccess("success"));
        LogUtils.info("QyweixinRepAppEventTemplate.onAppVisibleRangeChange,end,context={}",context);
    }

    @Override
    public void onAppStop(MethodContext context) {
        Map<String, Object> data = context.getData();
        String corpId = (String) data.get("corpId");
        String suiteId = (String) data.get("suiteId");
        cancelCorpAuth(corpId, suiteId);
        context.setResult(TemplateResult.newSuccess("success"));
    }

    private void saveAppEvent(String outEa, String appId, String infoType, String plainMsg) {
        log.info("QyweixinRepAppEventTemplate.saveAppEvent,outEa={}, appId={}, infoType={}, plainMsg={}", outEa, appId, infoType, plainMsg);
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(outEa);
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
            return;
        }
        boolean isHasManual = enterpriseMappingList.stream()
                .anyMatch(mapping -> mapping.getBindType() == 1);
        if(!isHasManual) {
            return;
        }

        SyncEventDataDoc dataDoc = new SyncEventDataDoc();
        dataDoc.setId(ObjectId.get());
        dataDoc.setAppId(appId);
        dataDoc.setOutEa(outEa);
        dataDoc.setEventType(infoType);
        dataDoc.setEvent(plainMsg);
        dataDoc.setStatus(0);
        dataDoc.setCreateTime(System.currentTimeMillis());
        dataDoc.setUpdateTime(System.currentTimeMillis());
        BulkWriteResult bulkWriteResult = syncEventDataManger.batchReplace(Lists.newArrayList(dataDoc));
        log.info("QyweixinRepAppEventTemplate.saveAppEvent,bulkWriteResult={}", bulkWriteResult);
    }

    private void changeCorpInfo(String corpId, String appId) {
        //更新企业信息
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetAuthInfoRsp> authInfoRspResult = qyWeixinManager.getCorpInfo(corpId, appId);
        if(!authInfoRspResult.isSuccess() || ObjectUtils.isEmpty(authInfoRspResult.getData())) {
            //目前发现已过期的应用也会收到change_auth事件，这时候调用获取企业信息接口就会返回错误，这里做特殊处理
            log.info("QyweixinRepAppEventTemplate.updateCorpInfo,getCorpInfo exception = {}",authInfoRspResult.getMsg());
            log.info("QyweixinRepAppEventTemplate.updateCorpInfo,调用企业微信获取企业信息接口失败");
            return ;
        }
        QyweixinGetAuthInfoRsp authInfoRsp = authInfoRspResult.getData();
        //迁移企业账号信息
//        corpId = this.refreshEnterpriseAccount(authInfoRsp.getAuth_corp_info().getCorpid(), appId, String.valueOf(authInfoRsp.getAuth_info().getAgent().get(0).getAgentid()));

        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
            return;
        }
        QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingList.get(0);
        String fsEa = enterpriseMapping.getFsEa();
        log.info("QyweixinRepAppEventTemplate.changeCorpInfo,enterpriseMappingList={}", enterpriseMappingList);
        //异步自动绑定账号
        Map<String, Map<String, String>> autoBindEmpEnterpriseMap = new Gson().fromJson(ConfigCenter.EXTATTR_AUTO_BIND_EMP_ENTERPRISE, new TypeToken<Map<String, Map<String, String>>>() {
        }.getType());
        if(autoBindEmpEnterpriseMap.containsKey(fsEa)) {
            log.info("QyweixinRepAppEventTemplate.changeCorpInfo,autoBindEmpEnterpriseMap,fsEa={}",fsEa);
            contactBindInnerService.autoBindEmpAccount(fsEa, corpId);
        } else if(ConfigCenter.AUTO_BIND_ACCOUNT_EA.contains(fsEa)) {
            log.info("QyweixinRepAppEventTemplate.changeCorpInfo,AUTO_BIND_ACCOUNT_EA,fsEa={}",fsEa);
            contactBindInnerService.changAuthEventToBindAccount(fsEa, corpId);
        }
    }

    public void updateCorpInfo(String corpId, String appId) {
        log.info("QyweixinRepAppEventTemplate.updateCorpInfo,corpId={},appId={}",corpId,appId);
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
           return;
        }
        //更新企业信息
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetAuthInfoRsp> authInfoRspResult = qyWeixinManager.getCorpInfo(corpId, appId);
        if(!authInfoRspResult.isSuccess() || ObjectUtils.isEmpty(authInfoRspResult.getData())) {
            //目前发现已过期的应用也会收到change_auth事件，这时候调用获取企业信息接口就会返回错误，这里做特殊处理
            log.info("QyweixinRepAppEventTemplate.updateCorpInfo,getCorpInfo exception = {}",authInfoRspResult.getMsg());
            log.info("QyweixinRepAppEventTemplate.updateCorpInfo,调用企业微信获取企业信息接口失败");
            return ;
        }
        //保存应用信息
        corpManager.updateCorpInfo(authInfoRspResult.getData(), corpId, appId);
    }

    public void cancelCorpAuth(String corpId, String appId) {
        //保存绑定关系或更新
        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, appId);
        log.info("QyweixinRepAppEventTemplate.cancelCorpAuth,qyweixinCorpBindBo={}",qyweixinCorpBindBo);

        qyweixinCorpBindBo.setStatus(QyweixinBindStatusEnum.DELETE_BIND.getCode());
        int count = qyweixinCorpBindDao.update(qyweixinCorpBindBo);
        log.info("QyweixinRepAppEventTemplate.cancelCorpAuth,count={}",count);
        //使用代开发授权会话留存，删除代开发应用的时候，会话留存的corpSecret和agentId也要删除
        corpManager.updateCorpMessageGenerating(corpId, qyweixinCorpBindBo);
    }
}
