package com.facishare.open.qywx.web.template;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.OuterEventHandlerTemplate;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.qywx.accountsync.constant.EnterpriseWeChatEventTag;
import com.facishare.open.qywx.accountsync.model.EnterpriseWeChatEventProto;
import com.facishare.open.qywx.accountsync.model.qyweixin.AppConfigInfo;
import com.facishare.open.qywx.accountsync.utils.QYWxCryptHelper;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.eventHandler.ThirdCmdEventHandler;
import com.facishare.open.qywx.web.manager.EventCloudProxyManager;
import com.facishare.open.qywx.web.manager.QYWeixinManager;
import com.facishare.open.qywx.accountsync.utils.xml.*;
import com.facishare.open.qywx.web.mq.sender.MQSender;
import com.facishare.open.qywx.web.template.outer.recvCmd.event.ticket.QyweixinTicketEventHandlerTemplate;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.facishare.open.qywx.web.utils.SecurityUtil;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.codec.binary.Base64;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 企微第三方外部事件处理器模板实现类
 * <AUTHOR>
 * @date 2024-11-20
 */

@Slf4j
@Component
public class QyweixinOuterRecvCmdEventHandlerTemplate extends OuterEventHandlerTemplate {
    @ReloadableProperty("appMetaInfoStr2")
    private String appMetaInfoCmsStr;

    @Resource
    private ThirdCmdEventHandler thirdCmdEventHandler;

    @Resource
    private MQSender mqSender;

    @Autowired
    private QyweixinTicketEventHandlerTemplate qyweixinTicketEventHandlerTemplate;

    @Autowired
    private EventCloudProxyManager eventCloudProxyManager;



    Map<String, AppConfigInfo> appMetaInfo = Maps.newHashMap();
    private static final List<String> proxyCloudEvent = Lists.newArrayList("cancel_auth", "change_contact", "change_external_contact");

    public List<String> supportedCmdEvent = Lists.newArrayList(
            "suite_ticket",
            "create_auth","change_auth","cancel_auth","change_contact",
            "open_order","change_order","pay_for_app_success","refund",
            "reset_permanent_code","agree_external_userid_migration",
            "change_external_contact","change_external_chat","enter_agent",
            "share_chain_change"
    );

    @PostConstruct
    public void initAppMeta() {
        //在cms上的格式配置格式 appMetaInfoStr=source,appid,appsecret   保存的时候key使用source_appid
        ArrayList<AppConfigInfo> appMetaComponent = new Gson().fromJson(appMetaInfoCmsStr, new TypeToken<ArrayList<AppConfigInfo>>() {
        }.getType());
        log.info("QyweixinOuterRecvCmdEventHandlerTemplate.initAppMeta, appMetaInfoStr={}, appMetaComponent={} ", appMetaInfoCmsStr, appMetaComponent);
        appMetaComponent.stream().forEach(v -> {
            v.setEncodingAESKeyBase64(Base64.decodeBase64(v.getEncodingAESKey() + "="));
            v.setToken(SecurityUtil.decryptStr(v.getToken()));
            v.setSecret(SecurityUtil.decryptStr(v.getSecret()));
            appMetaInfo.put(v.getAppId(), v);
        });
    }

    @Override
    public void onEventDecode(MethodContext context) {
        log.info("QyweixinOuterRecvCmdEventHandlerTemplate.onEventDecode,context={}",context);

        EnterpriseWeChatEventProto eventProto = context.getData();

        //校验
        if (StringUtils.isNotEmpty(eventProto.getEchoStr())) {
            if (StringUtils.isEmpty(eventProto.getAppId())) {
                eventProto.setAppId(ConfigCenter.crmAppId);
            }
            String verified = verifyURL(eventProto.getSignature(), eventProto.getTimestamp(), eventProto.getNonce(), eventProto.getEchoStr(), eventProto.getAppId());
            //这里比较特殊，只是检验地址配置是否正确，返回错误就行
            context.setResult(TemplateResult.newErrorData(verified));
            return;
        }

        //解密
        String plainMsg;
        try {
            plainMsg = thirdCmdEventHandler.decryptMsg(eventProto.getSignature(),
                    eventProto.getTimestamp(),
                    eventProto.getNonce(),
                    eventProto.getData(),
                    eventProto.getAppId());
            log.info("QyweixinOuterRecvCmdEventHandlerTemplate.onMsgEvent,plainMsg={}", plainMsg);
        } catch (Exception e) {
            log.info("QyweixinOuterRecvCmdEventHandlerTemplate.onMsgEvent,exception={}", e.getMessage(),e);
            context.setResult(TemplateResult.newErrorData("success"));
            return;
        }
        //准备下一步需要的context
        context.setData(plainMsg);
        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void onEventFilter(MethodContext context) {
        log.info("QyweixinOuterRecvCmdEventHandlerTemplate.onEventFilter,context={}",context);
        String plainMsg = context.getData();

        QyweixinMsgBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QyweixinMsgBaseXml.class);
        log.info("QyweixinOuterRecvCmdEventHandlerTemplate.handle,baseMsgXml={}",baseMsgXml);
        String infoType = baseMsgXml.getInfoType();
        log.info("QyweixinOuterRecvCmdEventHandlerTemplate.handle,infoType={}",infoType);

        if(!supportedCmdEvent.contains(infoType)) {
            log.info("QyweixinOuterRecvCmdEventHandlerTemplate.handle,not support event,event={}", infoType);
            context.setResult(TemplateResult.newErrorData("success"));
            return;
        }
        //去掉不支持的外部联系人事件
        List<String> externalContactChangeTypeList = Lists.newArrayList("add_external_contact","edit_external_contact","del_external_contact","del_follow_user");
        if(StringUtils.equalsIgnoreCase("change_external_contact",infoType)
                && !externalContactChangeTypeList.contains(baseMsgXml.getChangeType())) {
            context.setResult(TemplateResult.newErrorData("success"));
            return;
        }

        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void onEventHandle(MethodContext context) {
        log.info("QyweixinOuterRecvCmdEventHandlerTemplate.onEventHandle,context={}",context);
        String plainMsg = context.getData();

        QyweixinMsgBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QyweixinMsgBaseXml.class);
        log.info("QyweixinOuterRecvCmdEventHandlerTemplate.recvMsgEvent,baseMsgXml={}", baseMsgXml);
        String infoType = baseMsgXml.getInfoType();
        log.info("QyweixinOuterRecvCmdEventHandlerTemplate.recvMsgEvent,infoType={}", infoType);

        //紧急ticket事件，立即处理
        if ("suite_ticket".equalsIgnoreCase(infoType)) {
            //接收推送ticket
            //SuiteAuthXml suiteAuthXml = XmlParser.fromXml(plainMsg, SuiteAuthXml.class);
            SuiteAuthXml suiteAuthXml = XStreamUtils.parseXml(plainMsg, SuiteAuthXml.class);
            log.info("QyweixinOuterRecvCmdEventHandlerTemplate.recvMsgEvent,infoType={},suiteAuthXml={}", infoType,suiteAuthXml);
            qyweixinTicketEventHandlerTemplate.execute(suiteAuthXml);
//            qyWeixinManager.saveQyweixinTicketToken(suiteAuthXml, Boolean.TRUE);
        } else {
            //判断是否需要跨云
            if(proxyCloudEvent.contains(infoType)) {
                eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, baseMsgXml.getSuiteId(), EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_CMD_EVENT_4_THIRD, baseMsgXml.getAuthCorpId(), infoType, plainMsg, null);
            }

            //如果是非紧急事件，发送MQ，等待后面消费
            mqSender.sendEnterpriseWeChatMQ(EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_CMD_EVENT_4_THIRD,plainMsg);
        }

        context.setResult(TemplateResult.newSuccess("success"));
    }

    private String verifyURL(String msgSignature, String timeStamp, String nonce, String echostr, String appID) {
        String result = null;
        try {
            result = QYWxCryptHelper.VerifyURL(appMetaInfo.get(appID).getToken(), appMetaInfo.get(appID).getEncodingAESKeyBase64(), msgSignature, timeStamp, nonce, echostr);
            log.info("QyweixinOuterRecvCmdEventHandlerTemplate.verifyURL,result={}", result);
        } catch (Exception e) {
            log.error("QyweixinOuterRecvCmdEventHandlerTemplate.verifyURL,exception", e);
        }
        return result;
    }
}
