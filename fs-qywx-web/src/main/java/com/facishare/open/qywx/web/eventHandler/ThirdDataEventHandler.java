package com.facishare.open.qywx.web.eventHandler;

import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.utils.xml.QYWeiXinDataEventBaseXml;
import com.facishare.open.qywx.accountsync.utils.xml.XStreamUtils;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.manager.OANewBaseManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 企业微信第三方应用事件处理器
 * <AUTHOR>
 * @date 2022/12/20
 */
@Slf4j
@Component
public class ThirdDataEventHandler extends EnterpriseWeChatEventHandler {
    @Resource
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;
    @Resource
    private OANewBaseManager oaNewBaseManager;

    public static List<String> supportedDataEvent = Lists.newArrayList(
            "subscribe","unsubscribe"
    );

    @Override
    public void handle(String plainMsg) {
        super.handle(plainMsg);
        log.info("ThirdDataEventHandler.handle,plainMsg2={}", plainMsg);

        //QYWeiXinDataEventBaseXml baseMsgXml = XmlParser.fromXml(plainMsg, QYWeiXinDataEventBaseXml.class);
        QYWeiXinDataEventBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QYWeiXinDataEventBaseXml.class);
        log.info("ThirdDataEventHandler.handle,baseMsgXml={}",baseMsgXml);

        //是否执行在新基座
        if(!oaNewBaseManager.canRunInNewBase(baseMsgXml.getToUserName(), plainMsg)) {
            log.info("RepEventHandler.handle,runInNewBase,baseMsgXml.getToUserName()={},plainMsg={}", baseMsgXml.getToUserName(), plainMsg);
            return;
        }

        qyweixinGatewayInnerService.recvDataEvent(plainMsg, ConfigCenter.crmAppId);
    }
}
