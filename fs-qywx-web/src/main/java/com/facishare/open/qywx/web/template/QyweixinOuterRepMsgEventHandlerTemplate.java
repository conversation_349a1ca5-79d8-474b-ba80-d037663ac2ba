package com.facishare.open.qywx.web.template;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.OuterEventHandlerTemplate;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.qywx.accountsync.constant.EnterpriseWeChatEventTag;
import com.facishare.open.qywx.accountsync.model.EnterpriseWeChatEventProto;
import com.facishare.open.qywx.accountsync.model.qyweixin.AppConfigInfo;
import com.facishare.open.qywx.accountsync.utils.QYWxCryptHelper;
import com.facishare.open.qywx.accountsync.utils.xml.*;
import com.facishare.open.qywx.web.eventHandler.RepEventHandler;
import com.facishare.open.qywx.web.manager.EventCloudProxyManager;
import com.facishare.open.qywx.web.manager.QYWeixinManager;
import com.facishare.open.qywx.web.mq.sender.MQSender;
import com.facishare.open.qywx.web.template.outer.repMsg.event.ticket.QyweixinRepTicketEventHandlerTemplate;
import com.facishare.open.qywx.web.utils.SecurityUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 企微代开发外部事件处理器模板实现类
 * <AUTHOR>
 * @date 2024-11-20
 */

@Slf4j
@Component
public class QyweixinOuterRepMsgEventHandlerTemplate extends OuterEventHandlerTemplate {
    @ReloadableProperty("appMetaInfoStr2")
    private String appMetaInfoCmsStr;

    @Resource
    private RepEventHandler repEventHandler;

    @Resource
    private MQSender mqSender;

    @Resource
    private QyweixinRepTicketEventHandlerTemplate qyweixinRepTicketEventHandlerTemplate;

    @Autowired
    private EventCloudProxyManager eventCloudProxyManager;

    Map<String, AppConfigInfo> appMetaInfo = Maps.newHashMap();

    private static final List<String> repProxyCloudEvent = Lists.newArrayList("cancel_auth", "change_auth", "change_contact", "change_external_chat", "enter_agent");

    public List<String> supportedCmdEvent = Lists.newArrayList(
            "suite_ticket",
            "create_auth","change_auth","cancel_auth","change_contact",
            "open_order","change_order","pay_for_app_success","refund",
            "reset_permanent_code","agree_external_userid_migration",
            "change_external_contact","change_external_chat","enter_agent",
            "share_chain_change"
    );

    @PostConstruct
    public void initAppMeta() {
        //在cms上的格式配置格式 appMetaInfoStr=source,appid,appsecret   保存的时候key使用source_appid
        ArrayList<AppConfigInfo> appMetaComponent = new Gson().fromJson(appMetaInfoCmsStr, new TypeToken<ArrayList<AppConfigInfo>>() {
        }.getType());
        log.info("QyweixinOuterRepMsgEventHandlerTemplate.initAppMeta, appMetaInfoStr={}, appMetaComponent={} ", appMetaInfoCmsStr, appMetaComponent);
        appMetaComponent.stream().forEach(v -> {
            v.setEncodingAESKeyBase64(Base64.decodeBase64(v.getEncodingAESKey() + "="));
            v.setToken(SecurityUtil.decryptStr(v.getToken()));
            v.setSecret(SecurityUtil.decryptStr(v.getSecret()));
            appMetaInfo.put(v.getAppId(), v);
        });
    }

    @Override
    public void onEventDecode(MethodContext context) {
        log.info("QyweixinOuterRepMsgEventHandlerTemplate.onEventDecode,context={}",context);

        EnterpriseWeChatEventProto eventProto = context.getData();

        //校验
        if (StringUtils.isNotEmpty(eventProto.getEchoStr())) {
            String verified = verifyURL(eventProto.getSignature(), eventProto.getTimestamp(), eventProto.getNonce(), eventProto.getEchoStr(), eventProto.getAppId());
            //这里比较特殊，只是检验地址配置是否正确，返回错误就行
            context.setResult(TemplateResult.newErrorData(verified));
            return;
        }

        //解密
        String plainMsg;
        try {
            plainMsg = repEventHandler.decryptMsg(eventProto.getSignature(),
                        eventProto.getTimestamp(),
                        eventProto.getNonce(),
                        eventProto.getData(),
                        eventProto.getAppId());
            log.info("QyweixinOuterRepMsgEventHandlerTemplate.onMsgEvent,plainMsg={}", plainMsg);
        } catch (Exception e) {
            log.info("QyweixinOuterRepMsgEventHandlerTemplate.onMsgEvent,exception={}", e.getMessage(),e);
            context.setResult(TemplateResult.newErrorData("success"));
            return;
        }
        //准备下一步需要的context
        context.setData(plainMsg);
        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void onEventFilter(MethodContext context) {
        log.info("QyweixinOuterRepMsgEventHandlerTemplate.onEventFilter,context={}",context);

        String plainMsg = context.getData();
        QyweixinMsgBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QyweixinMsgBaseXml.class);
        log.info("QyweixinOuterRepMsgEventHandlerTemplate.handle,baseMsgXml={}",baseMsgXml);
        String infoType = baseMsgXml.getInfoType();
        log.info("QyweixinOuterRepMsgEventHandlerTemplate.handle,infoType={}",infoType);

        if(StringUtils.isEmpty(infoType)) {
            EventXml eventXml = XStreamUtils.parseXml(plainMsg, EventXml.class);
            infoType = eventXml.getEvent();
            log.info("QyweixinOuterRepMsgEventHandlerTemplate.handle,infoType2={}",infoType);
        }
        if(!supportedCmdEvent.contains(infoType)) {
            log.info("QyweixinOuterRepMsgEventHandlerTemplate.handle，not support event,infoType={}",infoType);
            context.setResult(TemplateResult.newErrorData("success"));
            return;
        }

        //去掉不支持的外部联系人事件
        List<String> externalContactChangeTypeList = Lists.newArrayList("add_external_contact","edit_external_contact","del_external_contact","del_follow_user");
        if(StringUtils.equalsIgnoreCase("change_external_contact",infoType)
                && !externalContactChangeTypeList.contains(baseMsgXml.getChangeType())) {
            context.setResult(TemplateResult.newErrorData("success"));
            return;
        }

        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void onEventHandle(MethodContext context) {
        log.info("QyweixinOuterRepMsgEventHandlerTemplate.onEventHandle,context={}",context);
        String plainMsg = context.getData();

        try {
            //QyweixinMsgBaseXml baseMsgXml = XmlParser.fromXml(plainMsg, QyweixinMsgBaseXml.class);
            QyweixinMsgBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QyweixinMsgBaseXml.class);
            log.info("QyweixinOuterRepMsgEventHandlerTemplate.repMsgEvent,baseMsgXml={}", baseMsgXml);
            String infoType = baseMsgXml.getInfoType();
            log.info("QyweixinOuterRepMsgEventHandlerTemplate.repMsgEvent,infoType={}", infoType);
            TagChangeEventXml tagChangeEventXml = null;
            if (StringUtils.isEmpty(infoType)) {
                //tagChangeEventXml = XmlParser.fromXml(plainMsg, TagChangeEventXml.class);
                tagChangeEventXml = XStreamUtils.parseXml(plainMsg, TagChangeEventXml.class);
                infoType = tagChangeEventXml.getEvent();
                log.info("QyweixinOuterRepMsgEventHandlerTemplate.repMsgEvent,infoType2={}", infoType);
            }
            //紧急ticket事件，立即处理
            if ("suite_ticket".equals(infoType)) {
                //接收推送ticket
                //SuiteAuthXml suiteAuthXml = XmlParser.fromXml(plainMsg, SuiteAuthXml.class);
                SuiteAuthXml suiteAuthXml = XStreamUtils.parseXml(plainMsg, SuiteAuthXml.class);
                log.info("QyweixinOuterRepMsgEventHandlerTemplate.repMsgEvent,infoType={},suiteAuthXml={}", infoType, suiteAuthXml);
                qyweixinRepTicketEventHandlerTemplate.execute(suiteAuthXml);
//                qyWeixinManager.saveQyweixinTicketToken(suiteAuthXml, Boolean.TRUE);
            } else {
//                if(tagChangeEventXml!=null && StringUtils.equalsIgnoreCase(tagChangeEventXml.getEvent(),"change_external_contact")) {
//                    //代开发应用不支持change_external_contact事件
//                    log.info("QyweixinGatewayInnerServiceImpl.repMsgEvent,rep app not support change_external_contact event");
//                    return "fail";
//                }

                //判断是否需要跨云
                if (repProxyCloudEvent.contains(infoType)) {
                    eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, baseMsgXml.getSuiteId(), EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_EVENT_4_REP, baseMsgXml.getAuthCorpId(), infoType, plainMsg, null);
                }

                //如果是非紧急事件，发送MQ，等待后面消费
                mqSender.sendEnterpriseWeChatMQ(EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_EVENT_4_REP, plainMsg);
            }
        } catch (Exception e) {
            log.warn("QyweixinOuterRepMsgEventHandlerTemplate.repMsgEvent,exception={}", e.getMessage());
            context.setResult(TemplateResult.newErrorData("fail"));
            return;
        }

        context.setResult(TemplateResult.newSuccess("success"));
    }

    private String verifyURL(String msgSignature, String timeStamp, String nonce, String echostr, String appID) {
        String result = null;
        try {
            result = QYWxCryptHelper.VerifyURL(appMetaInfo.get(appID).getToken(), appMetaInfo.get(appID).getEncodingAESKeyBase64(), msgSignature, timeStamp, nonce, echostr);
            log.info("QyweixinOuterRepMsgEventHandlerTemplate.verifyURL,result={}", result);
        } catch (Exception e) {
            log.error("QyweixinOuterRepMsgEventHandlerTemplate.verifyURL,exception", e);
        }
        return result;
    }
}
