package com.facishare.open.qywx.accountinner.model;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.net.URLDecoder;

/**
 * <AUTHOR>
 * @date 2024/02/26
 * 企微连接参数类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class QYWXConnectParam implements Serializable {
    private static final long serialVersionUID = 2369962196456072951L;

    /**
     * 企微连接器id
     */
    private String dataCenterId;

    /**
     * 企微连接器名称
     */
    private String dataCenterName;

    private String fsEa;

    private String outEa;

    /**
     * 企微企业名称
     */
    private String outEn;

    private String outDepId;

    private Integer bindType;  //0-新建企业并绑定 1-绑定已有的纷享企业

    public static QYWXConnectParam parse(String value) {
        if(StringUtils.isEmpty(value)) return new QYWXConnectParam();
        log.info("QYWXConnectParam.parse,value={}",value);
        try {
            String json = URLDecoder.decode(value,"utf-8");
            log.info("QYWXConnectParam.parse,json={}",json);
            QYWXConnectParam qywxConnectParam = JSON.parseObject(json, QYWXConnectParam.class);
            if(qywxConnectParam!=null && StringUtils.isEmpty(qywxConnectParam.getOutDepId())) {
                qywxConnectParam.setOutDepId("1");
            }
            log.info("QYWXConnectParam.parse,qywxConnectParam={}",qywxConnectParam);
            return qywxConnectParam;
        } catch (Exception e) {
            log.info("QYWXConnectParam.parse,url decode exception={}",e.getMessage());
        }
        return new QYWXConnectParam();
    }

    public static boolean isInvalid(QYWXConnectParam connectParam) {
        return connectParam == null
                || StringUtils.isEmpty(connectParam.getFsEa())
                || StringUtils.isEmpty(connectParam.getOutEa());
    }

    public static void main(String[] args) {

    }
}