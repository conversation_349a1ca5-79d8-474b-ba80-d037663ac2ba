package com.facishare.open.qywx.web.entity.entity;

import com.facishare.open.qywx.accountsync.annotation.SecurityField;
import com.facishare.open.qywx.accountsync.annotation.SecurityObj;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/3/25 12:06
 * @Version 1.0
 */
@Data
@SecurityObj
@Alias("messageGeneratingPo")
public class MessageGeneratingPo implements Serializable {
    private Long id;
    private Integer fsTenantId;
    private String ea;
    private String qywxCorpId;//企业微信id
    @SecurityField
    private String secret;//会话密钥
    @SecurityField
    private String publicKey;//公钥
    @SecurityField
    private String privateKey;//私钥
    private Integer version;//版本号
    @SecurityField
    private String corpSecret;//自建应用密钥
    private String agentId;//企业微信的应用id
    private String storageLocation;//会话存档存储位置
}
