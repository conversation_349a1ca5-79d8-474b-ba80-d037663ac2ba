package com.facishare.open.qywx.web.db.dao;

import com.facishare.open.qywx.web.entity.entity.MessageGeneratingPo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/25 11:18
 * @Version 1.0
 */
@Repository
public interface MessageGeneratingDao extends ICrudMapper<MessageGeneratingPo> {
    @Insert("INSERT INTO message_generating (fs_tenant_id, ea, qywx_corp_id, secret, public_key, private_key, version, corp_secret, agent_id, storage_location) " +
            "VALUES (#{fsTenantId}, #{ea}, #{qywxCorpId}, #{secret}, #{publicKey}, #{privateKey}, #{version}, #{corpSecret}, #{agentId}, #{storageLocation}) " +
            "ON DUPLICATE KEY UPDATE qywx_corp_id=#{qywxCorpId}, secret=#{secret}, corp_secret=#{corpSecret}, storage_location=#{storageLocation}")
    int saveInfo(MessageGeneratingPo messageGeneratingPo);

    @Select("<script>" +
            "SELECT * FROM message_generating " + // Modify with actual columns
            "WHERE ea = #{ea} " +
            "<if test='version != null'>AND version = #{version} </if>" +
            "<if test='outEa != null'>AND qywx_corp_id = #{outEa} </if>" +
            "ORDER BY create_time DESC LIMIT 1" +
            "</script>")
    MessageGeneratingPo getByVersion(@Param("ea") String ea, @Param("version") Integer version, @Param("outEa") String outEa);

    @Select("SELECT DISTINCT ea FROM message_generating")
    List<String> queryAllEa();

    @Select("SELECT * FROM message_generating WHERE ea = #{ea}") // Modify with actual columns
    List<MessageGeneratingPo> queryByEaSetting(@Param("ea") String ea);

    @Select("<script>" +
            "SELECT aut_retention FROM enterprise_account_bind WHERE fs_ea = #{ea} " +
            "<if test='outEa != null and outEa != \"\"'>AND out_ea = #{outEa} </if>" +
            "ORDER BY gmt_create ASC LIMIT 1" +
            "</script>")
    Integer getAuto(@Param("ea") String ea, @Param("outEa") String outEa);

    @Update("<script>" +
            "UPDATE enterprise_account_bind SET aut_retention = #{auto} WHERE fs_ea = #{ea} " +
            "<if test='outEa != null and outEa != \"\"'>AND out_ea = #{outEa} </if>" +
            "</script>")
    int updateAuto(@Param("ea") String ea, @Param("auto") int auto, @Param("outEa") String outEa);

    @Insert("INSERT INTO message_generating (fs_tenant_id, ea, qywx_corp_id, corp_secret, agent_id) VALUES (#{fsTenantId}, #{ea}, #{qywxCorpId}, #{corpSecret}, #{agentId})")
    int saveSecretInfo(MessageGeneratingPo messageGeneratingPo);

    @Select("<script>" +
            "SELECT COUNT(*) FROM message_generating WHERE ea = #{ea} AND corp_secret IS NOT NULL AND version IS NULL " +
            "<if test='outEa != null and outEa != \"\"'>AND qywx_corp_id = #{outEa} </if>" +
            "</script>")
    int queryAuto(@Param("ea") String ea, @Param("outEa") String outEa);

    @Select("<script>" +
            "SELECT COUNT(*) FROM message_generating WHERE ea = #{ea} " +
            "<if test='outEa != null and outEa != \"\"'>AND qywx_corp_id = #{outEa} </if>" +
            "</script>")
    int queryIsAuto(@Param("ea") String ea, @Param("outEa") String outEa);

    @Update("UPDATE message_generating SET secret = #{secret}, public_key = #{publicKey}, private_key = #{privateKey}, version = #{version} WHERE ea = #{ea} AND fs_tenant_id = #{fsTenantId} AND qywx_corp_id = #{qywxCorpId}")
    int saveInfoByAuto(MessageGeneratingPo messageGeneratingPo);

    @Update("UPDATE message_generating SET corp_secret = #{corpSecret}, agent_id = #{agentId} WHERE ea = #{ea} AND fs_tenant_id = #{fsTenantId} AND qywx_corp_id = #{qywxCorpId}")
    int saveInfoByNoAuto(MessageGeneratingPo messageGeneratingPo);

    @Update("UPDATE message_generating SET corp_secret = #{corpSecret}, agent_id = #{agentId} WHERE qywx_corp_id = #{qywxCorpId}")
    int updateCorpRepSecret(MessageGeneratingPo messageGeneratingPo);

    @Select("SELECT corp_secret FROM message_generating WHERE ea = #{ea} ORDER BY create_time DESC")
    List<String> getSecret(MessageGeneratingPo messageGeneratingPo);

    @Select("SELECT COUNT(*) FROM message_generating WHERE ea = #{ea} AND corp_secret IS NOT NULL AND version IS NULL")
    int querySecret(@Param("ea") String ea);

    @Select("SELECT agent_id FROM message_generating WHERE ea = #{ea} ORDER BY create_time DESC")
    List<String> getAgentId(MessageGeneratingPo messageGeneratingPo);

    @Update("UPDATE message_generating SET corp_secret = #{corpSecret}, secret = #{secret}, public_key = #{publicKey}, private_key = #{privateKey} WHERE ea = #{ea} AND fs_tenant_id = #{fsTenantId} AND qywx_corp_id = #{qywxCorpId} AND version = #{version}")
    int updateCorpSecret(MessageGeneratingPo messageGeneratingPo);
}
