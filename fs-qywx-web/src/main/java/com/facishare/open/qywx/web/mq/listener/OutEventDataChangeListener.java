package com.facishare.open.qywx.web.mq.listener;

import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.manager.EventCloudProxyManager;
import com.facishare.open.qywx.accountinner.model.OutEventDateChangeProto;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> 企微监听跨云mq
 * @Date 2024/04/26 16:21
 * @Version 1.0
 */
@Service("outEventDataChangeListener")
public class OutEventDataChangeListener implements MessageListenerConcurrently {
    @Resource
    private EventCloudProxyManager eventCloudProxyManager;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            TraceUtils.initTrace(msg.getMsgId());
            LogUtils.info("OutEventDataChangeListener.consumeMessage,msg={}",msg);
            try {
                //测试环境没有专属云
                String env = System.getProperty("process.profile");
                if(!ConfigCenter.IS_TEST) {
                    if(env.equals("fstest") || env.equals("fstest-gray")) {
                        continue;
                    }
                }

                if(ConfigCenter.MAIN_ENV) {
                    continue;
                }

                String receiveTags = msg.getTags();
                if(!receiveTags.equals(ChannelEnum.qywx.name())) {
                    continue;
                }

                OutEventDateChangeProto proto = new OutEventDateChangeProto();
                proto.fromProto(msg.getBody());
                eventCloudProxyManager.handleEvent(proto);
            } catch (Exception e) {
                LogUtils.error("OutEventDataChangeListener.consumeMessage consume failed.", e);
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

}
