package com.facishare.open.qywx.web.template.inner.listener;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.inner.listener.FsOrganizationChangedListenerTemplate;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.result.Result;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.service.ContactBindInnerService;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.organization.api.event.organizationChangeEvent.EmployeeChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.UUID;

@Slf4j
@Component
public class QyweixinFsOrganizationChangedListenerTemplate extends FsOrganizationChangedListenerTemplate {
    @Resource
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Resource
    private ContactBindInnerService contactBindInnerService;

    @Override
    public void onEmployeeChanged(MethodContext context) {
        log.info("QyweixinFsOrganizationChangedListenerTemplate.onEmployeeChanged,context={}",context);
        EmployeeChangeEvent event = context.getData();

        String traceId = TraceUtils.getTraceId();
        if(StringUtils.isEmpty(traceId)) {
            TraceUtils.initTraceId(UUID.randomUUID() + "_" + event.getEnterpriseAccount());
        }

        String fsEa = event.getEnterpriseAccount();
        if(ObjectUtils.isNotEmpty(event.getOldEmployeeDto())) {
            //不是新建人员过滤
            return;
        }
        log.info("QyweixinFsOrganizationChangedListenerTemplate.onEmployeeChanged.fsEa={},AUTO_BIND_ACCOUNT_EA={}.", fsEa, ConfigCenter.AUTO_BIND_ACCOUNT_EA);
        if(!ConfigCenter.AUTO_BIND_ACCOUNT_EA.contains(fsEa)) {
            //不是灰度名单
            return;
        }

        Result<QyweixinAccountEnterpriseMapping> qyweixinCorpIDResult =
                qyweixinAccountBindService.fsEaToOutEaResult("qywx", fsEa);
        log.info("QyweixinFsOrganizationChangedListenerTemplate.onEmployeeChanged.qyweixinCorpIDResult={}.", qyweixinCorpIDResult);
        if (!qyweixinCorpIDResult.isSuccess() || ObjectUtils.isEmpty(qyweixinCorpIDResult.getData())) {
            return;
        }
        com.facishare.open.qywx.accountsync.result.Result<Void> result = contactBindInnerService.listenMqEventToBindAccount(fsEa, qyweixinCorpIDResult.getData().getIsvOutEa(), event.getNewEmployeeDto());
        log.info("QyweixinFsOrganizationChangedListenerTemplate.onEmployeeChanged,result={}",result);
    }

    @Override
    public void onDepartmentChanged(MethodContext context) {
        log.info("QyweixinFsOrganizationChangedListenerTemplate.onDepartmentChanged,context={}",context);
    }
}
