package com.facishare.open.qywx.web.template;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.OuterEventHandlerTemplate;
import com.facishare.open.qywx.accountsync.constant.EnterpriseWeChatEventTag;
import com.facishare.open.qywx.accountsync.model.EnterpriseWeChatEventProto;
import com.facishare.open.qywx.accountsync.model.qyweixin.AppConfigInfo;
import com.facishare.open.qywx.accountsync.utils.QYWxCryptHelper;
import com.facishare.open.qywx.web.eventHandler.ThirdDataEventHandler;
import com.facishare.open.qywx.web.mq.sender.MQSender;
import com.facishare.open.qywx.web.utils.SecurityUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Map;

/**
 * 企微系统事件接收URL
 * <AUTHOR>
 * @date 2024-11-20
 */

@Slf4j
@Component
public class QyweixinOuterRecvSystemEventHandlerTemplate extends OuterEventHandlerTemplate {
    @ReloadableProperty("appMetaInfoStr2")
    private String appMetaInfoCmsStr;

    @Resource
    private ThirdDataEventHandler thirdDataEventHandler;

    @Resource
    private MQSender mqSender;

    Map<String, AppConfigInfo> appMetaInfo = Maps.newHashMap();


    @PostConstruct
    public void initAppMeta() {
        //在cms上的格式配置格式 appMetaInfoStr=source,appid,appsecret   保存的时候key使用source_appid
        ArrayList<AppConfigInfo> appMetaComponent = new Gson().fromJson(appMetaInfoCmsStr, new TypeToken<ArrayList<AppConfigInfo>>() {
        }.getType());
        log.info("QyweixinOuterRecvSystemEventHandlerTemplate.initAppMeta, appMetaInfoStr={}, appMetaComponent={} ", appMetaInfoCmsStr, appMetaComponent);
        appMetaComponent.stream().forEach(v -> {
            v.setEncodingAESKeyBase64(Base64.decodeBase64(v.getEncodingAESKey() + "="));
            v.setToken(SecurityUtil.decryptStr(v.getToken()));
            v.setSecret(SecurityUtil.decryptStr(v.getSecret()));
            appMetaInfo.put(v.getAppId(), v);
        });
    }

    @Override
    public void onEventDecode(MethodContext context) {
        log.info("QyweixinOuterRecvSystemEventHandlerTemplate.onEventDecode,context={}",context);

        EnterpriseWeChatEventProto eventProto = context.getData();

        //校验
        if (StringUtils.isNotEmpty(eventProto.getEchoStr())) {
            String verified = verifyURL(eventProto.getSignature(), eventProto.getTimestamp(), eventProto.getNonce(), eventProto.getEchoStr(), eventProto.getAppId());
            //这里比较特殊，只是检验地址配置是否正确，返回错误就行
            context.setResult(TemplateResult.newErrorData(verified));
            return;
        }

        //解密
        String plainMsg;
        try {
            plainMsg = thirdDataEventHandler.decryptMsg(eventProto.getSignature(),
                        eventProto.getTimestamp(),
                        eventProto.getNonce(),
                        eventProto.getData(),
                        eventProto.getAppId());
            log.info("QyweixinOuterRecvSystemEventHandlerTemplate.onMsgEvent,plainMsg={}", plainMsg);
        } catch (Exception e) {
            log.info("QyweixinOuterRecvSystemEventHandlerTemplate.onMsgEvent,exception={}", e.getMessage(),e);
            context.setResult(TemplateResult.newErrorData("success"));
            return;
        }
        //准备下一步需要的context
        context.setData(plainMsg);
        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void onEventFilter(MethodContext context) {
        log.info("QyweixinOuterRecvSystemEventHandlerTemplate.onEventFilter,context={}",context);
        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void onEventHandle(MethodContext context) {
        log.info("QyweixinOuterRecvSystemEventHandlerTemplate.onEventHandle,context={}",context);
        String plainMsg = context.getData();

        //非紧急事件，发送MQ，等待后面消费
        mqSender.sendEnterpriseWeChatMQ(EnterpriseWeChatEventTag.TAG_SYSTEM_EVENT_NOTIFY,plainMsg);

        context.setResult(TemplateResult.newSuccess("success"));
    }

    private String verifyURL(String msgSignature, String timeStamp, String nonce, String echostr, String appID) {
        String result = null;
        try {
            result = QYWxCryptHelper.VerifyURL(appMetaInfo.get(appID).getToken(), appMetaInfo.get(appID).getEncodingAESKeyBase64(), msgSignature, timeStamp, nonce, echostr);
            log.info("QyweixinOuterRecvSystemEventHandlerTemplate.verifyURL,result={}", result);
        } catch (Exception e) {
            log.error("QyweixinOuterRecvSystemEventHandlerTemplate.verifyURL,exception", e);
        }
        return result;
    }
}
