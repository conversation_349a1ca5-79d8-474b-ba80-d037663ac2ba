package com.facishare.open.qywx.accountsync.result;


import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringEnum;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/11
 */
public enum ErrorRefer {
    SUCC("s120050000","成功", I18NStringEnum.s1.getI18nKey()),// "成功",
    INTERNAL_ERROR("s320050001","内部错误", I18NStringEnum.s4.getI18nKey()), // "内部错误"
    QUERRY_EMPTY("s320050002","查询数据为空", I18NStringEnum.s6.getI18nKey()), // "查询数据为空",
    CORP_ACCOUNT_NOT_BIND("s320050003","企业账号未绑定", I18NStringEnum.s7.getI18nKey()), // "企业账号未绑定",
    INVALID_TICKET("s320050004","找不到此ticket的信息", I18NStringEnum.s8.getI18nKey()), //找不到此ticket的信息
    EXPIRED_TICKET("s320050005","ticket已经过期", I18NStringEnum.s9.getI18nKey()), //ticket已经过期
    CORP_NOT_AUTHORIZED("s320050006","查不到企业的授权信息", I18NStringEnum.s10.getI18nKey()), // "查不到企业的授权信息",
    BIND_ERROR("s320050007","绑定数量为0", I18NStringEnum.s11.getI18nKey()), // "绑定数量为0",
    CORP_CREATING("s320050008","企业初始化中", I18NStringEnum.s12.getI18nKey()), //企业初始化中
    CONTACT_QYWX_BIND_EXIT("s320050009","该企业微信账号已绑定其它纷享账号，请纷享客服", I18NStringEnum.s13.getI18nKey()), //该企业微信账号已绑定其它纷享账号 请纷享客服
    CONTACT_NOT_BIND("s320050010","不存在绑定关系（引导安装通讯录应用）", I18NStringEnum.s14.getI18nKey()),  //不存在绑定关系（引导安装通讯录应用）
    CONTACT_FIRST_BIND("s320050011","通讯录首次绑定", I18NStringEnum.s15.getI18nKey()), //通讯录首次绑定
    CONTACT_FSEA_BIND_EXIT("s320050012","该纷享账号已绑定其它企业微信账号，请纷享客服解绑", I18NStringEnum.s16.getI18nKey()),//该纷享账号已绑定其它企业微信账号 请纷享客服解绑

    CRM_APP_NOT_INSTALL("s320050020","CRM应用没有安装", I18NStringEnum.s17.getI18nKey()), //CRM应用没有安装
    OLD_CORP_ACCOUNT_NOT_BIND("s320050021","手动绑定场景，请在CRM系统企业微信对接页面->帐号绑定->已绑定Tab查看对应人员是否已正确绑定，如果没有绑定，请手动添加绑定", I18NStringEnum.s18.getI18nKey()), //手动绑定场景，请在CRM系统企业微信对接页面->帐号绑定->已绑定Tab查看对应人员是否已正确绑定，如果没有绑定，请手动添加绑定

    DIAL_NOT_GRANT_PRIVILEGE("s320050022","84056", I18NStringEnum.s19.getI18nKey()),  //84056 被拨打用户安装应用时未授权拨打公费电话权限
    DIAL_CALLEE_NOT_SUPPORT("s320050023","84060", I18NStringEnum.s20.getI18nKey()),         //84060 呼叫号码不支持
    DIAL_INSUFFICIENT_ACCOUNT_BALANCE("s320050024","84057", I18NStringEnum.s21.getI18nKey()),   //84057 公费电话余额不足
    DIAL_CALLEE_NOT_EXIST("s320050025","84064", I18NStringEnum.s22.getI18nKey()),  //84064 被叫号码不存在
    DIAL_OUT_OF_LIMIT("s320050026","84067", I18NStringEnum.s23.getI18nKey()),      //84067 管理员收到的服务商公费电话个数超过限制
    DIAL_OTHER_ERROR("s320050027","其他公费电话返回错误", I18NStringEnum.s24.getI18nKey()),       //其他公费电话返回错误
    DIAL_LACK_OF_CALLER("s320050028","84052", I18NStringEnum.s25.getI18nKey()),  //84052	缺少caller参数
    DIAL_LACK_OF_CALLEE("s320050029","84053", I18NStringEnum.s26.getI18nKey()),  //84053	缺少callee参数
    DIAL_LACK_OF_AUTH_CORID("s320050030","84054", I18NStringEnum.s27.getI18nKey()),   //84054	缺少auth_corpid参数
    DIAL_OVER_FREQUENCY("s320050031","84055", I18NStringEnum.s28.getI18nKey()),  //84055	超过拨打公费电话频率,同一个客服5秒内只能调用api拨打一次公费电话
    DIAL_CALLER_NOT_SUPPORT("s320050032","84058", I18NStringEnum.s29.getI18nKey()),   //84058	caller 呼叫号码不支持
    DIAL_ILLEGAL_NUMBER("s320050033","84059", I18NStringEnum.s30.getI18nKey()),   //84059   号码非法
    DIAL_NO_RELATIONSHIP("s320050034","84061", I18NStringEnum.s31.getI18nKey()),   //84061	不存在外部联系人的关系
    DIAL_UNOPENED("s320050035","84062", I18NStringEnum.s32.getI18nKey()),  //84062	未开启公费电话应用
    DIAL_CALLER_NOT_EXIST("s320050036","84063", I18NStringEnum.s33.getI18nKey()),   //84063	caller不存在
    DIAL_IDENTICAL("s320050037","84065", I18NStringEnum.s34.getI18nKey()),   //84065	caller跟callee电话号码一致
    DIAL_OVER_LIMIT("s320050038","84066", I18NStringEnum.s35.getI18nKey()),  //84066	服务商拨打次数超过限制,单个企业管理员，在一天（以上午10:00为起始时间）内，对应单个服务商，只能被呼叫【4】次。
    DIAL_ILLEGAL_AUTH("s320050039","84071", I18NStringEnum.s36.getI18nKey()),  //84071	不合法的外部联系人授权码,非法或者已经消费过
    DIAL_NO_CUSTOM_SERVICE("s320050040","84072", I18NStringEnum.s37.getI18nKey()),  //84072	应用未配置客服
    DIAL_CUSTOM_SERVICE_NOT_EXIST("s320050041","84073", I18NStringEnum.s38.getI18nKey()),  //84073	客服userid不在应用配置的客服列表中
    DIAL_NO_EXTERNAL("s320050042","84074", I18NStringEnum.s39.getI18nKey()),   // 84074	没有外部联系人权限

    CONTACT_CANCEL_BIND("s320050043","通讯录同步已取消", I18NStringEnum.s40.getI18nKey()), //通讯录同步已取消
    CONTACT_DELETE_ADMIN("s320050044","不能解绑企业开通管理员", I18NStringEnum.s41.getI18nKey()), //不能解绑企业开通管理员
    CONTACT_NOT_ALLOW("s320050045","从企业微信端添加应用生成的账号不允许绑定", I18NStringEnum.s42.getI18nKey()) ,    //从企业微信端添加应用生成的账号不允许绑定
    CONTACT_NOT_AUTHORIZED("s320050046","企业不在授权绑定的白名单中", I18NStringEnum.s43.getI18nKey()) ,    //企业不在授权绑定的白名单中
    CONTACT_NOT_TRANSLATION("s320050047","没有需要转译的内容", I18NStringEnum.s44.getI18nKey()),   // 没有需要转译的内容!
    MANUAL_BINDING_NOT_SUPPORTED("s320050048","手动绑定不支持", I18NStringEnum.s45.getI18nKey()),   // 手动绑定不支持

    NOT_EXTERNAL_LIST("203","获取外部联系人为空", I18NStringEnum.s46.getI18nKey()),   // 没有需要转译的内容!

    CONTACT_AUTO_ATTACHMENTS("s320050049", "自动绑定失败", I18NStringEnum.s47.getI18nKey()),

    GET_ACCESS_TOKEN_FAILED("s320050050", "获取access token failed", I18NStringEnum.s48.getI18nKey()), //获取access token failed
    GET_QYWX_USER_INFO_FAILED("s320050051","获取企业微信用户信息失败", I18NStringEnum.s49.getI18nKey()),
    GET_AUTH_TICKET_FAILED("s320050052","获取登录ticket失败", I18NStringEnum.s50.getI18nKey()),
    EMPLOYEE_IS_STOP("s320050053","员工已停用，请在CRM应用可见范围删除再添加有问题的人员", I18NStringEnum.s51.getI18nKey()), // "员工已停用，请在CRM应用可见范围删除再添加有问题的人员",
    PARAM_ERROR("s320050054","参数错误", I18NStringEnum.s5.getI18nKey()), // "参数错误",
    REP_APP_NOT_ENABLE("s320050055","代开发应用未安装或者代开发应用未授权", I18NStringEnum.s52.getI18nKey()),
    REP_APP_IS_STOP("s320050056","代开发应用已停用，请删除代开发应用并重新授权安装", I18NStringEnum.s53.getI18nKey()),
    CRM_APP_NOT_ENABLE("s320050057","CRM应用未安装，请重新安装", I18NStringEnum.s54.getI18nKey()),
    CRM_APP_IS_STOP("s320050058","CRM应用已停用，请删除CRM应用并重新安装", I18NStringEnum.s55.getI18nKey()),
    CRM_EA_HAS_BIND("s320050059","CRM企业已经和另一个企业微信企业绑定，已绑定的企业微信ID=%s，企业微信名称=%s，请解绑后再操作！", I18NStringEnum.s56.getI18nKey()),
    QYWX_CORPID_HAS_BIND("s320050060","企业微信企业已经和另一个CRM企业绑定，已绑定的CRM企业ea=%s，请解绑后再操作！", I18NStringEnum.s57.getI18nKey()),
    CORPID_CONVERSION_FAILED("s320050061","企业微信企业ID转换失败", I18NStringEnum.s58.getI18nKey()),
    FILE_OVERSIZE("s320050062","文件大小超过20M", I18NStringEnum.s59.getI18nKey()),
    GET_FILE_META_DATA_FAILED("s320050063","获取文件元数据失败", I18NStringEnum.s60.getI18nKey()),
    FILE_DOWNLOAD_FAILED("s320050064","文件下载失败", I18NStringEnum.s61.getI18nKey()),
    FILE_UPLOAD_FAILED("s320050065","文件上传失败", I18NStringEnum.s62.getI18nKey()),
    FS_EA_NOT_BIND("s320050066","纷享企业没有和企业微信企业绑定", I18NStringEnum.s63.getI18nKey()),
    QYWX_NOT_DATA("s320050067","暂时还没有数据", I18NStringEnum.s64.getI18nKey()),
    QYWX_NOT_INSTALL_VALID_FS_APP("s320050068","企微客户没有安装有效的纷享企微应用，比如CRM，订货通，服务通等", I18NStringEnum.s65.getI18nKey()),
    ENTERPRISE_BIND_DEPT_ID_ERROR("s320050069","企微和CRM绑定失败，请检查是否输错了企微部门id", I18NStringEnum.s66.getI18nKey()),
    FS_EMP_NOT_BIND("s320050070","找不到纷享员工和企微员工的绑定关系", I18NStringEnum.s66.getI18nKey()),
    OUT_EA_NOT_BIND("s320050071","找不企微企业和纷享企业的绑定关系", I18NStringEnum.s67.getI18nKey()),
    FS_EMP_CREATE_FAILED("s320050072","纷享员工创建失败", I18NStringEnum.s68.getI18nKey()),
    QYWX_EMP_MAPPING_NOT_EXIST("s320050073","企微员工绑定关系不存在", I18NStringEnum.s69.getI18nKey()),
    FS_ADMIN_CANNOT_STOP("s320050074","纷享企业管理员不能停用", I18NStringEnum.s70.getI18nKey()),
    UPDATE_TAG_EVENT_ONLY_BY_REP("s320050075","非代开发应用和代开发应用的可见范围内同时有相同的标签，只以代开发应用的身份处理标签更新事件", I18NStringEnum.s71.getI18nKey()),
    REQUIRED_PARAMETER("s320050076","请检查是否有参数未填写", I18NStringEnum.s72.getI18nKey()),
    TOKEN_ERROR("s320050077","获取token失败", I18NStringEnum.s73.getI18nKey()),
    JSAPI_TICKET_ERROR("s320050078","获取企业微信签名失败", I18NStringEnum.s74.getI18nKey()),
    STOP_DEPARTMENT_ERROR("s320050079","crm部门停用失败", I18NStringEnum.s75.getI18nKey()),
    START_DEPARTMENT_ERROR("s320050080","crm部门启用失败", I18NStringEnum.s76.getI18nKey()),
    TRANSFER_CUSTOMER_ERROR("s320050081","在职离职继承失败", I18NStringEnum.s77.getI18nKey()),
    IMAGE_OVERSIZE("s320050082","图片大小超过10M", I18NStringEnum.s78.getI18nKey()),
    VIDEO_OVERSIZE("s320050083","视频大小超过10M", I18NStringEnum.s79.getI18nKey()),
    VOICE_OVERSIZE("s320050084","语音大小超过2M", I18NStringEnum.s80.getI18nKey()),
    QUERY_CRM_USER_INFO_ERROR("s320050090","查询纷享员工信息失败", I18NStringEnum.s81.getI18nKey()),
    CONNECT_PARAMS_IS_EMPTY("s320050091","连接信息为空", I18NStringEnum.s82.getI18nKey()),
    ENTERPRISE_BIND_FAILED("s320050092","纷享和企微企业绑定失败", I18NStringEnum.s83.getI18nKey()),
    QYWX_CONNECTOR_INIT_FAILED("s320050093","企微连接器初始化失败", I18NStringEnum.s84.getI18nKey()),
    QYWX_CONNECTOR_ORDER_FAILED("s320050094","企微连接器订单下单失败", I18NStringEnum.s85.getI18nKey()),
    QYWX_CONNECTOR_NEED_ORDER("s320050095","如果要使用企微连接器，请购买", I18NStringEnum.s86.getI18nKey()),
    QYWX_CONNECTOR_INITED("s320050096","手动绑定的企业，企微连接器已经初始化", I18NStringEnum.s87.getI18nKey()),
    OUT_EA_IS_AUTO_BIND_CANNOT_MANUAL_BIND("s320050097",
            "当前绑定的企微企业已经和别的纷享企业存在自动绑定关系，请先解绑原自动绑定关系，再执行反绑定操作",
            I18NStringEnum.s88.getI18nKey()),
    EXPORTING("s320050098","导出中，请留意企信通知", I18NStringEnum.s89.getI18nKey()),
    DATA_LIST_CANNOT_EMPTY("s320050099","数据列表不可为空", I18NStringEnum.s90.getI18nKey()),
    NPATH_FILE_DOWNLOAD_FAILED("s320050100","NPATH文件下载失败", I18NStringEnum.s91.getI18nKey()),
    QYWX_NOT_PRIVILEGE_ERROR("s320050105", "指定的成员/部门/标签参数无权限", I18NStringEnum.s92.getI18nKey()),
    CRM_USER_UPPER_LIMIT_INITED("s320050106","贵企业总账号已达上限", I18NStringEnum.s93.getI18nKey()),
    QYWX_UNBIND_FAILED("s320050107","解绑失败", I18NStringEnum.s94.getI18nKey()),
    INVALID_CALL("s320050108","非法调用", I18NStringEnum.s95.getI18nKey()),
    DATABASE_RETURN_NULL("s320050109","数据库返回为空", I18NStringEnum.s96.getI18nKey()),
    IMPORT_EMP_ONLY_SUPPORT_THIRD_CRM_APP("s320050110","人员导入功能，仅支持三方CRM应用场景，不支持纯代开发应用场景", I18NStringEnum.s198.getI18nKey()),
    ;

    private String code;        //返回码
    private String qywxCode;        //企业微信返回码
    private String i18nKey;

    ErrorRefer(String code, String qywxCode, String i18nKey) {
        this.code = code;
        this.qywxCode = qywxCode;
        this.i18nKey = i18nKey;
    }

    public String getQywxCode() {
        return qywxCode;
    }

    public String getCode() {
        return code;
    }

    public String getI18nKey() {
        return i18nKey;
    }
//    ErrorRefer(String code){
//        this.code = code;
//    }
}
