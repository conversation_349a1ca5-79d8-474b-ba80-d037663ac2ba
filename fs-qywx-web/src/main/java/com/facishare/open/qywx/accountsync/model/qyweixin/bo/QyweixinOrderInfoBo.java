package com.facishare.open.qywx.accountsync.model.qyweixin.bo;

import lombok.Data;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * Created on 2019/2/15
 */
@Data
@Table(name = "qyweixin_order_info")
public class QyweixinOrderInfoBo {

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 订单状态。0-未支付(可能也表示已关闭、未支付且已过期、申请退款中)，1-已支付，2-已关闭， 3-未支付且已过期， 4-申请退款中， 5-申请退款成功
     */
    private Integer orderStatus;

    /**
     * 订单类型。0-普通订单，1-扩容订单，2-续期，3-版本变更
     */
    private Integer orderType;

    /**
     * 客户企业的corpid
     */
    private String paidCorpid;

    /**
     *  下单操作人员userid。如果是服务商代下单，没有该字段。
     */
    private String operatorId;

    /**
     * 应用id
     */
    private String suiteId;

    /**
     * 购买版本ID
     */
    private String editionId;

    /**
     *  购买版本名字
     */
    private String editionName;

    /**
     *  实付款金额，单位分
     */
    private Long price;

    /**
     *  购买的人数
     */
    private Integer userCount;

    /**
     *  购买的时间，单位天
     */
    private Integer orderPeriod;

    /**
     *  下单时间
     */
    private Long orderTime;

    /**
     *  付款时间
     */
    private Long paidTime;
    /**
     * 购买有效时间开始
     */
    private Long beginTime;
    /**
     * 购买有效时间结束
     */
    private Long endTime;

    //下单来源。0-客户下单；1-服务商代下单；2-代理商代下单
    private Integer orderFrom;

    //下单方corpid
    private String operatorCorpid;
    /**
     *  订单处理状态，0：无需推送已处理 :1：未推送未处理，2：已推送已处理，3：未推送已处理（企业开通失败并退款）
     */
    private Integer processingStatus;

}
