package com.facishare.open.qywx.accountinner.service;

import com.facishare.open.feishu.syncapi.model.EmployeeBindModel;
import com.facishare.open.feishu.syncapi.model.PageModel;
import com.facishare.open.qywx.accountinner.arg.QueryBindArg;
import com.facishare.open.qywx.accountinner.arg.QueryOutUnbindArg;
import com.facishare.open.qywx.accountinner.arg.QueryUnBindArg;
import com.facishare.open.qywx.accountinner.model.ContactBindInfo;
import com.facishare.open.qywx.accountinner.model.QyweixinDepartmentBindModel;
import com.facishare.open.qywx.accountinner.model.QyweixinEmployeeBindModel;
import com.facishare.open.qywx.accountinner.model.qywx.QyweixinDepartmentBind;
import com.facishare.open.qywx.accountinner.model.qywx.QyweixinEmployeeBind;
import com.facishare.open.qywx.accountinner.result.ExportDataResult;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinCorpIdInfo;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinDeptAndEmpSyncInfo;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinUserDetailInfoRsp;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.fxiaoke.common.Pair;

import java.util.List;
import java.util.Map;

/**
 * 通讯录绑定服务
 * Created by liuwei on 2018/09/25
 */
public interface ContactBindInnerService {

    //Result<Object> getContactAppBind(String fsEa, String outEa);

    //Result<List<SimpleContactBindInfo>> getWXAccounts(String enterpriseAccount, String type, String outEa);

    Result<List<ContactBindInfo>> automaticAccountMactch(String fsEa, Integer employeeId, String outEa);

    Result<String> saveAccountBind(String fsEa, List<String> bindList, String outEa);

    Result<String> deleteAccountBind(String fsEa, List<String> fsEmployeeAccountList, String outEa);

    //Result<String> updateAccountBind(String fsEa, List<String> bindList, String outEa);

    //Result<List<ContactBindInfo>> getAccountBind(String fsEa, int bindType, Integer employeeId, String outEa, String outDepId);

    Result<Object> deleteQYWXBind(Map<String, String> request);

//    //检测通讯录是否取消授权
//    Boolean checkContactAuth(String contactAppId, String corpId);

    /**
     * 异步通讯录id转译 获取jobId
     * @param ea 纷享ea
     */
    Result<String> getJobId(String ea);

    /**
     * 通过企业微信侧jobId获取下载链接
     */
    Result<Pair<Integer, String>> getTranslateUrl(String ea, String jobId);

    Result<Integer> getAutoBind(String fsEa, String outEa);

    Result<Integer> saveAutoBind(String fs_ea, String outEa, int flag);

    Result<Integer> getAutoContactBind();

    Result<Integer> saveAutoContactBind(String fs_ea, Integer employeeId, String outEa);

    Result<String> queryCorpName2(String fsEa, String outEa, String outDepId);

    Result<QyweixinCorpIdInfo> queryOutEaByFsEa(String fsEa);

    //Result<Integer> updateEmployeeAccountBind(List<QyweixinAccountEmployeeMapping> employeeMappings, QyweixinAccountEnterpriseMapping qyweixinAccountEnterpriseMapping);

    void getTagEmployeeListAndSave(String outEa);

    Result<Void> uploadDepartmentsAndEmployeesFile(List<QyweixinDeptAndEmpSyncInfo> deptAndEmpSyncInfos, Integer i);

    Result<Void> changAuthEventToBindAccount(String fsEa, String corpId);

    Result<Void> listenMqEventToBindAccount(String fsEa, String corpId, EmployeeDto employeeDto);

    Result<Void> autoBindEmpAccount(String fsEa, String corpId);

    Result<List<QyweixinUserDetailInfoRsp>> getAllEmployeeList(String outEa, String rootDepId);

    Result<PageModel<List<EmployeeBindModel.FsEmployee>>> queryFsUnbind(QueryUnBindArg arg);

    Result<PageModel<List<EmployeeBindModel>>> queryBind(QueryBindArg arg);

    Result<List<EmployeeBindModel.OutEmployee>>  queryOutUnbind(QueryOutUnbindArg arg);

    Result<List<QyweixinEmployeeBindModel>> queryEmployeeBind(QyweixinEmployeeBind queryEmployeeBind);

    Result<Integer> saveEmployeeBind(QyweixinEmployeeBind queryEmployeeBind);

    Result<Integer> updateEmployeeBind(QyweixinEmployeeBind queryEmployeeBind);

    Result<List<QyweixinDepartmentBindModel>> queryDepartmentBind(QyweixinDepartmentBind queryDepartmentBind);

    Result<Integer> saveDepartmentBind(QyweixinDepartmentBind queryDepartmentBind);

    Result<Integer> updateDepartmentBind(QyweixinDepartmentBind queryDepartmentBind);

    Result<ExportDataResult> exportEmployeeBind(String fsEa, String outEa, Integer userId, String lang);
    Result<Void> addUserList(String appId, String outEa, List<String> outUserIdList);

    Result<Void> addUser(String appId, String outEa, String outUserId);
}
