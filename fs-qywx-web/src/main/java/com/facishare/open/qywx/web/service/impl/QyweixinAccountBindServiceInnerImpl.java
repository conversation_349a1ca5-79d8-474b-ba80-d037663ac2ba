package com.facishare.open.qywx.web.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.qywx.accountbind.arg.AccountSyncDataArg;
import com.facishare.open.qywx.accountbind.arg.QyweixinEmpRebindArg;
import com.facishare.open.qywx.accountbind.arg.QyweixinEmpUnbindArg;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.db.dao.QyweixinAccountDepartmentBindDao;
import com.facishare.open.qywx.web.db.dao.QyweixinAccountEmployeeBindDao;
import com.facishare.open.qywx.web.db.dao.QyweixinAccountEnterpriseBindDao;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountDepartmentMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.web.mq.sender.MQSender;
import com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer;
import com.facishare.open.qywx.accountinner.enums.AccountSyncTypeEnum;
import com.facishare.open.qywx.accountinner.model.AccountSyncConfigModel;
import com.facishare.open.qywx.accountinner.model.OaconnectorEventDateChangeProto;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.core.enums.CloudProxyEnum;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.model.CloudMessageProxyProto;
import com.facishare.open.qywx.accountsync.model.EnterpriseModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpBindBo;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.uc.api.model.enterprise.arg.BatchGetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.EnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by liuwei on 2018/7/18.
 */
@Slf4j
@Service("qyweixinAccountBindInnerService")
public class QyweixinAccountBindServiceInnerImpl implements QyweixinAccountBindInnerService {

    @Autowired
    private QyweixinAccountEmployeeBindDao qyweixinAccountEmployeeBindDao;

    @Autowired
    private QyweixinAccountEnterpriseBindDao qyweixinAccountEnterpriseBindDao;

    @Autowired
    private QyweixinAccountDepartmentBindDao qyweixinAccountDepartmentBindDao;

    @Resource
    private EnterpriseEditionService enterpriseEditionService;

    @Resource(name = "accountSyncDataSender")
    private AutoConfRocketMQProducer accountSyncDataSender;

    @Resource(name = "employeeAccountBindSender")
    private AutoConfRocketMQProducer employeeAccountBindSender;

    @Autowired
    private MQSender mqSender;

    @Resource
    private EIEAConverter eieaConverter;

    @Autowired
    private QyweixinAccountEnterpriseBindDao accountEnterpriseBindDao;

    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;

    /**
     *
     * @param sourceType
     * @param corpId corpId可能普通应用的corpId，也可能是代开发应用对应的corpId
     * @param userId userId可能是加密的，也可能不是
     * @return
     */
    @Override
    public Result<String> outAccountToFsAccount(String sourceType, String corpId, String userId,String fsEa) {
        //查询普通人员关系
        List<QyweixinAccountEmployeeMapping> employeeMappingList = qyweixinAccountEmployeeBindDao.queryAccountBindList(
                corpId,
                userId,0,fsEa);
        log.info("QyweixinAccountBindServiceInnerImpl.outAccountToFsAccount,out_ea={},employeeMappingList={}",
                corpId,employeeMappingList);

        if(CollectionUtils.isNotEmpty(employeeMappingList)){
            return new Result<>(employeeMappingList.get(0).getFsAccount());
        }

        return new Result<>();
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> getEmployeeMapping(String corpId, String userId, int status, String fsEa) {
        //查询普通人员关系
        List<QyweixinAccountEmployeeMapping> employeeMappingList = qyweixinAccountEmployeeBindDao.queryAccountBindList(
                corpId, userId, status, fsEa);
        log.info("QyweixinAccountBindServiceInnerImpl.getEmployeeMapping,out_ea={},employeeMappingList={}",corpId,employeeMappingList);

        return new Result<>(employeeMappingList);
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> outAccountToFsAccount(String sourceType, String corpId, List<String> fsAccountList) {
        List<QyweixinAccountEmployeeMapping> result = qyweixinAccountEmployeeBindDao.queryMappingWithFsAccounts(sourceType, corpId, -1, fsAccountList);
        log.info("QyweixinAccountBindServiceInnerImpl.outAccountToFsAccount success. corpId:{}, fsAccountList:{}, result:{}", corpId, fsAccountList, result);
        return new Result<>(result);
    }

    @Override
    public Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBind(String sourceType, String fsEa,
                                                                              List<Integer> fsDepartmentIds) {
        List<QyweixinAccountDepartmentMapping> result = qyweixinAccountDepartmentBindDao.queryByFsDepartmentIds(sourceType, fsEa, fsDepartmentIds);
        log.info("QyweixinAccountBindServiceInnerImpl.queryDepartmentBind success. fsEa:{}, fsDepartmentIds:{}, result:{}", fsEa, fsDepartmentIds, result);
        return new Result<>(result);
    }

    @Override
    public int deleteAccountBind(String fsAdminAccount, List<String> fsAccountList, String appId, String outEa) {
        log.info("trace deleteAccountBind getFsAccountList result:{}", fsAccountList);
        if(!fsAccountList.isEmpty()){
            int deleteCount = 0;
            List<QyweixinAccountEmployeeMapping> accountEmployeeMappings = qyweixinAccountEmployeeBindDao.queryMappingFromFsAccount(SourceTypeEnum.QYWX.getSourceType(), -1, fsAccountList,outEa);
            Map<String, QyweixinAccountEmployeeMapping> employeeInfoMaps = accountEmployeeMappings.stream().collect(Collectors.toMap(QyweixinAccountEmployeeMapping::getFsAccount,
                    Function.identity(),(v1, v2) -> v1));
            for (String fsAccount : fsAccountList){
                if (!fsAccount.equals(fsAdminAccount)) {  //要解绑的员工不能是企业开通管理员
                    int delete = qyweixinAccountEmployeeBindDao.deleteEmployeeMapping(SourceTypeEnum.QYWX.getSourceType(), fsAccount, appId, outEa);
                    deleteCount += delete;
                    if(delete > 0) {
                        log.info("QyweixinAccountBindServiceImpl.deleteAccountBind,fsAccountList={},accountEmployeeMappings={}.", fsAccountList, accountEmployeeMappings);
                        Message message = new Message();
                        message.setTags(ConfigCenter.EMPLOYEE_UNBIND);
                        QyweixinEmpUnbindArg body = new QyweixinEmpUnbindArg();
                        QyweixinAccountEmployeeMapping employeeMapping = employeeInfoMaps.get(fsAccount);
                        List<String> fsList = Splitter.on(".").splitToList(employeeMapping.getFsAccount());
                        String ea = fsList.get(1);
                        String fsUserId = fsList.get(2);
                        body.setEa(ea);
                        body.setCorpId(employeeMapping.getOutEa());
                        body.setFsUserId(fsUserId);
                        body.setQwUserId(employeeMapping.getOutAccount());
                        message.setBody(body.toProto());
                        if(ConfigCenter.TEM_CLOUD_EA.contains(ea)) {
                            CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
                            cloudMessageProxyProto.setType(CloudProxyEnum.employeeAccountBindSender.name());
                            cloudMessageProxyProto.setFsEa(ea);
                            cloudMessageProxyProto.setCorpId(employeeMapping.getOutEa());
                            cloudMessageProxyProto.setMessage(message);
                            //跨云
                            mqSender.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(ea), cloudMessageProxyProto);
                        } else {
                            SendResult sendResult = employeeAccountBindSender.send(message);
                            log.info("QyweixinAccountBindServiceImpl.deleteAccountBind,sendResult={},message={}.", sendResult, message);
                        }
                    }
                }
            }
            if(fsAccountList.size()==deleteCount)
                return deleteCount;
        }
        return 0;
    }

    @Deprecated
    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> queryAccountBind(List<String> fsAccountList,String appId) {
        return queryAccountBind2(fsAccountList, appId, null);
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> queryAccountBind2(List<String> fsAccountList, String appId, String outEa) {
        List<QyweixinAccountEmployeeMapping> qyweixinAccountEmployeeMappings =
                qyweixinAccountEmployeeBindDao.queryMappingFromFsAccountBatch(SourceTypeEnum.QYWX.getSourceType(),
                        appId, 0, fsAccountList,outEa);
        return new Result<>(qyweixinAccountEmployeeMappings);
    }

    @Override
    public int updateQyweixinAccountEmployee(QyweixinAccountEmployeeMapping qyweixinAccountEmployeeMapping) {
        return qyweixinAccountEmployeeBindDao.update(qyweixinAccountEmployeeMapping);
    }

    @Override
    public int updateQyweixinAccountEmployee(QyweixinAccountEmployeeMapping qyweixinAccountEmployeeMapping, String qwOldUserId) {
        int count = qyweixinAccountEmployeeBindDao.update(qyweixinAccountEmployeeMapping);
        if(count > 0) {
            log.info("QyweixinAccountBindServiceImpl.updateQyweixinAccountEmployee,qyweixinAccountEmployeeMapping={},qwOldUserId={}.", qyweixinAccountEmployeeMapping, qwOldUserId);
            Message message = new Message();
            message.setTags(ConfigCenter.EMPLOYEE_REBIND);
            QyweixinEmpRebindArg body = new QyweixinEmpRebindArg();
            List<String> fsList = Splitter.on(".").splitToList(qyweixinAccountEmployeeMapping.getFsAccount());
            String ea = fsList.get(1);
            String fsUserId = fsList.get(2);
            body.setEa(ea);
            body.setCorpId(qyweixinAccountEmployeeMapping.getOutEa());
            body.setFsUserId(fsUserId);
            body.setQwNewUserId(qyweixinAccountEmployeeMapping.getOutAccount());
            body.setQwOldUserId(qwOldUserId);
            message.setBody(body.toProto());
            if(ConfigCenter.TEM_CLOUD_EA.contains(ea)) {
                CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
                cloudMessageProxyProto.setType(CloudProxyEnum.employeeAccountBindSender.name());
                cloudMessageProxyProto.setFsEa(ea);
                cloudMessageProxyProto.setCorpId(qyweixinAccountEmployeeMapping.getOutEa());
                cloudMessageProxyProto.setMessage(message);
                //跨云
                mqSender.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(ea), cloudMessageProxyProto);
            } else {
                SendResult sendResult = employeeAccountBindSender.send(message);
                log.info("QyweixinAccountBindServiceImpl.updateQyweixinAccountEmployee,sendResult={},message={}.", sendResult, message);
            }
        }
        return count;
    }

    @Deprecated
    @Override
    public List<QyweixinAccountEmployeeMapping> queryAccountBindByFsEa(String fsEa, String appId) {
        return queryAccountBindByFsEa2(fsEa, appId, null);
    }

    @Override
    public List<QyweixinAccountEmployeeMapping> queryAccountBindByFsEa2(String fsEa, String appId, String outEa) {
        return qyweixinAccountEmployeeBindDao.queryAccountBindByFsEa("E."+fsEa+".", appId, outEa);
    }

    @Override
    public List<QyweixinAccountEmployeeMapping> queryAccountBindByOutEa(String outEa, String appId, int status) {
        return qyweixinAccountEmployeeBindDao.queryAccountBindByOutEa(outEa, appId, status);
    }

    @Deprecated
    @Override
    public List<String> queryIsvAccountBindByFsEaOnlyOutAccount(String fsEa, String appId) {
        return queryIsvAccountBindByFsEaOnlyOutAccount2(fsEa,appId,null);
    }

    @Override
    public List<String> queryIsvAccountBindByFsEaOnlyOutAccount2(String fsEa, String appId, String outEa) {
        return qyweixinAccountEmployeeBindDao.queryIsvAccountBindByFsEaOnlyOutAccount("E."+fsEa+".",appId, outEa);
    }

    @Override
    public int countAccountBind(String fsEa,String appId) {
        return qyweixinAccountEmployeeBindDao.countAccountBind("E."+fsEa+".",appId);
    }

    @Override
    public List<QyweixinAccountEnterpriseMapping> queryEnterpriseBindByOutEa(String corpId) {
        return qyweixinAccountEnterpriseBindDao.queryEaMappingFromOutEa(SourceTypeEnum.QYWX.getSourceType(), corpId,null,0, ConfigCenter.crm_domain);
    }

    @Override
    public List<QyweixinAccountEnterpriseMapping> queryEnterpriseAllBindByOutEa(String corpId) {
        return qyweixinAccountEnterpriseBindDao.queryEaMappingFromOutEa(SourceTypeEnum.QYWX.getSourceType(), corpId,null,0, null);
    }

    @Override
    public Result<QyweixinAccountEnterpriseMapping> queryCorpBindByOutEa(String corpId,String depId) {
        if(StringUtils.isEmpty(depId)) {
            depId = "1";
        }
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountEnterpriseBindDao.queryEaMappingFromOutEa(SourceTypeEnum.QYWX.getSourceType(),
                corpId,
                depId,
                -1,
                ConfigCenter.crm_domain);
        if(CollectionUtils.isNotEmpty(enterpriseMappingList)) {
            return new Result<>(enterpriseMappingList.get(0));
        }
        return new Result<>(null);
    }

    @Override
    public int deleteQYWXBindByOutEa(List<String> outEaList, String appId) {
        outEaList.stream().forEach( v -> {
            if(!ConfigCenter.MAIN_ENV) {
                //跨云必须传ei，所以得确定身份
                List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountEnterpriseBindDao.queryEaMappingFromOutEa("qywx", v, null, -1, ConfigCenter.crm_domain);
                if(CollectionUtils.isNotEmpty(enterpriseMappingList)) {
                    OaconnectorEventDateChangeProto proto = new OaconnectorEventDateChangeProto();
                    proto.setOutEa(v);
                    proto.setAppId(appId);
                    proto.setEventType("oaconnector_enterprise_bind");
                    proto.setType("deleteQYWXBindByOutEa");
                    proto.setDomain(ConfigCenter.crm_domain);
                    mqSender.sendOaconnectorEventDataChangeMQ(ChannelEnum.qywx.name(), proto, String.valueOf(eieaConverter.enterpriseAccountToId(enterpriseMappingList.get(0).getFsEa())));
                }
            }

            qyweixinAccountEmployeeBindDao.deleteByOutEa(v, appId);
            qyweixinAccountDepartmentBindDao.deleteByOutEa(v, appId);
            qyweixinAccountEnterpriseBindDao.deleteByOutEa(v);
        });
        return 0;
    }

    @Override
    public Result<Void> deleteQYWXAccountBind(String fsEa, String appId, String corpId) {
        List<String> installedAppList = new ArrayList<>();
        installedAppList.add(appId);
        Result<List<QyweixinCorpBindBo>> repAppList = qyweixinGatewayInnerService.getRepAppList(corpId);
        log.info("deleteQYWXAccountBind,repAppList={}",repAppList);
        if(CollectionUtils.isNotEmpty(repAppList.getData())) {
            for(QyweixinCorpBindBo corpBindBo : repAppList.getData()) {
                installedAppList.add(corpBindBo.getAppId());
            }
        }
        log.info("deleteQYWXAccountBind,installedAppList={}",installedAppList);
        for(String appId2 : installedAppList) {
            qyweixinAccountEmployeeBindDao.deleteEmployeeBind(corpId, fsEa, appId2);
            qyweixinAccountDepartmentBindDao.deleteDepartmentBind(corpId, fsEa,appId2);
        }
        qyweixinAccountEmployeeBindDao.deleteEmployeeBind(corpId, fsEa, appId);
        qyweixinAccountDepartmentBindDao.deleteDepartmentBind(corpId, fsEa,appId);
        qyweixinAccountEnterpriseBindDao.deleteEnterpriseBind(corpId, fsEa);

        if(!ConfigCenter.MAIN_ENV) {
            OaconnectorEventDateChangeProto proto = new OaconnectorEventDateChangeProto();
            proto.setOutEa(corpId);
            proto.setAppId(appId);
            proto.setFsEa(fsEa);
            proto.setEventType("oaconnector_enterprise_bind");
            proto.setType("deleteQYWXAccountBind");
            proto.setDomain(ConfigCenter.crm_domain);
            mqSender.sendOaconnectorEventDataChangeMQ(ChannelEnum.qywx.name(), proto, String.valueOf(eieaConverter.enterpriseAccountToId(fsEa)));
        }
        return new Result<>();
    }

    @Deprecated
    @Override
    public Result<QyweixinAccountEnterpriseMapping> getEnterpriseMapping(String fs_ea) {
        return getEnterpriseMapping2(fs_ea,null);
    }

    @Override
    public Result<QyweixinAccountEnterpriseMapping> getEnterpriseMapping2(String fsEa, String outEa) {
        return new Result<>(qyweixinAccountEnterpriseBindDao.queryEaMappingFromFsEa("qywx", fsEa, outEa, ConfigCenter.crm_domain));
    }

    @Deprecated
    @Override
    public Result updateAccountSyncConfig(String fs_ea,List<AccountSyncConfigModel> modelList) {
        return updateAccountSyncConfig2(fs_ea,modelList,null);
    }

    @Override
    public Result updateAccountSyncConfig2(String fs_ea, List<AccountSyncConfigModel> modelList, String outEa) {
        QyweixinAccountEnterpriseMapping mapping = qyweixinAccountEnterpriseBindDao.queryEaMappingFromFsEa("qywx",fs_ea,outEa, ConfigCenter.crm_domain);
        if(mapping==null) {
            return Result.newInstance(ErrorRefer.CORP_ACCOUNT_NOT_BIND);
        }

        String syncAccountTime = null;
        String syncLeadsTime = null;
        String syncContactTime = null;
        Boolean isSend = false;
        if(StringUtils.isNotEmpty(mapping.getAccountSyncConfig())) {
            List<AccountSyncConfigModel> list = JSONObject.parseArray(mapping.getAccountSyncConfig(),AccountSyncConfigModel.class);
            Map<AccountSyncTypeEnum, AccountSyncConfigModel> accountSyncConfigModelMaps = list.stream().collect(Collectors.toMap(AccountSyncConfigModel::getSyncType,
                    Function.identity(),(v1,v2) -> v1));
            for (AccountSyncConfigModel model : modelList) {
                if (model.isAutoSync()) {
                    if(model.getSyncType().equals(AccountSyncTypeEnum.SYNC_TO_ACCOUNT)) {
                        if("null".equals(String.valueOf(mapping.getAccountSyncConfigTime()))
                                || accountSyncConfigModelMaps.get(AccountSyncTypeEnum.SYNC_TO_ACCOUNT).isAutoSync() == Boolean.FALSE) {
                            syncAccountTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
                            isSend = true;
                            //获取到后跳出循环
                            break;
                        }
                    }
                    if(model.getSyncType().equals(AccountSyncTypeEnum.SYNC_TO_LEADS)) {
                        if("null".equals(String.valueOf(mapping.getLeadsSyncConfigTime()))
                                || accountSyncConfigModelMaps.get(AccountSyncTypeEnum.SYNC_TO_LEADS).isAutoSync() == Boolean.FALSE) {
                            syncLeadsTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
                            isSend = true;
                            //获取到后跳出循环
                            break;
                        }
                    }
                    if(model.getSyncType().equals(AccountSyncTypeEnum.SYNC_TO_CONTACT)) {
                        if("null".equals(String.valueOf(mapping.getContactSyncConfigTime()))
                                || accountSyncConfigModelMaps.get(AccountSyncTypeEnum.SYNC_TO_CONTACT).isAutoSync() == Boolean.FALSE) {
                            syncContactTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
                            isSend = true;
                            //获取到后跳出循环
                            break;
                        }
                    }
                } else {
                    if(model.getSyncType().equals(AccountSyncTypeEnum.SYNC_TO_ACCOUNT)) {
                        if(accountSyncConfigModelMaps.get(AccountSyncTypeEnum.SYNC_TO_ACCOUNT).isAutoSync() == Boolean.TRUE) {
                            syncAccountTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
                            //获取到后跳出循环
                            break;
                        }
                    }
                    if(model.getSyncType().equals(AccountSyncTypeEnum.SYNC_TO_LEADS)) {
                        if(accountSyncConfigModelMaps.get(AccountSyncTypeEnum.SYNC_TO_LEADS).isAutoSync() == Boolean.TRUE) {
                            syncLeadsTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
                            //获取到后跳出循环
                            break;
                        }
                    }
                    if(model.getSyncType().equals(AccountSyncTypeEnum.SYNC_TO_CONTACT)) {
                        if(accountSyncConfigModelMaps.get(AccountSyncTypeEnum.SYNC_TO_CONTACT).isAutoSync() == Boolean.TRUE) {
                            syncContactTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
                            //获取到后跳出循环
                            break;
                        }
                    }
                }
            }
        } else {
            //只有第一次使用这个功能时才会走这一步
            for (AccountSyncConfigModel model : modelList) {
                if(model.isAutoSync()) {
                    if(model.getSyncType().equals(AccountSyncTypeEnum.SYNC_TO_ACCOUNT) && "null".equals(String.valueOf(mapping.getAccountSyncConfigTime()))) {
                        syncAccountTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
                        //获取到后跳出循环
                        isSend = true;
                        break;
                    }
                    if(model.getSyncType().equals(AccountSyncTypeEnum.SYNC_TO_LEADS) && "null".equals(String.valueOf(mapping.getLeadsSyncConfigTime()))) {
                        syncLeadsTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
                        //获取到后跳出循环
                        isSend = true;
                        break;
                    }
                    if(model.getSyncType().equals(AccountSyncTypeEnum.SYNC_TO_CONTACT) && "null".equals(String.valueOf(mapping.getContactSyncConfigTime()))) {
                        syncContactTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
                        //获取到后跳出循环
                        isSend = true;
                        break;
                    }
                }
            }
        }

        log.info("QyweixinAccountBindServiceInnerImpl.updateAccountSyncConfig,ea={},syncAccountTime={},syncLeadsTime={},syncContactTime={},isSend={}.", fs_ea, syncAccountTime, syncLeadsTime, syncContactTime, isSend);
        String json = JSONObject.toJSONString(modelList);
        int count = qyweixinAccountEnterpriseBindDao.updateAccountSyncConfig(mapping.getId(),json, syncAccountTime, syncLeadsTime, syncContactTime);
        if (count != 1)
            return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
        //为防止保存失败的情况，在这里发送mq
        try {
            if(StringUtils.isNotEmpty(syncAccountTime) && isSend) {
                this.SyncAccountData("AccountObj", fs_ea, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(syncAccountTime).getTime(), ObjectUtils.isNotEmpty(mapping.getAccountSyncConfigTime())? mapping.getAccountSyncConfigTime().getTime() : null);
            }
            if(StringUtils.isNotEmpty(syncLeadsTime) && isSend) {
                this.SyncAccountData("LeadsObj", fs_ea, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(syncLeadsTime).getTime(), ObjectUtils.isNotEmpty(mapping.getLeadsSyncConfigTime())? mapping.getLeadsSyncConfigTime().getTime() : null);
            }
            if(StringUtils.isNotEmpty(syncContactTime) && isSend) {
                this.SyncAccountData("ContactObj", fs_ea, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(syncContactTime).getTime(), ObjectUtils.isNotEmpty(mapping.getContactSyncConfigTime())? mapping.getContactSyncConfigTime().getTime() : null);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return new Result();
    }

    private void SyncAccountData(String apiName, String fsEa, Long upTime, Long downTime) {
        if(StringUtils.isEmpty(apiName) || StringUtils.isEmpty(fsEa)) {
            return;
        }
        AccountSyncDataArg body = new AccountSyncDataArg();
        body.setApiName(apiName);
        body.setEa(fsEa);
        body.setUpTime(upTime);
        body.setDownTime(downTime);
        Message message = new Message();
        message.setBody(body.toProto());
        if(ConfigCenter.TEM_CLOUD_EA.contains(fsEa)) {
            CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
            cloudMessageProxyProto.setType(CloudProxyEnum.accountSyncDataSender.name());
            cloudMessageProxyProto.setFsEa(fsEa);
            cloudMessageProxyProto.setMessage(message);
            //跨云
            mqSender.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(fsEa), cloudMessageProxyProto);
        } else {
            SendResult sendResult = accountSyncDataSender.send(message);
            log.info("QyweixinAccountBindServiceInnerImpl.updateAccountSyncConfig,ea={},apiName={},sendResult={},message={}.", fsEa, apiName, sendResult, message);
        }
    }

    @Override
    public Result<List<EnterpriseModel>> getFsEaList(String outEa,String outUserId) {
        //查询全部企业绑定关系
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = accountEnterpriseBindDao.queryEaMappingFromOutEa("qywx", outEa,null,-1, null);
        //为兼容老逻辑，如果是纷享云的企业，查询是否存在人员绑定关系，调用这个方法的都是纷享云的请求
        List<String> eaList;
        boolean hasDomain = enterpriseMappingList.stream()
                .anyMatch(mapping -> ConfigCenter.crm_domain.equals(mapping.getDomain()));
        log.info("QyweixinAccountBindServiceInnerImpl.getFsEaList,outEa={},hasDomain={}", outEa, hasDomain);
        if(hasDomain) {
            List<QyweixinAccountEmployeeMapping> employeeMappingList = qyweixinAccountEmployeeBindDao.queryAccountBindList(outEa,
                    outUserId, 0, null);
            if(CollectionUtils.isEmpty(employeeMappingList)) return new Result<>();

            eaList = employeeMappingList.stream().map((item)->{
                List<String> itemList = Splitter.on(".").splitToList(item.getFsAccount());
                return itemList.get(1);
            }).collect(Collectors.toList());
        } else {
            eaList = enterpriseMappingList.stream()
                    .map(QyweixinAccountEnterpriseMapping::getFsEa)
                    .collect(Collectors.toList());
        }

        return new Result<>(batchGetEnterpriseData(eaList));
    }

    private List<EnterpriseModel> batchGetEnterpriseData(List<String> eaList) {
        if(CollectionUtils.isEmpty(eaList)) return new ArrayList<>();

        BatchGetEnterpriseDataArg arg = new BatchGetEnterpriseDataArg();
        arg.setEnterpriseAccounts(eaList);
        BatchGetEnterpriseDataResult result = enterpriseEditionService.batchGetEnterpriseData(arg);

        List<EnterpriseModel> modelList = new ArrayList<>();
        for(EnterpriseData data : result.getEnterpriseDatas()) {
            EnterpriseModel model = new EnterpriseModel();
            model.setEa(data.getEnterpriseAccount());
            model.setName(data.getEnterpriseName());
            modelList.add(model);
        }

        return modelList;
    }
    @Override
    public Result<List<String>> getFsEaList(String outEa) {
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountEnterpriseBindDao.queryEaMappingFromOutEa(SourceTypeEnum.QYWX.getSourceType(), outEa, null, -1, ConfigCenter.crm_domain);
        if(CollectionUtils.isEmpty(enterpriseMappingList)) return new Result<>();

        List<String> eaList = enterpriseMappingList.stream().map(QyweixinAccountEnterpriseMapping::getFsEa).collect(Collectors.toList());
        return new Result<>(eaList);
    }

    @Override
    public Result<Integer> updateExtend(String fsEa, String extend) {
        return updateExtend2(fsEa,extend,null);
    }

    @Override
    public Result<Integer> updateExtend2(String fsEa, String extend, String outEa) {
        return new Result<>(qyweixinAccountEnterpriseBindDao.updateExtend(fsEa, extend, outEa));
    }

    @Deprecated
    @Override
    public Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindV2(String sourceType, String fsEa, String appId, Integer status, List<Integer> fsDepartmentIds) {
        return queryDepartmentBindV21(sourceType, fsEa, appId, status, fsDepartmentIds, null);
    }

    @Override
    public Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindV21(String sourceType,
                                                                                 String fsEa,
                                                                                 String appId,
                                                                                 Integer status,
                                                                                 List<Integer> fsDepartmentIds,
                                                                                 String outEa) {
        List<QyweixinAccountDepartmentMapping> result = qyweixinAccountDepartmentBindDao.queryByFsDepartmentIdsV2(sourceType, fsEa, appId, status, fsDepartmentIds, outEa);
        log.info("QyweixinAccountBindServiceInnerImpl.queryDepartmentBindV21 success. fsEa:{}, fsDepartmentIds:{}, result:{}", fsEa, fsDepartmentIds, result);
        return new Result<>(result);
    }

    @Override
    public Result<Integer> updateEnterpriseOutInfo(int id, String outEa, String outDepId) {
        return new Result<>(qyweixinAccountEnterpriseBindDao.updateEnterpriseOutInfo(id, outEa, outDepId));
    }

    @Override
    public Result<Integer> updateEnterpriseDomain(String fsEa, String domain) {
        return new Result<>(qyweixinAccountEnterpriseBindDao.updateEnterpriseDomain(fsEa, domain));
    }

    @Override
    public Result<List<QyweixinAccountEnterpriseMapping>> queryEnterpriseMappingListFromFsEa(String source, String fsEa, Integer status) {
        return new Result<>(qyweixinAccountEnterpriseBindDao.queryEnterpriseMappingListFromFsEa(source, fsEa, status));
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> queryEmployeeMappingListFromFsUserId(String source,
                                                                                             String fsEa,
                                                                                             String fsUserId) {
        String fsAccount = "E."+fsEa+"."+fsUserId;
        return new Result<>(qyweixinAccountEmployeeBindDao.queryMappingFromFsAccount("qywx",
                -1,
                Lists.newArrayList(fsAccount),
                null));
    }
}
