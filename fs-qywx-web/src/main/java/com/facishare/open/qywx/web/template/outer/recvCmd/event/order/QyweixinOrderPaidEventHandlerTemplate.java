package com.facishare.open.qywx.web.template.outer.recvCmd.event.order;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.order.OrderPaidEventHandlerTemplate;
import com.facishare.open.feishu.syncapi.model.event.FeishuOrderPaidEvent;
import com.facishare.open.feishu.syncapi.service.OrderService;
import com.facishare.open.qywx.web.manager.OrderManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class QyweixinOrderPaidEventHandlerTemplate extends OrderPaidEventHandlerTemplate {
    @Resource
    private QyweixinSaveOrderHandlerTemplate qyweixinSaveOrderHandlerTemplate;
    @Resource
    private OrderManager orderManager;


    @Override
    public void openEnterpriseOrSaveOrder(MethodContext context) {
        log.info("QyweixinOrderPaidEventHandlerTemplate.openEnterpriseOrSaveOrder,context={}",context);
        //企微只有保存订单到crm
        context.setResult(TemplateResult.newSuccess(SAVE_ORDER));
    }

    @Override
    public void openEnterprise(MethodContext context) {

    }

    @Override
    public void saveOrder(MethodContext context) {
        log.info("QyweixinOrderPaidEventHandlerTemplate.saveOrder,context={}",context);

        Map<String,Object> data = context.getData();

        String orderType = (String) data.get("orderType");
        String suiteId = (String) data.get("suiteId");
        String orderId = (String) data.get("orderId");
        String newOrderId = (String) data.get("newOrderId");
        String oldOrderId = (String) data.get("oldOrderId");

        if(orderType.equals("open_order")) {
            orderManager.openOrderEvent(suiteId, orderId);
        } else if(orderType.equals("change_order")) {
            orderManager.changeOrderEvent(suiteId, newOrderId, oldOrderId);
        } else if(orderType.equals("pay_for_app_success")) {
            //保存订单
            qyweixinSaveOrderHandlerTemplate.execute(data);
        } else if(orderType.equals("refund")) {
            orderManager.refundEvent(suiteId, orderId);
        }
    }
}
