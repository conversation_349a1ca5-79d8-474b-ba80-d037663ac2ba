package com.facishare.open.qywx.web.manager;

import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.result.Result;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.web.utils.DateUtils;
import com.facishare.open.qywx.accountsync.utils.xml.TagChangeEventXml;
import com.facishare.open.qywx.accountsync.utils.xml.XStreamUtils;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class OANewBaseManager {
    @Resource
    private QyweixinAccountBindService qyweixinAccountBindService;
    @ReloadableProperty("oa.bind.time.greater")
    private String oaBindTimeGreater;

    /**
     * 合并到新基座后，需要路由到新基座处理
     */
    public Boolean canRunInNewBase(String outEa,String plainMsg) {
        log.info("EnterpriseWeChatEventHandler.canRunInNewBaseEnv,outEa={},plainMsg={}",outEa,plainMsg);
        //ea为空，有可能是代开发应用的事件，需要重新解析
        if (StringUtils.isEmpty(outEa)) {
            TagChangeEventXml tagChangeEventXml = XStreamUtils.parseXml(plainMsg, TagChangeEventXml.class);
            log.info("EnterpriseWeChatEventHandler.canRunInNewBaseEnv,tagChangeEventXml={}", tagChangeEventXml);
            if(StringUtils.isNotEmpty(tagChangeEventXml.getToUserName())) {
                outEa = tagChangeEventXml.getToUserName();
            }
        }

        if(StringUtils.isNotEmpty(outEa)) {
            boolean runInNewBase = canRunInNewBaseEnv(outEa, null);
            log.info("EnterpriseWeChatEventHandler.canRunInNewBaseEnv,outEa={}",outEa);
            return runInNewBase;
        } else {
            log.info("EnterpriseWeChatEventHandler.canRunInNewBaseEnv,outEa is null, outEa={},plainMsg={}",outEa,plainMsg);
            if(StringUtils.containsIgnoreCase(plainMsg,"<InfoType><![CDATA[create_auth]]></InfoType>")
                    || StringUtils.containsIgnoreCase(plainMsg,"<InfoType><![CDATA[pay_for_app_success]]></InfoType>")) {
                //这两个事件由于没有outEa，所以无法判断，所以只能走新基座处理
                return true;
            }
            //其余事件都走老基座
            return false;
        }
    }

    /**
     * 判断当前企业是否可以走灰度环境
     * @param outEa
     * @return
     */
    private boolean canRunInNewBaseEnv(String outEa, String fsEa) {

        //判断企业创建日期是否大于指定的灰度日期
        log.info("OANewBaseManager.canRunInNewBaseEnv,oaBindTimeGreater={}", oaBindTimeGreater);
        Date date = null;
        if (StringUtils.isNotEmpty(oaBindTimeGreater)) {
            date = DateUtils.parseDate(oaBindTimeGreater, "yyyy-MM-dd");
            if (date == null) {
                return false;
            }
            log.info("OANewBaseManager.canRunInNewBaseEnv,query enterprise mapping,grayOutEaBindTimeGreater={}", date);

            Result<List<QyweixinAccountEnterpriseMapping>> result;
            if(StringUtils.isNotEmpty(outEa)) {
                result = qyweixinAccountBindService.selectEnterpriseBind(SourceTypeEnum.QYWX.getSourceType(),
                        outEa);
                log.info("OANewBaseManager.canRunInNewBaseEnv,query enterprise mapping,outEa={},result={}", outEa,result);
            } else {
                result = qyweixinAccountBindService.fsEaToOutEaResultList(SourceTypeEnum.QYWX.getSourceType(),
                        fsEa);
                log.info("OANewBaseManager.canRunInNewBaseEnv,query enterprise mapping,fsEa={},result={}", fsEa,result);
            }

            if (CollectionUtils.isEmpty(result.getData())) {
                //没有绑定关系，走新基座
                return true;
            }

            QyweixinAccountEnterpriseMapping enterpriseMapping = result.getData().get(0);
            if(enterpriseMapping.getGmtCreate().getTime() > date.getTime()) {
                log.info("OANewBaseManager.canRunInNewBaseEnv,grayOutEaBindTimeGreater,outEa={}", outEa);
                return true;
            }
        }
        return false;
    }

    public Boolean canRunInNewBaseByFsEa(String fsEa) {
        boolean isNew = canRunInNewBaseEnv(null, fsEa);
        log.info("OANewBaseManager.canRunInNewBaseByFsEa,fsEa={},isNew={}",fsEa,isNew);
        return isNew;
    }

    public Boolean canRunInNewBaseByOutEa(String outEa) {
        boolean isNew = canRunInNewBaseEnv(outEa, null);
        log.info("OANewBaseManager.canRunInNewBaseByOutEa,outEa={},isNew={}",outEa,isNew);
        return isNew;
    }
}
