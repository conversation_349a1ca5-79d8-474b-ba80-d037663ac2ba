package com.facishare.open.huawei.kit.web.template.outer.event.order;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.order.SaveOrderHandlerTemplate;
import com.facishare.open.huawei.kit.web.model.HuaweiOrderDataModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.huawei.kit.web.service.HuaweiOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class HuaweiSaveOrderHandlerTemplate extends SaveOrderHandlerTemplate {
    @Resource
    private HuaweiOrderService huaweiOrderService;

    @Override
    public void saveOrderAndAddCrmOrder(MethodContext context) {
        log.info("HuaweiSaveOrderHandlerTemplate.saveOrderAndAddCrmOrder,context={}",context);
        HuaweiOrderDataModel huaweiOrderDataModel = context.getData();

        Result<Void> result = huaweiOrderService.saveOrderAndAddCrmOrder(huaweiOrderDataModel);
        log.info("HuaweiSaveOrderHandlerTemplate.saveOrderAndAddCrmOrder,result={}",result);
    }
}
