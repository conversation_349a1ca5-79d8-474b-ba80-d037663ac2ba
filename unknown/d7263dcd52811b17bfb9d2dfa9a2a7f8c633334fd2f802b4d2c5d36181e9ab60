package com.facishare.open.huawei.kit.web.controller.outer;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.syncapi.arg.QueryEmployeeBindArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.huawei.kit.web.info.AuthSyncInfo;
import com.facishare.open.feishu.syncapi.info.EmployeeBindInfo;
import com.facishare.open.huawei.kit.web.info.HuaweiUserInfoVo;
import com.facishare.open.huawei.kit.web.service.EmployeeBindService;
import com.facishare.open.huawei.kit.web.service.EnterpriseBindService;
import com.facishare.open.huawei.kit.web.service.HuaweiContactsService;
import com.facishare.open.huawei.kit.web.service.HuaweiLoginService;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.facishare.userlogin.api.model.CreateUserTokenDto;
import com.facishare.userlogin.api.model.UserTokenDto;
import com.facishare.userlogin.api.service.SSOLoginService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("huaweikit2/external")
public class HuaweiExternalController {
    @Autowired
    private HuaweiLoginService huaweiLoginService;
    @Resource
    private EnterpriseBindService enterpriseBindService;
    @Autowired
    private SSOLoginService ssoLoginService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private HuaweiContactsService huaweiContactsService;
    @Resource
    private I18NStringManager i18NStringManager;

    @RequestMapping(value = "/loginAuth", produces = {"application/json;charset=UTF-8;"})
    @ResponseBody
    public void loginAuth(@RequestParam("tenant") String tenantId,
                           @RequestParam("code") String code,
                           @RequestHeader("user-agent") String userAgent,
                           HttpServletResponse response,
                           HttpServletRequest request) throws ServletException, IOException {
        //免登录
        log.info("HuaweiExternalController.loginAuth,tenant:{},code:{}",tenantId,code);

        com.facishare.open.feishu.syncapi.result.Result<List<EnterpriseBindEntity>> enterpriseBindListResult = enterpriseBindService.getEnterpriseBindList(tenantId);
        if(!enterpriseBindListResult.isSuccess() || CollectionUtils.isEmpty(enterpriseBindListResult.getData())) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg","开通中"); //ignorei18n
            request.setAttribute("propose", "请关闭页面，重新登陆进去纷享crm"); //ignorei18n
            request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
            return;
        }

        //多语
        Integer ei = eieaConverter.enterpriseAccountToId(enterpriseBindListResult.getData().get(0).getFsEa());
        String lang = i18NStringManager.getDefaultLang(String.valueOf(ei));
        if(StringUtils.isEmpty(lang)) {
            lang = "zh-CN";
        }

        i18NStringManager.setDefaultRequestScope(request, lang);

        Result<HuaweiUserInfoVo> userByCode = huaweiLoginService.getUserByCode(code, tenantId);

        log.info("HuaweiExternalController.loginAuth,userByCode:{}",userByCode);

        if (ObjectUtils.isEmpty(userByCode.getData())||!userByCode.isSuccess()) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg",i18NStringManager.get(I18NStringEnum.s140, lang, String.valueOf(ei)));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s65, lang, String.valueOf(ei)));
            request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
            return;
        }

        QueryEmployeeBindArg queryEmployeeBindArg = new QueryEmployeeBindArg();
        queryEmployeeBindArg.setFsEa(enterpriseBindListResult.getData().get(0).getFsEa());
        queryEmployeeBindArg.setOutEa(enterpriseBindListResult.getData().get(0).getOutEa());
        queryEmployeeBindArg.setOutUserId(userByCode.getData().getUserName());

        com.facishare.open.feishu.syncapi.result.Result<EmployeeBindInfo> employeeBindInfoResult = employeeBindService.queryEmployeeBind(ChannelEnum.huawei.name(), queryEmployeeBindArg);
        if(!employeeBindInfoResult.isSuccess() || ObjectUtils.isEmpty(employeeBindInfoResult.getData())) {
            //再次开通
            AuthSyncInfo.User user = new AuthSyncInfo.User();
            user.setUserName(userByCode.getData().getUserName());
            user.setName(userByCode.getData().getName());
            user.setOrgCode(userByCode.getData().getOrganizationCode());
            user.setRole(userByCode.getData().getRole());
            user.setEnable("true");
            user.setMobile(userByCode.getData().getMobile());
            Result<Void> employeeInfoResult = huaweiContactsService.createEmployee(tenantId, user);
            if(employeeInfoResult.isSuccess()) {
                employeeBindInfoResult = employeeBindService.queryEmployeeBind(ChannelEnum.huawei.name(), queryEmployeeBindArg);
            } else {
                if(employeeInfoResult.getCode() == ResultCodeEnum.CRM_USER_UPPER_LIMIT_INITED.getCode()) {
                    request.setAttribute("errorCode", "s320050002");
                    request.setAttribute("errorMsg", String.format(i18NStringManager.get(I18NStringEnum.s141, lang, String.valueOf(ei)), tenantId, userByCode.getData().getUserId()));
                    request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s69, lang, String.valueOf(ei)));
                    request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
                    return;
                }
            }

            if(!employeeBindInfoResult.isSuccess() || ObjectUtils.isEmpty(employeeBindInfoResult.getData())) {
                request.setAttribute("errorCode", "s320050002");
                request.setAttribute("errorMsg",i18NStringManager.get(I18NStringEnum.s142, lang, String.valueOf(ei)));
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s143, lang, String.valueOf(ei)));
                request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
                return;
            }
        }

//        Integer ei = eieaConverter.enterpriseAccountToId(enterpriseBindListResult.getData().get(0).getFsEa());
        Integer fsUserId = Integer.valueOf(employeeBindInfoResult.getData().getFsUserId());
        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(ei, Integer.valueOf(fsUserId)));
        CreateUserTokenDto.Result userToken = ssoLoginService.createUserToken(userTokenArg);
        log.info("ssoLogin token :{}", userToken);
        CreateUserTokenDto.LoginStatus loginStatus = userToken.getLoginStatus();
        String fsToken = userToken.getToken();
        if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus || StringUtils.isEmpty(fsToken)) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg",i18NStringManager.get(I18NStringEnum.s144, lang, String.valueOf(ei)));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s65, lang, String.valueOf(ei)));
            request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
            return;
        }
        String redirectUrl = String.format(ConfigCenter.crm_domain + ConfigCenter.SSO_REDIRECT_URL, fsToken);
        log.info("redirectUrl[{}]", redirectUrl);
        if (userAgent.contains("app")) {
            redirectUrl = redirectUrl.concat("&source=").concat(ConfigCenter.SSO_H5_URL);
            log.info("aliapp:{},redirectUrl:{}", userAgent, redirectUrl);
        }

        response.sendRedirect(redirectUrl);
    }
}
