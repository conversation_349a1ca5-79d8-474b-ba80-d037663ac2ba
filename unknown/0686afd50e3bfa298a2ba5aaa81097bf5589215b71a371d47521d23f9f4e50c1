package com.facishare.open.ding.provider.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.common.utils.BeanUtil;
import com.facishare.open.ding.provider.arg.DingCorpMappingEntity;
import com.facishare.open.ding.provider.config.ConfigCenter;
import com.facishare.open.ding.provider.dao.DingCorpMappingDao;
import com.facishare.open.feishu.syncapi.config.GlobalValue;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.model.EnterpriseExtendModel;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/7 16:54
 * @Version 1.0
 */
@Service("dingCorpMappingServiceImpl")
@Slf4j
public class DingCorpMappingServiceImpl implements DingCorpMappingService {
    @Autowired
    private DingCorpMappingDao corpMappingDao;


    @Override
    public Result<Integer> insertCorpMapping(DingCorpMappingVo dingCorpMappingVo) {
        int count = corpMappingDao.insertCorpMapping(dingCorpMappingVo);
        return Result.newSuccess(count);
    }

    @Override
    public Result<List<DingCorpMappingVo>> queryCorpMappingByCorpId(String dingCorpId) {
        DingCorpMappingEntity dingCorpMappingVo=new DingCorpMappingEntity();
        dingCorpMappingVo.setDingCorpId(dingCorpId);
        List<DingCorpMappingEntity> byEntity = corpMappingDao.findByEntity(dingCorpMappingVo);
        List<DingCorpMappingVo> voList = BeanUtil.copy(byEntity, DingCorpMappingVo.class);
        return Result.newSuccess(voList);
    }

    @Override
    public Result<DingCorpMappingVo> queryMappingByAppId(String dingCorpId, Long appId) {
        DingCorpMappingEntity dingCorpMappingVo=new DingCorpMappingEntity();
        dingCorpMappingVo.setDingCorpId(dingCorpId);
        dingCorpMappingVo.setAppCode(appId);
        List<DingCorpMappingEntity> appCorpResult = corpMappingDao.findByEntity(dingCorpMappingVo);
        List<DingCorpMappingVo> voList = BeanUtil.copy(appCorpResult, DingCorpMappingVo.class);
        if(CollectionUtils.isNotEmpty(voList)){
            return Result.newSuccess(voList.get(0));
        }
        return Result.newSuccess();
    }

    @Override
    public Result<DingCorpMappingVo> queryMappingByConnector(String dingCorpId, Long appCode, int connector) {
        DingCorpMappingEntity dingCorpMappingVo=new DingCorpMappingEntity();
        dingCorpMappingVo.setDingCorpId(dingCorpId);
        dingCorpMappingVo.setAppCode(appCode);
        dingCorpMappingVo.setConnector(connector);
        List<DingCorpMappingEntity> appCorpResult = corpMappingDao.findByEntity(dingCorpMappingVo);
        List<DingCorpMappingVo> voList = BeanUtil.copy(appCorpResult, DingCorpMappingVo.class);
        if(CollectionUtils.isNotEmpty(voList)){
            return Result.newSuccess(voList.get(0));
        }
        return Result.newSuccess();
    }

    @Override
    public Result<String> getToken(String dingCorpId) {

        return null;
    }

    @Override
    public Result<Integer> updateRepeatIndex(Integer ei) {
        Integer count = corpMappingDao.updateCorpMapping(ei);
        return Result.newSuccess(count);
    }

    @Override
    public Result<Integer> updateInitStatus(Integer ei) {
        Integer count = corpMappingDao.updateInitStatus(ei);
        return Result.newSuccess(count);
    }

    @Override
    public int update(DingCorpMappingVo dingCorpMappingVo) {
        DingCorpMappingEntity entity = new DingCorpMappingEntity();
        BeanUtils.copyProperties(dingCorpMappingVo,entity);
        return corpMappingDao.update(entity);
    }

    @Override
    public DingCorpMappingVo queryByConnector(int connector,int ei,Long appCode) {
        return corpMappingDao.queryByConnector(connector,ei,appCode);
    }

    @Override
    public Result<List<DingCorpMappingVo>> queryByEi(Integer ei) {
        List<DingCorpMappingVo> vos = corpMappingDao.queryByEi(ei);
        return Result.newSuccess(vos);
    }

    @Override
    public Result<List<DingCorpMappingVo>> queryByEa(String ea) {
        List<DingCorpMappingVo> mappingVos = corpMappingDao.queryByEa(ea);
        return Result.newSuccess(mappingVos);
    }

    @Override
    public Result<Void> updateEnterpriseExtend(String fsEa, String extendField, Object extendValue) {
        //通过fsEa查询企业绑定关系
        DingCorpMappingVo dingCorpMappingVo = corpMappingDao.queryByEaAndAppCode(fsEa, ConfigCenter.APP_CRM_ID);
        if(ObjectUtils.isEmpty(dingCorpMappingVo)) {
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        //获取字段
        EnterpriseExtendModel extendModel = new Gson().fromJson(StringUtils.isNotEmpty(dingCorpMappingVo.getExtend()) ? dingCorpMappingVo.getExtend() : GlobalValue.enterprise_extend, EnterpriseExtendModel.class);
        //更新字段，要先判断字段是否符合
        switch(extendField) {
            case "isFirstLand":
                if(StringUtils.equalsIgnoreCase(extendValue.toString(), Boolean.FALSE.toString())
                        || StringUtils.equalsIgnoreCase(extendValue.toString(), Boolean.TRUE.toString())) {
                    extendModel.setIsFirstLand(Boolean.valueOf(extendValue.toString()));
                }
                break;
            case "isRetainInformation":
                if(StringUtils.equalsIgnoreCase(extendValue.toString(), Boolean.FALSE.toString())
                        || StringUtils.equalsIgnoreCase(extendValue.toString(), Boolean.TRUE.toString())) {
                    extendModel.setIsRetainInformation(Boolean.valueOf(extendValue.toString()));
                }
                break;
            default:
                log.info("EnterpriseBindServiceImpl.updateEnterpriseExtend,This field is not supported,extendField={}",extendField);
                return Result.newError(ResultCode.PARAMS_ERROR);
        }
        //保存数据
        corpMappingDao.updateExtend(fsEa, new Gson().toJson(extendModel), ConfigCenter.APP_CRM_ID);
        return Result.newSuccess();
    }
}
