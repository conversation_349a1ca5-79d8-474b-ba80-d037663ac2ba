package com.facishare.open.huawei.kit.web.service

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alibaba.fastjson.TypeReference
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult
import com.facishare.open.feishu.syncapi.arg.CreateCustomerAndUpdateMappingArg
import com.facishare.open.feishu.syncapi.arg.CreateOrderArg
import com.facishare.open.feishu.syncapi.config.ConfigCenter
import com.facishare.open.feishu.syncapi.result.Result
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum
import com.facishare.open.huawei.kit.web.handler.NewInstanceEventHandler
import com.facishare.open.huawei.kit.web.info.HuaweiOrderInfo
import com.facishare.open.huawei.kit.web.model.HuaweiOrderDataModel
import com.facishare.open.huawei.kit.web.result.OrderInfoResult
import com.facishare.open.huawei.kit.web.template.outer.event.order.HuaweiOpenEnterpriseHandlerTemplate
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData
import com.facishare.open.huawei.kit.web.utils.HuaweiApiUtil
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils
import com.google.gson.reflect.TypeToken
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/fsHuaweiKitWebAppContext-test.xml")
class HuaweiOrderServiceTest extends Specification {
    @Resource
    private HuaweiOrderService huaweiOrderService;
    @Autowired
    private HuaweiLoginService huaweiLoginService;
    @Resource
    private RedisDataSource redisDataSource;
    @Resource
    private HuaweiOpenEnterpriseHandlerTemplate huaweiOpenEnterpriseHandlerTemplate;
    @Resource
    private NewInstanceEventHandler newInstanceEventHandler;

    def "getSersaveOrderviceAuth"() {
        expect:

        HuaweiOrderDataModel huaweiOrderDataModel = new HuaweiOrderDataModel()
        huaweiOrderDataModel.setTenantId("382fd33e2ee64c26bc0ed2ab62bbd42c")
        huaweiOrderDataModel.setEnterpriseName("大风车")
        huaweiOrderDataModel.setInstanceId("47e255f4-f0e7-45ef-b46b-2e00fba3a4aa")
        huaweiOrderDataModel.setOrderId("CS2411151523ARO0M")

        Result<HuaweiOrderInfo> a = huaweiOrderService.saveOrder(huaweiOrderDataModel)



//        def a = huaweiOrderService.saveOrder(huaweiOrderDataModel)
        print(a)
    }

    def "createCustomerAndUpdateMapping"() {
        expect:
//        HuaweiOrderDataModel huaweiOrderDataModel = new HuaweiOrderDataModel()
//        huaweiOrderDataModel.setTenantId("68cbc86abc2018ab880d92f36422fa0e")
//        huaweiOrderDataModel.setEnterpriseName("huaweitenantnamename")
//        huaweiOrderDataModel.setInstanceId("huaiweitest1234567")
//        huaweiOrderDataModel.setOrderId("MOCKPERIODYEARNEW")
        CreateCustomerAndUpdateMappingArg arg = new CreateCustomerAndUpdateMappingArg()
        arg.setFsEa("huaweitest122")
        arg.setEnterpriseName("huaweitenantname122")
        arg.setOutEa("68cbc86abc2018ab880d92f36422fa0e222")
        arg.setOutEid("68cbc86abc2018ab880d92f36422fa0e222")
        arg.setInstallerMobilePhone("1000000000")
        arg.setInstallerUserId("123456")
        arg.setInstallerName("namename")


        def a = huaweiOrderService.createCustomerAndUpdateMapping(arg)
        print(a)
    }

    def "createOrder"() {
        expect:
        HuaweiOrderDataModel huaweiOrderDataModel = new HuaweiOrderDataModel()
        huaweiOrderDataModel.setTenantId("68cbc86abc2018ab880d92f36422fa0e")
        huaweiOrderDataModel.setEnterpriseName("huaweitenantname")
        huaweiOrderDataModel.setInstanceId("huaiweitest123456")
        huaweiOrderDataModel.setOrderId("MOCKPERIODYEARNEW")

        CreateCustomerAndUpdateMappingArg arg = new CreateCustomerAndUpdateMappingArg()
        arg.setFsEa("huaweitest")
        arg.setEnterpriseName("huaweitenantname")
        arg.setOutEa("68cbc86abc2018ab880d92f36422fa0e1")
        arg.setOutEid("68cbc86abc2018ab880d92f36422fa0e1")
        arg.setInstallerMobilePhone("1000000000")
        arg.setInstallerUserId("123456")
        arg.setInstallerName("namename")

        CreateOrderArg createOrderArg = new CreateOrderArg();
        createOrderArg.setFsEa("huaiweitest0001");
        createOrderArg.setOutEa("huaiweiinsstancetest0001");
        createOrderArg.setOrderId("MOCKPERIODYEARNEW");

        def a = huaweiOrderService.createOrder(createOrderArg)
        print(a)
    }

    def "getUserByCode"() {
        expect:
        KitVerifyTemplateData huaweiOuterKitTemplate = new KitVerifyTemplateData();
        TemplateResult result = huaweiOpenEnterpriseHandlerTemplate.execute(huaweiOuterKitTemplate);
        println result;

        def a = huaweiLoginService.getUserByCode("QhSD4_su9YWLyfFCcP81X-Qt53yOkddjlHlZO3Uw7ow", "07454853c9800f6c0f3dc001d861de20_34f07482")
        print(a)
    }

    def "saveOrderAndAddCrmOrder"() {
        expect:
        HuaweiOrderDataModel model = new HuaweiOrderDataModel()
        model.setOrderId("MOCKMONTYRENEW")
        model.setTenantId("huaiweiinsstancetest0007")
        model.setInstanceId("huaiweitest0007")


        def a = huaweiOrderService.saveOrderAndAddCrmOrder(model)
        print(a)
    }

    def "newInstanceEventHandler"() {
        given:
        KitVerifyTemplateData kitVerifyTemplateData = new KitVerifyTemplateData();

        Map<String, Object> isvProduceReq = new HashMap<>();
        isvProduceReq.put("businessId", "huaiweitestzy004")
        //orderId
        isvProduceReq.put("orderId", "MOCKPERIODYEARNEW")
        kitVerifyTemplateData.setIsvProduceReq(isvProduceReq)
        expect:
        def a = newInstanceEventHandler.handle(kitVerifyTemplateData)
        print(a)
    }
}
