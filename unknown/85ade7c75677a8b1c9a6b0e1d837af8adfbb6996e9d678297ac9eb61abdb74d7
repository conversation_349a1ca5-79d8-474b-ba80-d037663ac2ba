package com.facishare.open.ding.provider.service;

import com.facishare.open.ding.api.result.AppAuthResult;
import com.facishare.open.ding.api.service.AppAuthService;
import com.facishare.open.ding.api.vo.AppAuthVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.utils.BeanUtil;
import com.facishare.open.ding.provider.arg.AppAuthEntity;
import com.facishare.open.ding.provider.dao.AppAuthDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/10 16:24
 * @Version 1.0
 */
@Service("appAuthServiceImpl")
public class AppAuthServiceImpl implements AppAuthService {

    @Autowired
    private AppAuthDao appAuthDao;

    @Override
    public Result<Integer> insertAuth(AppAuthVo appAuthVo) {
        AppAuthEntity authEntity = BeanUtil.copy(appAuthVo, AppAuthEntity.class);
        int count = appAuthDao.insertAppAuth(authEntity);
        return Result.newSuccess(count);
    }

    @Override
    public Result<List<AppAuthResult>> conditionAppAuth(String dingCorpId, Long appCode, Long suiteId) {
        List<AppAuthResult> conditionResult = appAuthDao.conditionAppAuth(dingCorpId, appCode, suiteId);
        return Result.newSuccess(conditionResult);
    }


}
