package com.facishare.open.ding.provider.service;

import com.facishare.open.ding.api.result.PollingSyncResult;
import com.facishare.open.ding.api.service.PollingSyncService;
import com.facishare.open.ding.api.vo.PollingSyncDataVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.utils.BeanUtil;
import com.facishare.open.ding.provider.arg.PollingSyncDataEntity;
import com.facishare.open.ding.provider.dao.PollingSyncDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2021/5/11 17:11
 * @Version 1.0
 */
@Service("pollingSyncServiceImpl")
public class PollingSyncServiceImpl implements PollingSyncService {

    @Autowired
    private PollingSyncDao pollingSyncDao;
    @Override
    public Result<PollingSyncResult> queryPolling(Integer syncType) {
        PollingSyncResult pollingSyncResults = pollingSyncDao.queryAllResult(syncType);
        return  Result.newSuccess(pollingSyncResults);
    }

    @Override
    public Result<Integer> insertData(PollingSyncDataVo pollingSyncDataVo) {
        PollingSyncDataEntity pollingSyncDataEntity = BeanUtil.copy(pollingSyncDataVo, PollingSyncDataEntity.class);
        int count = pollingSyncDao.insert(pollingSyncDataEntity);
        return Result.newSuccess(count);
    }

    @Override
    public Result<Integer> updateSync(PollingSyncDataVo pollingSyncDataVo) {
        int count = pollingSyncDao.updateSyncTime(pollingSyncDataVo.getLastSyncTime().getTime(),pollingSyncDataVo.getEventLevel());
        return Result.newSuccess(count);
    }
}
