package com.facishare.open.huawei.kit.web.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.huawei.kit.web.BaseTest;
import com.facishare.open.huawei.kit.web.mapper.AppInfoMapper;
import com.facishare.open.feishu.syncapi.entity.AppInfoEntity;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class AppInfoManagerTest extends BaseTest {
    @Resource
    private AppInfoMapper appInfoMapper;
    @Test
    public void selectAll() {
        AppInfoEntity entity = AppInfoEntity.builder()
                .appId("cli_a20192f6afb8d00c")
                .tenantKey("11b1a8c266da9758")
                .build();

//        int insert = appInfoMapper.insert(entity);
//        System.out.println(insert);

        LambdaQueryWrapper<AppInfoEntity> wrapper = new LambdaQueryWrapper<>();
        List<AppInfoEntity> list = appInfoMapper.selectList(wrapper);
        System.out.println(list);
    }
}
