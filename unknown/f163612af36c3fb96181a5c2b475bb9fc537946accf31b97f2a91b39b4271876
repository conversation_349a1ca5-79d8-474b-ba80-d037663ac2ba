package com.facishare.open.ding.provider.service;

import com.facishare.open.ding.api.model.DingRefuseDataModel;
import com.facishare.open.ding.api.service.cloud.DingRefuseDataService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.utils.BeanUtil;
import com.facishare.open.ding.provider.arg.DingRefuseDataEntity;
import com.facishare.open.ding.provider.dao.DingRefuseDataDao;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @Date 2021/10/30 17:22
 * @Version 1.0
 */
@Service("dingRefuseDataServiceImpl")
public class DingRefuseDataServiceImpl implements DingRefuseDataService {

    @Autowired
    private DingRefuseDataDao dingRefuseDataDao;

    @Override
    public Result<Integer> insertOrderData(DingRefuseDataModel dingRefuseDataModel) {
        DingRefuseDataEntity dingRefuseDataEntity=new DingRefuseDataEntity();
        BeanUtils.copyProperties(dingRefuseDataModel,dingRefuseDataEntity);
        Integer count = dingRefuseDataDao.insertData(dingRefuseDataEntity);
        return Result.newSuccess(count);
    }

    @Override
    public Result<Integer> queryRefuseDataModel(String dingCorpId, String suiteId) {
        Integer refuseCount = dingRefuseDataDao.queryRefuseData(dingCorpId, suiteId);
        return Result.newSuccess(refuseCount);
    }

    @Override
    public Result<Integer> releaseForbid(String dingCorpId) {
        Integer updateStatus = dingRefuseDataDao.updateStatus(0, dingCorpId);
        return Result.newSuccess(updateStatus);
    }
}
