package com.facishare.open.ding.provider.service.cloud;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.open.ding.api.enums.OperationStatusEnum;
import com.facishare.open.ding.api.enums.OperationTypeEnum;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.cloud.CloudDeptService;
import com.facishare.open.ding.api.service.cloud.CloudDingRequestService;
import com.facishare.open.ding.api.service.cloud.CloudEmpService;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.provider.constants.Constant;
import com.facishare.open.ding.provider.crm.CrmRestManager;
import com.facishare.open.ding.provider.entity.LogWriteVo;
import com.facishare.open.ding.provider.manager.DingDeptMananger;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2021/5/14 12:00 钉钉应用回调部门事件，支持多应用回调的时候，还是需要依赖数据库是否已经存在该部门的映射来判断是否创建或者更新
 * @Version 1.0
 */
@Service(value = "cloudDeptServiceImpl")
@Slf4j
public class CloudDeptServiceImpl implements CloudDeptService {

    @Autowired
    private DingDeptMananger dingDeptMananger;
    @Autowired
    private CrmRestManager crmRestManager;
    @Autowired
    private CloudDingRequestService requestService;
    @Autowired
    private DingMappingEmployeeManager dingMappingEmployeeManager;
    @Autowired
    private DingCorpMappingService dingCorpMappingService;
    @Autowired
    private CloudEmpService cloudEmpService;
    private static final String Symbol = "__";

    @Override
    public Result<DeptVo> queryDept(Integer ei, Long deptId) {
        DeptVo deptVo = dingDeptMananger.queryByDingId(ei, deptId);
        return Result.newSuccess(deptVo);
    }

    @Override
    public Result<Integer> createDept(Dept dept, String dingCorpId, Integer ei,String suiteId) {
        DeptVo deptVo = dingDeptMananger.queryByDingId(ei, dept.getId());
        if (ObjectUtils.isNotEmpty(deptVo)) return Result.newSuccess();
        //查询父级部门
        Integer count = 0;
        DeptVo deptPatent = dingDeptMananger.queryByDingId(ei, dept.getParentid());
        if (ObjectUtils.isEmpty(deptPatent) && ObjectUtils.isNotEmpty(dept.getParentid())) {
            //查询没有创建的上级部门并创建
            count = createNotFoundDept(dingCorpId, ei, dept.getId(),suiteId);
            return Result.newSuccess(count);
        } else {
            Integer parentCrmId = ObjectUtils.isNotEmpty(deptPatent) ? deptPatent.getCrmDeptId() : Constant.TREE_PARENT_ID;
            count = createCrmDept(dept, parentCrmId, ei,suiteId);
        }
        return Result.newSuccess(count);
    }

    //查询所有的上级部门，没有创建的创建
    private Integer createNotFoundDept(String dingCorpId, Integer ei, Long deptId,String suiteId) {
        Result<List<Dept>> parentResult = requestService.queryListParentById(dingCorpId, deptId,suiteId);
        Integer count = 0;
        if (CollectionUtils.isNotEmpty(parentResult.getData())) {
            for (Dept datum : parentResult.getData()) {
                //查询父级部门
                DeptVo deptVo = dingDeptMananger.queryByDingId(ei, datum.getId());
                if (ObjectUtils.isEmpty(deptVo)) {
                    DeptVo parentVo = dingDeptMananger.queryByDingId(ei, datum.getParentid());
                    Integer parentId = Constant.TREE_PARENT_ID;
                    if (ObjectUtils.isNotEmpty(parentVo)) {
                        parentId = ObjectUtils.isEmpty(parentVo.getDingParentId()) ? Constant.TREE_PARENT_ID : parentVo.getCrmDeptId();
                    }
                    Integer temp = createCrmDept(datum, parentId, ei,suiteId);
                    count += temp;
                }
            }
        }
        return count;
    }


    @Override
    public Result<Integer> modifyDept(Dept dept, String dingCorpId, Integer ei,String suiteId) {
        DeptVo deptVo = dingDeptMananger.queryByDingId(ei, dept.getId());
        if (ObjectUtils.isEmpty(deptVo)) {
            return createDept(dept, dingCorpId, ei,suiteId);
        }
        //这边做个过滤如果判断名字是否一致，以前缀的"__"名字是否一致
        if ((ObjectUtils.isNotEmpty(dept.getParentid())&&ObjectUtils.isNotEmpty(deptVo.getDingParentId())&&(!dept.getParentid().equals(deptVo.getDingParentId())) )
                || suffixName(dept.getName(), deptVo.getName()) || compareDeptOwner(dept.getDeptOwner(), deptVo.getDingDeptOwner())
                || isModifyDeptOwner(deptVo.getCrmDeptOwner(), dept.getDeptOwner())) {
            //如果上级部门变动，需要更新crm的上级部门
            DeptVo deptParent = dingDeptMananger.queryByDingId(ei, dept.getParentid());
            Integer parentId = Constant.TREE_PARENT_ID;
            if (ObjectUtils.isNotEmpty(deptParent)) {
                parentId = Optional.ofNullable(deptParent.getCrmDeptId()).orElse(Constant.TREE_PARENT_ID);
            }
            //查询部门管理员
            Result<DingMappingEmployeeResult> dingMappingEmployeeResultResult = dingMappingEmployeeManager.queryEmpByDingUserId(ei, dept.getDeptOwner());
            Integer crmEmployeeId = dingMappingEmployeeResultResult.getData() == null ? null : dingMappingEmployeeResultResult.getData().getEmployeeId();
            deptVo.setName(deptValidName(dept.getName()));
            deptVo.setDingParentId(dept.getParentid());
            deptVo.setDingDeptOwner(dept.getDeptOwner());
            deptVo.setCrmParentId(parentId);
            deptVo.setCrmDeptOwner(crmEmployeeId);
            Result<Void> modifyResult = crmRestManager.modifyDept(deptVo);
            if (modifyResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode()) {
                //如果部门名称已经存在
                Map<String, Object> sameMap = fixCallBackName(ei, dept);
                if (Boolean.valueOf(sameMap.get("same").toString())) {
                    //如果是true，名字前缀不一致，通过index后缀修改
                    //更新部门上下级关系
                    deptVo.setName(sameMap.get("name").toString());
                    modifyResult = crmRestManager.modifyDept(deptVo);
                    dept.setName(sameMap.get("name").toString());
                }
            }
            //写入日志
            Integer statusCode = modifyResult.getErrorCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
            LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.UPDATE.getType(), deptVo.getName(), statusCode, modifyResult.getErrorMessage());

            //更新数据库
            //更新名字/上级部门/主管
            deptVo.setName(dept.getName());
            deptVo.setDingParentId(dept.getParentid());
            deptVo.setDingDeptOwner(dept.getDeptOwner());
            deptVo.setCrmDeptOwner(crmEmployeeId);
            deptVo.setCrmParentId(parentId);
            deptVo.setCrmDeptId(deptVo.getCrmDeptId());
            Result<Integer> updateDept = dingDeptMananger.updateDept(deptVo);
            return updateDept;
        }
        return Result.newSuccess();
    }


    @Override
    public Result<Integer> removeDept(Long deptId, String dingCorpId, Integer ei) {
        DeptVo deptVo = dingDeptMananger.queryByDingId(ei, deptId);
        if (ObjectUtils.isEmpty(deptVo)) return Result.newSuccess();
        Result<Void> stopDept = crmRestManager.stopDept(deptVo);
        Result<Integer> deleteResult = dingDeptMananger.deleteDept(ei, deptId);
        return deleteResult;
    }


    private Integer createCrmDept(Dept dept, Integer parentId, Integer ei,String suiteId) {
        DeptVo vo = new DeptVo();
        vo.setEi(ei);
        vo.setDingDeptId(dept.getId());
        vo.setDingParentId(dept.getParentid());
        vo.setName(deptValidName(dept.getName()));
        vo.setDingDeptOwner(dept.getDeptOwner());
        vo.setCrmParentId(parentId);
        Result<DingMappingEmployeeResult> queryResult = dingMappingEmployeeManager.queryEmpByDingUserId(ei, dept.getDeptOwner());
        Integer crmEmployeeId = queryResult.getData() == null ? null : queryResult.getData().getEmployeeId();
        //查询负责人,负责人预先创建
        vo.setCrmDeptOwner(crmEmployeeId);
        Result<Integer> createDeptResult = crmRestManager.createDept(vo);
        Map<String, Object> sameMap = Maps.newHashMap();
        if (createDeptResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode()) {
            sameMap = fixCallBackName(ei, dept);
            if (Boolean.valueOf(sameMap.get("same").toString())) {
                //创建部门
                vo.setName(sameMap.get("name").toString());
                createDeptResult = crmRestManager.createDept(vo);
                dept.setName(sameMap.get("name").toString());
            }
        }
        if (createDeptResult.getErrorCode() == ResultCode.DEPT_NAME_IS_EXIST.getErrorCode() || !createDeptResult.isSuccess()) {
            //如果处理完同名的部门后，还是同名就返回
            Integer statusCode = createDeptResult.getErrorCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
            return 0;
        }
        vo.setCrmDeptId(createDeptResult.getData());
        vo.setCrmParentId(parentId);
        vo.setUpdateTime(new Date());
        Integer count = dingDeptMananger.addDeptByEi(vo);
        if (count != 0) {
            log.info("insert ding_dept row:{},ei:{},deptVo:{}", count, ei, vo);
        }
        Integer statusCode = createDeptResult.getErrorCode() == ResultCode.SUCCESS.getErrorCode() ? OperationStatusEnum.SYNC_SUCCESS.getStatus() : OperationStatusEnum.SYNC_FAIL.getStatus();
        String message = createDeptResult.getErrorMessage() + "\t" + vo.toString();
        LogWriteVo logWriteVo = new LogWriteVo(ei, OperationTypeEnum.ADD.getType(), vo.getName(), statusCode, message);
        return count;
    }

    private Integer advanceCreateCrmOwner(String dingEmpId,String dingCorpId,String suiteId){
//        dingMappingEmployeeManager
//        cloudEmpService.cloudCreateEmp()
        return 1;
    }





    //处理回调事件部门同名
    private Map<String, Object> fixCallBackName(Integer ei, Dept dept) {
        //回调事件处理变更
        Map<String, Object> map = Maps.newHashMap();
        //判断中间表的数据是否已经存在该部门
        List<DeptVo> deptName = dingDeptMananger.getDeptName(ei, dept.getName());
        Boolean isEquals = false;
        for (DeptVo vo : deptName) {
            if (vo.getDingDeptId().equals(dept.getId()) && vo.getCrmDeptId() != 0) {
                isEquals = true;
            }
        }
        if (!isEquals) {
            //如果不一致，拿enterprise的全局index++。默认一开始10000
            Result<List<DingCorpMappingVo>> byEiResult = dingCorpMappingService.queryByEi(ei);
            if (ObjectUtils.isNotEmpty(byEiResult.getData())) {
                Integer all_index = byEiResult.getData().get(0).getRepeatIndex() + 1;
                String finalName = dept.getName().concat(Symbol).concat(all_index.toString());
                //更新enterprise的信息
                dingCorpMappingService.updateRepeatIndex(ei);
                map.put("same", Boolean.TRUE);
                map.put("name", (finalName));
                return map;
            }
        }
        //如果前缀名字一致，确定是同名的，拿中间表的数据去当做部门名字
        map.put("same", Boolean.FALSE);
        map.put("name", deptValidName(dept.getName()));
        return map;

    }

    //统一处理特殊字符或者空格的情况
    private String deptValidName(String name) {
        String match = "[^\\-\\\\/\\[\\]【】()（）_a-zA-Z0-9\\u4E00-\\u9fAF\\u3400-\\u4dBF\\u3300-\\u33FF\\uF900-\\uFAFF]";
        // 创建 Pattern 对象
        Pattern r = Pattern.compile(match);
        // 现在创建 matcher 对象
        Matcher m = r.matcher(name);
        String result = m.replaceAll("-");
        return result;
    }
    private Boolean suffixName(String deptName, String dataName) {
        String[] names = dataName.split("__");
        if (deptName.equals(names[0])) {
            return false;
        }
        return true;
    }

    private Boolean compareDeptOwner(String deptOwner, String dingOwner) {
        if (StringUtils.isAllBlank(deptOwner, dingOwner)) {
            return false;
        }
        if (deptOwner != null && !deptOwner.equals(dingOwner)) {
            return true;
        }
        if (dingOwner != null && !dingOwner.equals(deptOwner)) {
            return true;
        }
        return false;
    }

    private Boolean isModifyDeptOwner(Integer crmDeptOwner, String deptOwner) {
        if(crmDeptOwner == null && StringUtils.isNotEmpty(deptOwner)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public Result<Void> updateDeptBind(List<DeptVo> deptVos) {
        for(DeptVo deptVo : deptVos) {
            //查询是否已绑定
            DeptVo vo = dingDeptMananger.queryByDingId(deptVo.getEi(), deptVo.getDingDeptId());
            if(ObjectUtils.isEmpty(vo)) {
                //未绑定直接插入
                Integer count = dingDeptMananger.addDeptByEi(deptVo);
                log.info("CloudDeptServiceImpl.updateDeptBind,dingDeptId={},count={}", deptVo.getDingDeptId(), count);
            } else {
                //已绑定的更新一下
                Result<Integer> updateDeptResult = dingDeptMananger.updateDept(deptVo);
                log.info("CloudDeptServiceImpl.updateDeptBind,dingDeptId={},updateDeptResult={}", deptVo.getDingDeptId(), updateDeptResult);
            }
        }
        return null;
    }

    @Override
    public Result<List<DeptVo>> queryDepartments(Integer ei, Integer page, Integer size) {
        List<DeptVo> deptVos = dingDeptMananger.queryDepartments(ei, page, size);
        return Result.newSuccess(deptVos);
    }

    @Override
    public Result<Integer> updateDeptVo(DeptVo vo) {
        return dingDeptMananger.updateDept(vo);
    }
}
