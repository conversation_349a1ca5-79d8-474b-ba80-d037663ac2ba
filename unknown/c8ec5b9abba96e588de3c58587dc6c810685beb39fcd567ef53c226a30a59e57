package com.facishare.open.ding.provider.service;

import com.facishare.open.ding.api.enums.DingObjTypeEnum;
import com.facishare.open.ding.api.enums.SyncDirectionEnum;
import com.facishare.open.ding.api.model.SyncDataMappingModel;
import com.facishare.open.ding.api.service.SyncDataMappingsService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.entity.SyncDataMappingsEntity;
import com.facishare.open.ding.provider.manager.SyncDataMananger;
import com.facishare.open.ding.provider.utils.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

@Service("syncDataMappingsServiceImpl")
public class SyncDataMappingsServiceImpl implements SyncDataMappingsService {

    @Autowired
    private SyncDataMananger syncDataMananger;

    @Override
    public Result<List<SyncDataMappingModel>> loadSyncDataMapping(Integer ei,
                                                            String corpId,
                                                            String crmObjectApiName,
                                                            String dingObjApiName,
                                                            SyncDirectionEnum direction,
                                                            String dataId) {
        if (!StringUtils.isEmpty(crmObjectApiName)){
            dingObjApiName=DingObjTypeEnum.getEnumByCrmApiName(crmObjectApiName).getDingObjApiName();
        }else {
            crmObjectApiName=DingObjTypeEnum.getEnumByDingApiName(dingObjApiName).getCrmObjApiName();
        }

        List<SyncDataMappingsEntity> syncDataMappingsEntitys =
          syncDataMananger.loadSyncDataMapping(ei, corpId, crmObjectApiName, dingObjApiName, direction, dataId);
        if (syncDataMappingsEntitys!=null){
            List<SyncDataMappingModel> syncDataMappingModels = BeanUtil.copyList(syncDataMappingsEntitys, SyncDataMappingModel.class);
            return Result.newSuccess(syncDataMappingModels);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> updateSyncDataMapping(SyncDataMappingModel mappingModel) {
        SyncDataMappingsEntity syncDataMappingsEntity = BeanUtil.deepCopy(mappingModel, SyncDataMappingsEntity.class);
        int save = syncDataMananger.save(syncDataMappingsEntity);
        if (save>0){
            return Result.newSuccess(true);
        }else {
            return Result.newSuccess(false);
        }
    }

}
