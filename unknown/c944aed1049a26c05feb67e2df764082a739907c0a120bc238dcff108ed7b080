package com.facishare.open.aliyun.market.service;

import com.aliyun.market20151101.models.DescribeOrderResponseBody;
import com.facishare.open.aliyun.market.model.OrderModel;
import com.facishare.open.aliyun.market.model.result.CreateOrderResult;
import com.facishare.open.feishu.syncapi.entity.OrderInfoEntity;
import com.facishare.open.feishu.syncapi.result.Result;

public interface OrderEventService {
    /**
     * 下单事件
     * @param orderModel 订单
     * @return
     */
    Result<CreateOrderResult> upgradeInstance(OrderModel orderModel);

    /**
     * 新增或更新订单信息
     * @param entity
     * @return
     */
    Result<Integer> insertOrUpdateOrderInfo(OrderInfoEntity entity);

    /**
     * 下单开通CRM企业
     * @param entity
     * @return
     */
    Result<Void> openEnterprise(OrderInfoEntity entity, DescribeOrderResponseBody orderInfo);
}
