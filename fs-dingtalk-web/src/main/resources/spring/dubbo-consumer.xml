<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:application name="${dubbo.application.name}"/>
    <dubbo:registry address="${dubbo.registry.address}" file="${dubbo.registry.file}" timeout="120000"/>
    <dubbo:consumer check="false" timeout="15000" filter="tracerpc"/>

    <dubbo:reference id="activeSessionAuthorizeService" interface="com.facishare.asm.api.service.ActiveSessionAuthorizeService" />

    <dubbo:reference id="enterpriseEditionService" interface="com.facishare.uc.api.service.EnterpriseEditionService" protocol="dubbo" timeout="3000" />

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.AuthService" id="authService" version="${dubbo.provider.version}"/>-->

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.EnterpriseService" id="enterpriseService" version="${dubbo.provider.version}" timeout="15000"/>-->

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.ObjectMappingService" id="objectMappingService" version="${dubbo.provider.version}" timeout="15000">-->
<!--        <dubbo:method name="updateEmp" async="true"/>-->
<!--    </dubbo:reference>-->

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.RedisDingService" id="redisDingService" version="${dubbo.provider.version}"/>-->

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.SyncLogService" id="syncLogService" version="${dubbo.provider.version}"/>-->

    <dubbo:reference id="ssoLoginService" interface="com.facishare.userlogin.api.service.SSOLoginService"/>

<!--    <dubbo:reference interface="com.fxiaoke.message.extrnal.platform.api.ExternalMessageService" id="externalMessageServiceImpl"-->
<!--                     group="dingtalk"/>-->
<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.DingtalkUserService" id="dingtalkUserService" protocol="dubbo" version="${dubbo.provider.version}"/>-->

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.SyncBizDataService" id="syncBizDataService" protocol="dubbo" version="1.0"/>-->
<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.cloud.DingProxyService" id="dingProxyService" protocol="dubbo" version="1.0"/>-->
<!--    &lt;!&ndash; 应用授权企业绑定 &ndash;&gt;-->
<!--    <dubbo:reference id="dingCorpMappingService"-->
<!--                     interface="com.facishare.open.ding.api.service.DingCorpMappingService" timeout="5000" version="1.0"/>-->

<!--    <dubbo:reference id="pollingSyncService"-->
<!--                     interface="com.facishare.open.ding.api.service.PollingSyncService" timeout="5000" version="1.0"/>-->
<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.AppAuthService" id="appAuthService" version="${dubbo.provider.version}" timeout="15000">-->
<!--    </dubbo:reference>-->
<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.cloud.CloudOrderService" id="cloudOrderService" version="${dubbo.provider.version}" timeout="15000"></dubbo:reference>-->

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.cloud.DingAuthService" id="dingAuthService" version="${dubbo.provider.version}" timeout="15000"></dubbo:reference>-->

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.cloud.DingRefuseDataService" id="dingRefuseDataService" version="${dubbo.provider.version}" timeout="15000"></dubbo:reference>-->

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.DataStorageService" id="dataStorageService" version="${dubbo.provider.version}" timeout="15000"></dubbo:reference>-->
<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.cloud.DingObjectService" id="dingObjectService" version="${dubbo.provider.version}" timeout="15000"></dubbo:reference>-->

<!--    <dubbo:reference id="connectorSyncObjectService"-->
<!--                     interface="com.facishare.open.ding.api.service.cloud.connector.ConnectorSyncObjectService"-->
<!--                     protocol="dubbo"-->
<!--                     version="1.0"-->
<!--                     timeout="60000">-->

<!--    </dubbo:reference>-->
<!--    <dubbo:reference id="dssSyncDataMappingService"-->
<!--                     interface="com.facishare.open.ding.api.service.DssSyncDataMappingService"-->
<!--                     protocol="dubbo"-->
<!--                     version="1.0"-->
<!--                     timeout="60000">-->
<!--    </dubbo:reference>-->

    <dubbo:reference id="fsEmployeeServiceProxy"
                     interface="com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy"
                     protocol="dubbo"
                     version="1.0"
                     check="false"/>

<!--    <dubbo:reference id="cloudToolsService"-->
<!--                     interface="com.facishare.open.ding.api.service.cloud.CloudToolsService"-->
<!--                     protocol="dubbo"-->
<!--                     version="1.0"-->
<!--                     check="false"/>-->

<!--    <dubbo:reference id="cloudEmpServiceImpl"-->
<!--                     interface="com.facishare.open.ding.api.service.cloud.CloudEmpService"-->
<!--                     version="${dubbo.provider.version}"-->
<!--                     timeout="15000"/>-->

<!--    <dubbo:reference id="cloudNotificationService"-->
<!--                     interface="com.facishare.open.ding.api.service.cloud.CloudNotificationService"-->
<!--                     version="${dubbo.provider.version}"-->
<!--                     timeout="15000"/>-->

<!--    <dubbo:reference id="cloudMonitorService"-->
<!--                     interface="com.facishare.open.ding.api.service.cloud.CloudMonitorService"-->
<!--                     version="${dubbo.provider.version}"-->
<!--                     timeout="15000"/>-->

<!--    <dubbo:reference id="cloudContactsService"-->
<!--                     interface="com.facishare.open.ding.api.service.cloud.CloudContactsService"-->
<!--                     version="${dubbo.provider.version}"-->
<!--                     timeout="15000"/>-->


    <!--    fs-dingtalk-provider dubbo-consumer.xml-->
    <!-- 应用管理员信息 -->
    <dubbo:reference id="openAppAdminService"
                     interface="com.facishare.open.app.center.api.service.OpenAppAdminService" timeout="3000" version="1.3"/>

    <dubbo:reference id="appAdminService"
                     interface="com.facishare.open.app.center.api.service.QueryAppAdminService" timeout="5000" version="1.0"/>

    <!-- 员工 -->
    <!--    <dubbo:reference id="employeeService"-->
    <!--                     interface="com.facishare.open.addressbook.api.EmployeeService" timeout="5000" version="1.1"/>-->
    <!--    <dubbo:reference id="circleService"-->
    <!--                     interface="com.facishare.open.addressbook.api.CircleService" timeout="10000" version="1.1"/>-->
    <!-- 开平消息服务 -->
    <dubbo:reference id="sendMessageService" interface="com.facishare.open.msg.service.SendMessageService" version="1.0" timeout="10000" />

    <!--    <dubbo:reference id="employeeProviderService"-->
    <!--                     interface="com.facishare.organization.api.service.EmployeeProviderService" protocol="dubbo"-->
    <!--                     version="5.7"/>-->

    <!--    <dubbo:reference id="employeeApiService"-->
    <!--                     interface="com.facishare.organization.api.service.EmployeeService" protocol="dubbo"-->
    <!--                     version="5.7"/>-->

    <!--    <dubbo:reference id="employeeAdapterService"-->
    <!--                     interface="com.facishare.organization.adapter.api.service.EmployeeService"/>-->

    <!--    <dubbo:reference id="departmentService"-->
    <!--                     interface="com.facishare.organization.adapter.api.service.DepartmentService"/>-->

    <!--    <dubbo:reference id="departmentPrincipalService"-->
    <!--                     interface="com.facishare.organization.adapter.api.service.DepartmentPrincipalService"/>-->

    <!--    <dubbo:reference id="enterpriseConfigService"-->
    <!--                     interface="com.facishare.organization.adapter.api.config.service.EnterpriseConfigService"/>-->

    <!--    <dubbo:reference id="departmentProviderService"-->
    <!--                     interface="com.facishare.organization.api.service.DepartmentProviderService" protocol="dubbo"-->
    <!--                     version="5.7"/>-->


<!--    <dubbo:reference id="enterpriseEditionService" interface="com.facishare.uc.api.service.EnterpriseEditionService" protocol="dubbo" timeout="3000"/>-->

<!--    <dubbo:reference id="cloudDingRequestService" interface="com.facishare.open.ding.api.service.cloud.CloudDingRequestService" protocol="dubbo" timeout="3000" version="1.0"/>-->

<!--    <dubbo:reference id="fsEmployeeServiceProxy"-->
<!--                     interface="com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy"-->
<!--                     protocol="dubbo"-->
<!--                     version="1.0"-->
<!--                     check="false"/>-->
    <!--    fs-dingtalk-provider dubbo-consumer.xml-->


    <!--    fs-dingtalk-cloud dubbo-consumer.xml-->
    <!-- 应用授权企业绑定 -->
<!--    <dubbo:reference id="dingCorpMappingService"-->
<!--                     interface="com.facishare.open.ding.api.service.DingCorpMappingService" timeout="5000" version="1.0"/>-->

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.ObjectMappingService" id="objectMappingService" version="${dubbo.provider.version}" timeout="15000">-->
<!--    </dubbo:reference>-->
<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.cloud.CloudEmpService" id="cloudEmpServiceImpl" version="${dubbo.provider.version}" timeout="15000">-->
<!--    </dubbo:reference>-->
<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.cloud.CloudAccountService" id="cloudAccountServiceImpl" version="${dubbo.provider.version}" timeout="15000">-->
<!--    </dubbo:reference>-->
<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.cloud.CloudDeptService" id="cloudDeptServiceImpl" version="${dubbo.provider.version}" timeout="15000">-->
<!--    </dubbo:reference>-->
<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.AppAuthService" id="appAuthService" version="${dubbo.provider.version}" timeout="15000">-->
<!--    </dubbo:reference>-->

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.UserAppMappingService" id="userAppMappingService" version="${dubbo.provider.version}" timeout="15000">-->
<!--    </dubbo:reference>-->

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.cloud.CloudOrderService" id="cloudOrderService" version="${dubbo.provider.version}" timeout="15000">-->
<!--    </dubbo:reference>-->
<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.cloud.DingRefuseDataService" id="dingRefuseDataService" version="${dubbo.provider.version}" timeout="15000">-->
<!--    </dubbo:reference>-->
<!--    <dubbo:reference id="pollingSyncService" interface="com.facishare.open.ding.api.service.PollingSyncService" timeout="5000" version="1.0"/>-->
    <dubbo:reference id="dingAddressBookCallBackService" interface="com.facishare.marketing.outapi.service.DingAddressBookCallBackService" timeout="5000" version="1.0"/>

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.cloud.connector.ConnectorObjectDataCacheService"-->
<!--                     id="connectorObjectDataCacheService"-->
<!--                     version="${dubbo.provider.version}"-->
<!--                     timeout="15000"/>-->

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.DingMappingEmployeeService"-->
<!--                     id="dingMappingEmployeeService"-->
<!--                     version="${dubbo.provider.version}"-->
<!--                     timeout="15000"/>-->
<!--    <dubbo:reference  interface="com.facishare.open.ding.api.service.CrmSyncObjService" id="crmSyncObjService" protocol="dubbo" version="1.0" />-->
<!--    <dubbo:reference  interface="com.facishare.open.ding.api.service.DingObjSyncService" id="dingObjSyncService" protocol="dubbo" version="1.0" />-->
<!--    <dubbo:reference  interface="com.facishare.open.ding.api.service.CrmObjectWriterService" id="objectWriterService" protocol="dubbo" version="${dubbo.provider.version}"/>-->
<!--    <dubbo:reference  interface="com.facishare.open.ding.api.service.SyncDataMappingsService" id="syncDataMappingsService" protocol="dubbo" version="${dubbo.provider.version}" />-->
<!--    <dubbo:reference  interface="com.facishare.open.ding.api.service.cloud.DingAuthService"-->
<!--                      id="dingAuthServiceImplForSelfBuiltApp"-->
<!--                      group="dingAuthForSelfBuiltApp"-->
<!--                      protocol="dubbo"-->
<!--                      version="${dubbo.provider.version}" />-->

<!--    <dubbo:reference id="fsEmployeeServiceProxy"-->
<!--                     interface="com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy"-->
<!--                     protocol="dubbo"-->
<!--                     version="1.0"-->
<!--                     check="false"/>-->
    <!--    fs-dingtalk-cloud dubbo-consumer.xml-->

</beans>