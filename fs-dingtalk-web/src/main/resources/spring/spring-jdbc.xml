<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
		http://www.springframework.org/schema/tx
		http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

    <!-- 数据源 -->
    <bean id="dataSource" class="com.github.mybatis.spring.DynamicDataSource">
        <property name="configName" value="fs-open-dingtalk-all"/>
    </bean>

    <!-- 配置事务管理器 -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <tx:annotation-driven transaction-manager="transactionManager" proxy-target-class="true" />

    <!-- define the SqlSessionFactory -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <!-- 自动扫描entity目录, 省掉Configuration.xml里的手工配置 -->
        <property name="typeAliasesPackage" value="com.facishare.open.ding.provider.entity,com.facishare.open.ding.cloud.entity" />
        <property name="configLocation" value="classpath:mybatis/mybatis-config.xml" />
        <property name="mapperLocations" value="classpath*:mapper/*.xml"/>
    </bean>

    <!-- scan for mapper and let them be autowired -->
    <bean class="com.github.mybatis.spring.ScannerConfigurer">
        <property name="basePackage" value="com.facishare.open.ding.provider.dao,com.facishare.open.ding.cloud.dao"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>


    <!-- erp-syncdata数据源 -->
    <bean id="dataSourceDss" class="com.github.mybatis.spring.DynamicDataSource">
        <property name="configName" value="erp-sync-data-all"/>
    </bean>

    <!-- 配置事务管理器 -->
    <bean id="transactionManagerDss" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSourceDss"/>
    </bean>

    <tx:annotation-driven transaction-manager="transactionManagerDss" proxy-target-class="true" />

    <!-- define the SqlSessionFactory -->
    <bean id="sqlSessionFactoryDss" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSourceDss"/>
        <!-- 自动扫描entity目录, 省掉Configuration.xml里的手工配置 -->
        <property name="typeAliasesPackage" value="com.facishare.open.ding.provider.dss.entity" />
        <property name="configLocation" value="classpath:mybatis/dssmybatis-config.xml" />
    </bean>

    <!-- scan for mapper and let them be autowired -->
    <bean class="com.github.mybatis.spring.ScannerConfigurer">
        <property name="basePackage" value="com.facishare.open.ding.provider.dss.dao"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactoryDss"/>
    </bean>

    <!-- fs-dingtalk-cloud  spring-jdbc.xml -->
    <!-- 数据源 -->
<!--    <bean id="dataSource" class="com.github.mybatis.spring.DynamicDataSource">-->
<!--        <property name="configName" value="fs-rds-dingtalk-all"/>-->
<!--    </bean>-->

<!--    &lt;!&ndash; 配置事务管理器 &ndash;&gt;-->
<!--    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">-->
<!--        <property name="dataSource" ref="dataSource"/>-->
<!--    </bean>-->

<!--    <tx:annotation-driven transaction-manager="transactionManager" proxy-target-class="true" />-->

<!--    &lt;!&ndash; define the SqlSessionFactory &ndash;&gt;-->
<!--    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">-->
<!--        <property name="dataSource" ref="dataSource"/>-->
<!--        &lt;!&ndash; 自动扫描entity目录, 省掉Configuration.xml里的手工配置 &ndash;&gt;-->
<!--        <property name="typeAliasesPackage" value="com.facishare.open.ding.cloud.entity" />-->
<!--        <property name="configLocation" value="classpath:mybatis/mybatis-config.xml" />-->
<!--        <property name="mapperLocations" value="classpath*:mapper/*.xml"/>-->
<!--    </bean>-->

<!--    &lt;!&ndash; scan for mapper and let them be autowired &ndash;&gt;-->
<!--    <bean class="com.github.mybatis.spring.ScannerConfigurer">-->
<!--        <property name="basePackage" value="com.facishare.open.ding.cloud.dao"/>-->
<!--        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>-->
<!--    </bean>-->

    <!-- 日志上报数据源 -->
    <bean id="fsClickHouseDB" class="com.github.mybatis.spring.DynamicDataSource">
        <property name="configName" value="eye-clickhouse-db"/>
    </bean>
    <!-- define the SqlSessionFactory -->
    <bean id="clickHouseSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="fsClickHouseDB"/>
        <property name="typeAliasesPackage" value="com.facishare.open.ding.cloud.ckEntity"/>
        <property name="configLocation" value="classpath:mybatis/mybatis-config.xml"/>
    </bean>
    <!-- scan for mapper and let them be autowired -->
    <bean id="clickHouseDbConfig" class="com.github.mybatis.spring.ScannerConfigurer">
        <property name="basePackage" value="com.facishare.open.ding.cloud.ckDao"/>
        <property name="sqlSessionFactoryBeanName" value="clickHouseSqlSessionFactory"/>
    </bean>
    <!-- fs-dingtalk-cloud  spring-jdbc.xml -->

</beans>