package com.facishare.open.ding.cloud.manager;

import com.facishare.open.ding.api.enums.CloudDataTypeEnum;
import com.facishare.open.ding.api.enums.SourceTypeEnum;
import com.facishare.open.ding.api.model.OAConnectorOpenDataModel;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.ckDao.BizLogOaconnectoropendataDistDao;
import com.facishare.open.ding.cloud.ckEntity.BizLogOaconnectoropendataDistEntity;
import com.facishare.open.ding.cloud.utils.DateUtils;
import com.facishare.open.ding.cloud.utils.TraceUtil;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.fxiaoke.common.IpUtil;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.OAConnectorOpenDataLog;
import com.fxiaoke.log.dto.OAConnectorOpenDataLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class OAConnectorOpenDataManager {
    @Autowired
    private BizLogOaconnectoropendataDistDao bizLogOaconnectoropendataDistDao;
    @Autowired
    private DingCorpMappingService corpMappingService;

    public void send(OAConnectorOpenDataModel model) {
        if(model.getCreateTime() == null) {
            model.setCreateTime(System.currentTimeMillis());
        }

        //人员登陆失败区分
        if(StringUtils.isNotEmpty(model.getDataTypeId())
                && model.getDataTypeId().equals(CloudDataTypeEnum.EMPLOYEE_LOGIN.getDataType())) {
            //安装失败，无记录
            try {
                if(model.getErrorCode().equals("100")) {
                    List<BizLogOaconnectoropendataDistEntity> distBos = bizLogOaconnectoropendataDistDao.findEnterpriseCreateError(SourceTypeEnum.DING_CLOUD.name(), CloudDataTypeEnum.ENTERPRISE_CREATE.getDataType(), model.getCorpId());
                    if(CollectionUtils.isNotEmpty(distBos)) {
                        model.setDataTypeId(CloudDataTypeEnum.NEW_EMPLOYEE_LOGIN.getDataType());
                    }
                } else {
                    //安装失败，有记录
                    if(model.getErrorCode().equals("101") || model.getErrorCode().equals("102")) {
                        Result<List<DingCorpMappingVo>> listResult = corpMappingService.queryCorpMappingByCorpId(model.getCorpId());
                        if(listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
                            Date date = DateUtils.parseDate("2024-02-01", "yyyy-MM-dd");
                            assert date != null;
                            if(listResult.getData().get(0).getCreateTime().getTime() > date.getTime()) {
                                model.setDataTypeId(CloudDataTypeEnum.NEW_EMPLOYEE_LOGIN.getDataType());
                            }
                        }
                    }
                }
            } finally {
                log.info("OAConnectorOpenDataManager.send,model.dataTypeId={}", model.getDataTypeId());
            }
        }

        OAConnectorOpenDataLogDTO dto = OAConnectorOpenDataLogDTO.builder()
                .appName(ConfigHelper.getProcessInfo().getName())
                .traceId(TraceUtil.get())
                .ea(model.getEa())
                .createTime(model.getCreateTime())
                .serverIp(IpUtil.getSiteLocalIp())
                .appId(model.getAppId())
                .channelId(model.getChannelId())
                .corpId(model.getCorpId())
                .dataTypeId(model.getDataTypeId())
                .outUserId(model.getOutUserId())
                .errorCode(model.getErrorCode())
                .errorMsg(model.getErrorMsg())
                .build();

        log.info("OAConnectorOpenDataManager.send,logDTO={}", dto);
        try {
            BizLogClient.send("biz-log-oaconnectoropendata", Pojo2Protobuf.toMessage(dto, OAConnectorOpenDataLog.class).toByteArray());
        } catch (Exception e) {
            log.info("OAConnectorOpenDataManager.send,exception={}",e.getMessage());
        }
    }
}
