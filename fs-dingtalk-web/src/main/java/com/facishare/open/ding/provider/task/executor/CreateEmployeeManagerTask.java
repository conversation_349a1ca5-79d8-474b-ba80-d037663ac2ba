package com.facishare.open.ding.provider.task.executor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.vo.CreateCrmEmployeeVo;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.constants.Constant;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.manager.DingDeptMananger;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.facishare.organization.adapter.api.model.biz.department.ModifiedDepartment;
import com.facishare.organization.adapter.api.model.biz.department.arg.ModifyDepartmentArg;
import com.facishare.organization.adapter.api.model.biz.department.result.ModifyDepartmentResult;
import com.facishare.organization.adapter.api.service.DepartmentService;
import com.facishare.organization.api.exception.OrganizationException;
import com.google.common.collect.Lists;
import com.sun.xml.bind.v2.runtime.reflect.opt.Const;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2020/7/24 10:10
 * @Version 1.0
 */
@Slf4j
public class CreateEmployeeManagerTask implements Runnable {

    private static final Integer CURRENT_EMPLOYEE_ID = -9;

    private Integer ei;
    private DingMappingEmployeeManager dingMappingEmployeeManager;
    private ObjectMappingService service;
    private DeptVo vo;
    private DingDeptMananger dingDeptMananger;
    private DepartmentService departmentService;



    public CreateEmployeeManagerTask(ObjectMappingService service,DepartmentService departmentService, DeptVo vo,DingDeptMananger dingDeptMananger,DingMappingEmployeeManager employeeManager,Integer ei) {
        this.ei = ei;
       this.service=service;
       this.vo=vo;
       this.dingDeptMananger=dingDeptMananger;
       this.dingMappingEmployeeManager=employeeManager;
       this.departmentService=departmentService;
    }

    @Override
    public void run() {
        String userID = vo.getDingDeptOwner();
        if (StringUtils.isNotBlank(userID)) {
            Result<DingMappingEmployeeResult> dingMappingEmployeeResultResult = dingMappingEmployeeManager.queryEmpByDingUserId(ei, userID);
            log.info("createCrmManager dingMappingEmployeeResultResult:{}", dingMappingEmployeeResultResult);
            DingMappingEmployeeResult data = dingMappingEmployeeResultResult.getData();
            CreateCrmEmployeeVo createCrmEmployeeVo = new CreateCrmEmployeeVo();
            createCrmEmployeeVo.setMobile(data.getDingEmployeePhone());
            createCrmEmployeeVo.setName(data.getDingEmployeeName());
            createCrmEmployeeVo.setEi(data.getEi());
            createCrmEmployeeVo.setDingDeptId(data.getDingDeptId());
            createCrmEmployeeVo.setGender(data.getGender());
            createCrmEmployeeVo.setId(data.getId().intValue());
            createCrmEmployeeVo.setUpdateBy(data.getUpdateBy());
            createCrmEmployeeVo.setDingEmployeeId(data.getDingEmployeeId());
            List<CreateCrmEmployeeVo> createCrmEmployeeVosList = Lists.newArrayList();
            createCrmEmployeeVosList.add(createCrmEmployeeVo);
//                    Result<Integer> fxEmployee = createFxEmployee(employee);
            //查询中间表，如果发现crmEmployeeId不为空，则取相关的数据
            Result<DingMappingEmployeeResult> crmEmpResult = dingMappingEmployeeManager.queryEmpByDingUserId(ei, vo.getDingDeptOwner());
            if(ObjectUtils.isEmpty(crmEmpResult.getData())){
                //设置相关的部门负责人
                service.batchCreateFxEmployee(createCrmEmployeeVosList, ei, createCrmEmployeeVo.getUpdateBy());
                crmEmpResult = dingMappingEmployeeManager.queryEmpByDingUserId(ei, userID);
            }

            log.info("afterResult:{}", crmEmpResult);
            //更新部门的信息
            if (crmEmpResult.getErrorCode() == Constant.errcode) {
                //设置crm部门负责人
                vo.setCrmDeptOwner(crmEmpResult.getData().getEmployeeId());
                dingDeptMananger.updateDept(vo);
                log.info("item:{}", vo);
                ModifyDepartmentArg arg = new ModifyDepartmentArg();
                ModifiedDepartment modifiedDepartment = new ModifiedDepartment();
                modifiedDepartment.setDepartmentId(vo.getCrmDeptId());
                modifiedDepartment.setName(vo.getName());
                modifiedDepartment.setDepartmentPrincipalId(crmEmpResult.getData().getEmployeeId());
                arg.setModifiedDepartment(modifiedDepartment);
                arg.setEnterpriseId(ei);
                arg.setCurrentEmployeeId(Constant.SYSTEM_USER);
                arg.setLastUpdateTime(new Date().getTime());

                //更新crm的部门
                try {
                    log.info("更新部门负责人 deptName:{},userName:{}",vo.getName(),data.getDingEmployeeName());
                    ModifyDepartmentResult modifyDepartmentResult = departmentService.modifyDepartment(arg);
                } catch (OrganizationException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
