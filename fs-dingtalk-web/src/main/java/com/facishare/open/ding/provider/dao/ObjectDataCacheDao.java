package com.facishare.open.ding.provider.dao;

import com.facishare.open.ding.api.vo.ObjectDataCacheVo;
import com.facishare.open.ding.provider.entity.ObjectDataCacheEntity;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ObjectDataCacheDao extends I<PERSON>rudMapper<ObjectDataCacheEntity> {
    @Select("select * from object_data_cache where corp_id=#{corpId} and app_id=#{appId} and object_api_name=#{objectApiName}")
    List<ObjectDataCacheVo> queryDataList(@Param("corpId") String corpId,
                                          @Param("appId") long appId,
                                          @Param("objectApiName") String objectApiName);

    @Select("select * from object_data_cache where corp_id=#{corpId} and app_id=#{appId} and object_api_name=#{objectApiName} and data_code=#{dataCode} limit 1")
    ObjectDataCacheVo queryOneData(@Param("corpId") String corpId,
                                              @Param("appId") long appId,
                                              @Param("objectApiName") String objectApiName,
                                              @Param("dataCode") String dataCode);

    @Select("select * from object_data_cache where corp_id=#{corpId} and app_id=#{appId} and object_api_name=#{objectApiName} and object_data_id=#{objectDataId} limit 1")
    ObjectDataCacheVo queryOneDataByObjectDataId(@Param("corpId") String corpId,
                                   @Param("appId") long appId,
                                   @Param("objectApiName") String objectApiName,
                                   @Param("objectDataId") String objectDataId);
}
