package com.facishare.open.ding.cloud.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.open.ding.api.model.AppParams;
import com.facishare.open.ding.api.model.OrderModel;
import com.facishare.open.ding.api.service.cloud.CloudDeptService;
import com.facishare.open.ding.api.service.cloud.CloudEmpService;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.cloud.Model.InnerSearchQueryInfo;
import com.facishare.open.ding.cloud.arg.CreateCrmOrderArg;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.arg.CreateCustomerArg;
import com.facishare.open.ding.cloud.limiter.CrmRateLimiter;
import com.facishare.open.ding.cloud.utils.CrmUrlUtils;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.organization.adapter.api.permission.model.AddRoleWithDepartmentToEmployeesByAppId;
import com.facishare.organization.adapter.api.permission.model.RoleCodeAndDepartmentIds;
import com.facishare.organization.adapter.api.permission.service.PermissionService;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.privilege.api.FieldPrivilegeRestService;
import com.facishare.privilege.api.FunctionPrivilegeRestService;
import com.facishare.privilege.api.RolePrivilegeRestService;
import com.facishare.privilege.api.UserPrivilegeRestService;
import com.facishare.privilege.api.module.PrivilegeContext;
import com.facishare.privilege.api.module.user.BatchAddUserRoleVo;
import com.facishare.privilege.api.module.user.DeleteByUserIdsVo;
import com.facishare.restful.common.StopWatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2021/4/29 11:11
 * @Version 1.0
 */
@Service
@Slf4j
public class CrmManager {


    private static final Random RANDOM = new Random();

    @Autowired
    private CloudDeptService cloudDeptService;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private CloudEmpService cloudEmpService;

    @Autowired
    private HttpCloudManager httpCloudManager;
    //创建客户的url
    private String CREATE_CUSTOMER_URL = "versionRegisterService/addCrmCustomer";
    //创建订单的url
    private String ORDER_URL = "versionRegisterService/addCrmOrder";

    //人员相关的api
    @Autowired
    private UserPrivilegeRestService userPrivilegeService;
    //操作权限相关的api
    @Autowired
    private FunctionPrivilegeRestService functionPrivilegeRestService;
    //字段权限相关的api
    @Autowired
    private FieldPrivilegeRestService fieldPrivilegeRestService;
    //角色相关的api
    @Autowired
    private RolePrivilegeRestService rolePrivilegeRestService;
    @Autowired
    private PermissionService permissionService;
    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;

    //新企业创建客户
    public Result<String> createCustomer(CreateCustomerArg createCustomerArg) {
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            log.info("CrmManager.createCustomer,interface limit");
            return Result.newError(ResultCode.SERVER_BUSY);
        }
        Object createJson = JSONObject.toJSON(createCustomerArg);
        String customerId = httpCloudManager.postUrl(ConfigCenter.RESET_URL.concat(CREATE_CUSTOMER_URL), createJson, createHeader());
        Map map = JSONObject.parseObject(customerId, Map.class);
        if(ObjectUtils.isNotEmpty(map.get("customerId"))){
            return Result.newSuccess(customerId);
        }
        return Result.newError(ResultCode.CREATE_CUSTOMER_FAIL,customerId);
    }


    //创建订单
    public Result<String> createCrmOrder(CreateCrmOrderArg createCrmOrderArg) {
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            log.info("CrmManager.createCrmOrder,interface limit");
            return Result.newError(ResultCode.SERVER_BUSY);
        }
        String createJson = JSONObject.toJSON(createCrmOrderArg).toString();
        String orderResult = httpCloudManager.postUrl(ConfigCenter.RESET_URL.concat(ORDER_URL), createJson, createHeader());
        Object isSuccess = JSONPath.read(orderResult, "$.isSuccess");
        if (ObjectUtils.isEmpty(isSuccess) || !Boolean.valueOf(isSuccess.toString())) {
            log.error("create crm order failed arg:{}", createCrmOrderArg);
            return Result.newError(ResultCode.CREATE_ORDER_FAILED);
        }
        return Result.newSuccess(JSONPath.read(orderResult, "$.orderId").toString());
    }

    //创建角色
    public Result<String> createRoleCode(String ea, Integer ei, List<Integer> empIds, String roleCode) {
        if(CollectionUtils.isEmpty(empIds)){
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        List<String> userIds = Lists.newArrayList();
        empIds.stream().forEach(item -> {
            userIds.add(item.toString());
        });
        PrivilegeContext crm = PrivilegeContext.builder().appId("CRM").tenantId(ei).operatorId(-10000).build();
        BatchAddUserRoleVo.Argument argument = new BatchAddUserRoleVo.Argument();
        argument.setRoleCodes(Lists.newArrayList(roleCode));
        argument.setUserIds(userIds);
        argument.setMajorRole(roleCode);
        argument.setUpdateMajorRole(false);
        //该接口错误会抛出异常，捕获异常，返回错误信息
        BatchAddUserRoleVo.Result result;
        try {
            result = userPrivilegeService.batchAddUserRole(crm, argument);
        } catch (Exception e) {
            log.info("CrmManager.createRoleCode,batchAddUserRole error={}",e.getMessage(), e);
            return Result.newError(ResultCode.ADD_USER_ROLE_ERROR);
        }
        log.info("createRoleCode result:{}", result);
        return Result.newSuccess();
    }

    //移除角色
    public Result<String> removeRoleCode(String ea, Integer ei, List<Integer> empIds, Long appId) {
        List<String> userIds = Lists.newArrayList();
        empIds.stream().forEach(item -> {
            userIds.add(item.toString());
        });
        String roleCode = Optional.ofNullable(ConfigCenter.ROLE_MAP.get(appId)).orElseGet(() -> "00000000000000000000000000000015");
        final PrivilegeContext crm = PrivilegeContext.builder().appId("CRM").tenantId(ei).operatorId(-10000).build();
        DeleteByUserIdsVo.Argument argument = new DeleteByUserIdsVo.Argument();
        argument.setRoleCode(roleCode);
        argument.setUserIds(userIds);
        DeleteByUserIdsVo.Argument result = userPrivilegeService.deleteByUserIds(crm, argument);
        return Result.newSuccess();
    }

    //添加管理员角色
    public Result<String> addManagerRole(Integer ei, Integer empId) {
        AddRoleWithDepartmentToEmployeesByAppId.Argument argument = new AddRoleWithDepartmentToEmployeesByAppId.Argument();
        RoleCodeAndDepartmentIds roleCodeAndDepartmentIds = new RoleCodeAndDepartmentIds();
        roleCodeAndDepartmentIds.setRoleCode("99");
        roleCodeAndDepartmentIds.setIsOpen(true);
        roleCodeAndDepartmentIds.setDepartmentIds(Lists.newArrayList(999999));
        argument.setRoleCodeAndDepartmentIds(roleCodeAndDepartmentIds);
        argument.setEnterpriseId(ei);
        argument.setAppId("facishare-system");
        argument.setEmployeeIds(Lists.newArrayList(empId));
        argument.setCurrentEmployeeId(-10000);
        AddRoleWithDepartmentToEmployeesByAppId.Result result = permissionService.addRoleWithDepartmentToEmployeesByAppId(argument);
        log.info("addManagerRole result:{},argument:{}", result,argument);
        return Result.newSuccess();
    }

    //查询部门对象
    //查询部门是否存在
    public Result<Map<String, Object>> queryCrmDept(Integer enterpriseId, String filed, String filedValue) {
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            log.info("CrmManager.queryCrmDept,interface limit");
            return Result.newError(ResultCode.SERVER_BUSY);
        }
        Map<String, String> hearsMap = Maps.newHashMap();
        hearsMap.put("x-fs-ei", enterpriseId.toString());
        hearsMap.put("x-fs-userinfo", "-10000");

        InnerSearchQueryInfo innerSearchQueryInfo = new InnerSearchQueryInfo();
        InnerSearchQueryInfo.Filter orderIdFilter = new InnerSearchQueryInfo.Filter();
        orderIdFilter.setOperator("EQ");
        orderIdFilter.setField_name(filed);
        orderIdFilter.setField_values(Lists.newArrayList(filedValue));

        List<InnerSearchQueryInfo.Filter> filters = Lists.newArrayList(orderIdFilter);
        innerSearchQueryInfo.setFilters(filters);
        innerSearchQueryInfo.setOffset(0);
        innerSearchQueryInfo.setLimit(100);
        InnerSearchQueryInfo.Order order = new InnerSearchQueryInfo.Order();
        order.setFieldName("last_modified_time");
        order.setAsc(false);
        List<InnerSearchQueryInfo.Order> orders = Lists.newArrayList(order);
        innerSearchQueryInfo.setOrders(orders);

        Map<String, Object> queryMap = Maps.newHashMap();
        Gson gson = new Gson();
        queryMap.put("object_describe_api_name", "DepartmentObj");
        queryMap.put("search_query_info", gson.toJson(innerSearchQueryInfo));
        String result = httpCloudManager.postUrl(CrmUrlUtils.queryList("/DepartmentObj"), queryMap, hearsMap);
        log.info("queryCrmDept result:{}", result);
        Map<String, Object> objectMap = Maps.newHashMap();
        String listResult = JSONPath.read(result, "$.data.dataList").toString();
        JSONArray jsonArray = JSONArray.parseArray(listResult);
        if (jsonArray.size() == 0) {
            return Result.newSuccess();
        }
        String object_id = JSONPath.read(result, "$.data.dataList[0]._id").toString();
        objectMap.put("object_id", object_id);
        return Result.newSuccess(objectMap);
    }


    //同步通讯录

    public Result<Void> obtainOrderDept(Integer ei, String dingCorpId, List<Dept> orderDepts, String suiteId) {
        if (ObjectUtils.isEmpty(orderDepts) || CollectionUtils.isEmpty(orderDepts)) {
            log.info("obtainOrder dept is null ei:{}", ei);
        }
        log.info("obtainOrderDept:{}", ei);
        //创建crm部门返回的result
        for (int i = 0; i < orderDepts.size(); i++) {
            Dept originDept = orderDepts.get(i);
            originDept.setName(validName(originDept.getName()));
            //如果部门有负责人，先同步负责人到待分配创建，然后再创建部门
            if (StringUtils.isNotEmpty(originDept.getDeptOwner())&&!ConfigCenter.MARKETING_SUITE_ID.equals(suiteId)) {//营销通只同步主管理人员
                List<EmployeeDingVo> dingEmp = dingManager.getDingEmp(dingCorpId, Lists.newArrayList(originDept.getDeptOwner()), suiteId);
                AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(suiteId);
                Result<Integer> crmEmpResult = cloudEmpService.cloudCreateEmp(dingEmp.get(0), ei, Long.valueOf(appParams.getAppId()), dingCorpId);
            }
            //部门在crm的数据
            Result<DeptVo> deptVoResult = cloudDeptService.queryDept(ei, originDept.getId());
            if (ObjectUtils.isNotEmpty(deptVoResult.getData())) {
                //如果不为空，说明数据已经存在。走更新部门的信息
                cloudDeptService.modifyDept(originDept, dingCorpId, ei, suiteId);
            } else {
                //不存在映射关系，就创建
                Result<Integer> createResult = cloudDeptService.createDept(originDept, dingCorpId, ei, suiteId);

            }
        }
        log.info("obtain order dept finish ei:{},corpId:{}", ei, dingCorpId);
        return Result.newSuccess();
    }

    public LicenseVersionResult queryCrmLicense(QueryProductArg queryProductArg) {
        LicenseVersionResult licenseVersionResult = licenseClient.queryProductVersion(queryProductArg);
        log.info("queryCrmLicense result:{}",licenseVersionResult);
        return licenseVersionResult;
    }


    //统一处理特殊字符或者空格的情况
    private String validName(String name) {
        String match = "[^\\\\-\\\\\\\\/\\\\[\\\\]【】()（）_a-zA-Z0-9\\u4E00-\\u9fAF\\u3400-\\u4dBF\\u3300-\\u33FF\\uF900-\\uFAFF]";
        // 创建 Pattern 对象
        Pattern r = Pattern.compile(match);
        // 现在创建 matcher 对象
        Matcher m = r.matcher(name);
        String result = m.replaceAll("-");
        return result;
    }

    //构造model创建订单
    public Result<String> createModelOrder(OrderModel orderModel, String enterpriseAccount, String crmProductId) {
        StopWatch stopWatch = StopWatch.create("createCrmOrder");
        CreateCrmOrderArg createCrmOrderArg = new CreateCrmOrderArg();
        CreateCrmOrderArg.CrmOrderDetailInfo crmOrderDetailInfo = new CreateCrmOrderArg.CrmOrderDetailInfo();
        crmOrderDetailInfo.setOrderId(orderModel.getOrderId().toString());
        crmOrderDetailInfo.setEnterpriseAccount(enterpriseAccount);
        Integer orderType = orderModel.getItemCode().equals(ConfigCenter.TRY_GOOD) ? ConfigCenter.ORDER_TRY_TYPE : ConfigCenter.ORDER_BUY_TYPE;
        crmOrderDetailInfo.setOrderTpye(orderType);
        crmOrderDetailInfo.setOrderTime(orderModel.getPaidTime());
        createCrmOrderArg.setCrmOrderDetailInfo(crmOrderDetailInfo);
        CreateCrmOrderArg.CrmOrderProductInfo crmOrderProductInfo = new CreateCrmOrderArg.CrmOrderProductInfo();
        crmOrderProductInfo.setProductId(crmProductId);
        crmOrderProductInfo.setQuantity(orderModel.getSubQuantity());
        crmOrderProductInfo.setAllResourceCount(orderModel.getSubQuantity());
        //钉钉推送的金额是分
        Double payCount = Double.parseDouble(orderModel.getPayFee().toString()) / 100;
        crmOrderProductInfo.setOrderAmount(payCount.toString());
        Date fromDate = new Date(orderModel.getServiceStartTime());
        Date endDate = new Date(orderModel.getServiceStopTime());
        crmOrderProductInfo.setBeginTime(fromDate.getTime());
        crmOrderProductInfo.setEndTime(endDate.getTime());
        createCrmOrderArg.setCrmOrderProductInfo(crmOrderProductInfo);
        Result<String> orderResult = createCrmOrder(createCrmOrderArg);
        stopWatch.lap("createOrder");
        stopWatch.log();
        log.info("create crm order arg :{} result:{},orderModel:{}", createCrmOrderArg, orderResult, orderModel);
        return orderResult;
    }

    public Result<String> createShortUrl(Integer ei, String url) {
        String crmShortUrl = ConfigCenter.CRM_SHORT_URL;

        Map<String, String> hearsMap = Maps.newHashMap();
        hearsMap.put("x-fs-ei", ei.toString());
        hearsMap.put("x-fs-userinfo", "-10000");
        hearsMap.put("Content-Type", "application/json");

        Map<String, Object> queryMap = Maps.newHashMap();
        queryMap.put("url", url);

        String result = httpCloudManager.postUrl(crmShortUrl, queryMap, hearsMap);
        log.info("createShortUrl result:{}", result);

        Object shortUrlObject = JSONPath.read(result, "$.data.shortUrl");

        if(ObjectUtils.isEmpty(shortUrlObject)) {
            return Result.newError(ResultCode.SYSTEM_ERROR);
        }

        return Result.newSuccess(shortUrlObject.toString());
    }



    public static Map<String, String> createHeader() {
        Map<String, String> headers = Maps.newHashMapWithExpectedSize(1);
        headers.put("Content-Type", "application/json");
        return headers;
    }
}
