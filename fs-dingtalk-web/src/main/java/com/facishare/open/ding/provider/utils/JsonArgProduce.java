package com.facishare.open.ding.provider.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * Created by huang<PERSON>h on 2018/8/6.
 */
@Slf4j
public class JsonArgProduce {
    public static String getJson(List<String> codes, Map<String, List<String>> values) {
        Map<String, Object> map = Maps.newHashMap();
        for (String code : codes) {
            if (CollectionUtils.isEmpty(values.get(code))) {
                continue;
            }
            //处理为空字段
            boolean empty = false;
            for (String value : values.get(code)) {
                if (Strings.isNullOrEmpty(value)) {
                    empty = true;
                }
            }
            if (empty) {
                continue;
            }
            String[] everyCode = code.split("\\.");
            if (everyCode.length == 1) {
                map.put(everyCode[0], values.get(code).get(0));
            }
            //处理二级嵌套
            if (everyCode.length == 2) {
                if (everyCode[0].endsWith("#")) {                                            //处理数组
                    String key = everyCode[0].substring(0, everyCode[0].length() - 1);
                    if (map.containsKey(key)) {
                        List<Object> sonList = (List<Object>) map.get(key);
                        for (int i = 0; i < sonList.size(); i++) {
                            Map<String, Object> sonMap = (Map<String, Object>) sonList.get(i);
                            if (values.get(code).size() < sonList.size()) {
                                sonMap.put(everyCode[1], values.get(code).get(0));
                            } else {
                                sonMap.put(everyCode[1], values.get(code).get(i));
                            }
                        }
                    } else {
                        List<Object> sonList = Lists.newArrayList();
                        for (String value : values.get(code)) {
                            Map<String, Object> sonMap = Maps.newHashMap();
                            sonMap.put(everyCode[1], value);
                            sonList.add(sonMap);
                        }
                        map.put(key, sonList);
                    }
                } else {
                    if (map.containsKey(everyCode[0])) {
                        Map<String, Object> sonMap = (Map<String, Object>) map.get(everyCode[0]);
                        sonMap.put(everyCode[1], values.get(code).get(0));
                    } else {
                        Map<String, Object> sonMap = Maps.newHashMap();
                        sonMap.put(everyCode[1], values.get(code).get(0));
                        map.put(everyCode[0], sonMap);
                    }
                }
            }
            //三级嵌套
            if (everyCode.length >= 3) {
                if (everyCode[0].endsWith("#")) {
                    //第一、第二均是Lits情况
                    if (everyCode[1].endsWith("#")) {
                        String key = everyCode[0].substring(0, everyCode[0].length() - 1);
                        String key2 = everyCode[1].substring(0, everyCode[1].length() - 1);
                        if (map.containsKey(key)) {
                            List<Object> sonList = (List<Object>) map.get(key);
                            for (int i = 0; i < sonList.size(); i++) {
                                Map<String, Object> sonMap = (Map<String, Object>) sonList.get(i);
                                if (sonMap.containsKey(key2)) {       //判断是否包含二级
                                    List<Object> sonSonList = (List<Object>) sonMap.get(key2);
                                    Map<String, Object> sonSonMap = (Map<String, Object>) sonSonList.get(0);
                                    if (everyCode.length > 3) {
                                        Map<String, Object> sonSonSonMap = Maps.newHashMap();
                                        if (values.get(code).size() < sonList.size()) {
                                            sonSonSonMap.put(everyCode[3], values.get(code).get(0));
                                        } else {
                                            sonSonSonMap.put(everyCode[3], values.get(code).get(i));
                                        }
                                        sonSonMap.put(everyCode[2], sonSonSonMap);
                                    } else {
                                        if (values.get(code).size() < sonList.size()) {
                                            sonSonMap.put(everyCode[2], values.get(code).get(0));
                                        } else {
                                            sonSonMap.put(everyCode[2], values.get(code).get(i));
                                        }
                                    }
                                } else {
                                    Map<String, Object> sonSonMap = Maps.newHashMap();
                                    List<Object> sonSonList = Lists.newArrayList();
                                    if (everyCode.length > 3) {
                                        Map<String, Object> sonSonSonMap = Maps.newHashMap();
                                        sonSonSonMap.put(everyCode[3], values.get(code).get(0));
                                        sonSonMap.put(everyCode[2], sonSonSonMap);
                                    } else {
                                        if (values.get(code).size() < sonList.size()) {
                                            sonSonMap.put(everyCode[2], values.get(code).get(0));
                                        } else {
                                            sonSonMap.put(everyCode[2], values.get(code).get(i));
                                        }
                                    }
                                    sonSonList.add(sonSonMap);
                                    sonMap.put(key2, sonSonList);
                                }
                            }
                        } else {
                            List<Object> sonList = Lists.newArrayList();
                            for (String value : values.get(code)) {         //当前只处理第一层List
                                Map<String, Object> sonMap = Maps.newHashMap();
                                Map<String, Object> sonSonMap = Maps.newHashMap();
                                if (everyCode.length > 3) {
                                    Map<String, Object> sonSonSonMap = Maps.newHashMap();
                                    sonSonSonMap.put(everyCode[3], value);
                                    sonSonMap.put(everyCode[2], sonSonSonMap);
                                } else {
                                    sonSonMap.put(everyCode[2], value);
                                }
                                List<Object> key2List = Lists.newArrayList();
                                key2List.add(sonSonMap);
                                sonMap.put(key2, key2List);
                                sonList.add(sonMap);
                            }
                            map.put(key, sonList);
                        }
                    } else {   //只有第一级是List的情况
                        String key = everyCode[0].substring(0, everyCode[0].length() - 1);
                        if (map.containsKey(key)) {
                            List<Map<String, Object>> sonList = (List<Map<String, Object>>) map.get(key);

                            for (int i = 0; i < values.get(code).size(); i++) {
                                Map<String, Object> sonMap;
                                if (i < sonList.size()) {
                                    sonMap = sonList.get(i);
                                } else {
                                    sonMap = new HashMap<>();
                                    sonList.add(sonMap);
                                }
                                if (sonMap.containsKey(everyCode[1])) {       //判断是否包含二级
                                    Map<String, Object> sonSonMap = (Map<String, Object>) sonMap.get(everyCode[1]);
                                    sonSonMap.put(everyCode[2], values.get(code).get(i));
                                } else {
                                    Map<String, Object> sonSonMap = Maps.newHashMap();
                                    sonSonMap.put(everyCode[2], values.get(code).get(i));
                                    sonMap.put(everyCode[1], sonSonMap);
                                }
                            }

//                            for (int i = 0; i < sonList.size(); i++) {
//                                Map<String, Object> sonMap = sonList.get(i);
//                                if (sonMap.containsKey(everyCode[1])) {       //判断是否包含二级
//                                    Map<String, Object> sonSonMap = (Map<String, Object>) sonMap.get(everyCode[1]);
//                                    sonSonMap.put(everyCode[2], values.get(code).get(i));
//                                } else {
//                                    Map<String, Object> sonSonMap = Maps.newHashMap();
//                                    sonSonMap.put(everyCode[2], values.get(code).get(i));
//                                    sonMap.put(everyCode[1], sonSonMap);
//                                }
//                            }
                        } else {
                            List<Object> sonList = Lists.newArrayList();
                            for (String value : values.get(code)) {
                                Map<String, Object> sonMap = Maps.newHashMap();
                                Map<String, Object> sonSonMap = Maps.newHashMap();
                                sonSonMap.put(everyCode[2], value);
                                sonMap.put(everyCode[1], sonSonMap);
                                sonList.add(sonMap);
                            }
                            map.put(key, sonList);
                        }
                    }
                } else {
                    if (map.containsKey(everyCode[0])) {
                        Map<String, Object> sonMap = (Map<String, Object>) map.get(everyCode[0]);
                        if (sonMap.containsKey(everyCode[1])) {       //判断是否包含二级
                            Map<String, Object> sonSonMap = (Map<String, Object>) sonMap.get(everyCode[1]);
                            sonSonMap.put(everyCode[2], values.get(code).get(0));
                        } else {
                            Map<String, Object> sonSonMap = Maps.newHashMap();
                            sonSonMap.put(everyCode[2], values.get(code).get(0));
                            sonMap.put(everyCode[1], sonSonMap);
                        }
                    } else {
                        Map<String, Object> sonMap = Maps.newHashMap();
                        Map<String, Object> sonSonMap = Maps.newHashMap();
                        sonSonMap.put(everyCode[2], values.get(code).get(0));
                        sonMap.put(everyCode[1], sonSonMap);
                        map.put(everyCode[0], sonMap);
                    }
                }
            }
        }
/*
* //这个字段必须放在订单产品的第一位上
                if ("FSaleOrderEntry#.FMaterialId.FNumber".equals(key)) {
                    cloudSubOrderValues.remove(key);
                    Map<String, List<String>> newCloudSubOrderValues = Maps.newLinkedHashMap();
                    if (fxValue instanceof List) {
                        oldValues.addAll((List) fxValue);
                        newCloudSubOrderValues.put(key, oldValues);
                    } else {
                        oldValues.addAll(Lists.newArrayList(String.valueOf(fxValue)));
                        newCloudSubOrderValues.put(key, oldValues);
                    }
                    newCloudSubOrderValues.putAll(cloudSubOrderValues);
                    cloudSubOrderValues = newCloudSubOrderValues;
                }
* */
        Map<String, Object> jsonArg = new HashMap();
        //因cloud接口有传参顺序，所以特殊处理订单产品
        if (map.get("FSaleOrderEntry") != null) {
            List<Map> orderEntrys = (List<Map>) map.get("FSaleOrderEntry");
            List<Map> orderEntryTrees = Lists.newArrayList();
            for (Map orderEntry : orderEntrys) {
                TreeMap treeMap = new TreeMap<>(orderEntry);
                Map<String, Object> entryMap = Maps.newLinkedHashMap();
                entryMap.put("FMaterialId", treeMap.get("FMaterialId"));
                treeMap.remove("treeMap");
                entryMap.putAll(treeMap);
                orderEntryTrees.add(entryMap);
            }
            map.put("FSaleOrderEntry", orderEntryTrees);
        }
        jsonArg.put("Model", new TreeMap<>(map));

        //特殊处理联系人,此是在TreeMap后处理
        if (map.get("FCompanyType") != null) {
            String companyType = map.get("FCompanyType").toString();
            map.remove("FCompanyType");
            Map listMap = Maps.newLinkedHashMap();
            listMap.put("FCompanyType", companyType);
            listMap.putAll(map);
            map = listMap;
            jsonArg.put("Model", map);
        }
        return JSON.toJSONString(jsonArg);
    }

    /*
    FBillTypeID.FNumber=[XSDD01_SYS],
	FCustId.FNumber=[CUST1862],
	FDate=[2018-09-1100: 00: 00],
	FNote=[纷享备注],
	FReceiveAddress=[纷享收货方地址],
	FSaleOrderEntry#.FDeliveryDate=[2018-10-0100: 00: 00],
	FSaleOrderEntry#.FMaterialId.FNumber=[00002,
	00003],
	FSaleOrderEntry#.FOrderEntryPlan#.FDetailLocAddress=[纷享收货地址],
	FSaleOrderEntry#.FQty=[1.00,
	1.00],
	FSaleOrderEntry#.FTaxPrice=[11.00,
	33.00],
	FSaleOrderEntry#.FUnitID.FNumber=[002,
	002],
	FSaleOrderFinance.FExchangeRate=[1],
	FSaleOrderFinance.FExchangeTypeId.FNumber=[HLTX01_SYS],
	FSaleOrderFinance.FSettleCurrId.FNumber=[PRE001],
	FSaleOrgId.FNumber=[000],
	FSalerId.FNumber=[10017]
    * */
    public static void main(String[] args) {
        TreeMap<String, List<String>> treeVarMap = new TreeMap<>();
        treeVarMap.put("FBillTypeID.FNumber", Lists.newArrayList("XSDD01_SYS"));
        treeVarMap.put("FCustId.FNumber", Lists.newArrayList("CUST1862"));
        treeVarMap.put("FDate", Lists.newArrayList("2018-09-1100: 00: 00"));
        treeVarMap.put("FNote", Lists.newArrayList("纷享备注"));
        treeVarMap.put("FReceiveAddress", Lists.newArrayList("纷享收货方地址"));
        treeVarMap.put("FSaleOrderEntry#.FMaterialId.FNumber", Lists.newArrayList("00002", "00003"));
        treeVarMap.put("FSaleOrderEntry#.FDeliveryDate", Lists.newArrayList("2018-10-0100: 00: 00"));
        treeVarMap.put("FSaleOrderEntry#.FOrderEntryPlan#.FDetailLocAddress", Lists.newArrayList("纷享收货地址"));
        treeVarMap.put("FSaleOrderEntry#.FQty", Lists.newArrayList("1.00", "1.00"));
        treeVarMap.put("FSaleOrderEntry#.FTaxPrice", Lists.newArrayList("11.00", "33.00"));
        treeVarMap.put("FSaleOrderEntry#.FUnitID.FNumber", Lists.newArrayList("002", "002"));
        treeVarMap.put("FSaleOrderFinance.FMarginLevel", Lists.newArrayList());
        treeVarMap.put("FSaleOrderFinance.FExchangeRate", Lists.newArrayList("1"));
        treeVarMap.put("FSaleOrderFinance.FExchangeTypeId.FNumber", Lists.newArrayList("HLTX01_SYS"));
        treeVarMap.put("FSaleOrderFinance.FSettleCurrId.FNumber", Lists.newArrayList("PRE001"));
        treeVarMap.put("FSaleOrgId.FNumber", Lists.newArrayList("000"));
        treeVarMap.put("FSalerId.FNumber", Lists.newArrayList("10017"));
        System.out.println(getJson(Lists.newArrayList(treeVarMap.keySet()), treeVarMap));
    }
}
