package com.facishare.open.ding.provider.utils;

import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.fxiaoke.cloud.DataPersistor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class BuriedSitesStatisticsUtils {

    private final static String SERVICE_NAME = "fs-erp-sync-data";

    private static Map<Integer, String> directors = new HashMap<Integer, String>() {{
        put(1, "CRM到ERP");
        put(2, "ERP到CRM");

    }};

    public static void uploadBuriedStitesLog(String tenantId,String enterpriseName,String objApiName, Integer director){
        try {
            AsyncLog(tenantId, objApiName, director, enterpriseName);
        }catch (Exception e){
            log.error("upload BuriedStitesLogBySnap failed,e:",e);
        }
    }

    private static void AsyncLog(String tenantId, String objApiName, Integer director,String enterpriseName) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("tenantId", tenantId);
        dataMap.put("objApiName", objApiName);
        dataMap.put("channel","DingTalkAll");
        dataMap.put("enterpriseName", enterpriseName);
        dataMap.put("director", directors.get(director));
        dataMap.put("trace", "EnterpriseObjectCount");
        dataMap.put("traceName", "Erp-Dss平台统计企业对象同步次数");
        DataPersistor.asyncLog(SERVICE_NAME, dataMap);
    }

}
