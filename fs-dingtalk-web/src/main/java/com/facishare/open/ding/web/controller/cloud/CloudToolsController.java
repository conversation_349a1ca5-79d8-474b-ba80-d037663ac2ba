package com.facishare.open.ding.web.controller.cloud;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.service.cloud.CloudToolsService;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.web.base.BaseController;
import com.facishare.open.ding.web.base.UserVo;
import com.facishare.open.ding.web.constants.ConfigCenter;
import com.facishare.open.ding.web.manager.ExcelListener.BaseListener;
import com.facishare.open.ding.web.manager.FileManager;
import com.facishare.open.ding.web.manager.excel.ReadExcel;
import com.facishare.uc.api.model.usertoken.User;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>钉钉云工具类接口</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2013-06-25 11:52
 */
@CrossOrigin
@Slf4j
@RestController
@RequestMapping("/tools")
public class CloudToolsController extends BaseController {
    @Resource
    private CloudToolsService cloudToolsService;
    @Autowired
    private FileManager fileManager;

    @PostMapping(value = "/department/updateDeptBind")
    public Result<Void> updateDeptBind(@RequestParam(value = "sheetName", required = false) String sheetName, MultipartFile file) {
        byte[] bytes = new byte[0];
        try {
            bytes = file.getBytes();
            InputStream inputStream = new ByteArrayInputStream(bytes);
            ReadExcel.Arg<Map<Integer, String>> arg = new ReadExcel.Arg<>();
            BaseListener<Map<Integer, String>> listen = new BaseListener<Map<Integer, String>>() {
            };
            arg.setExcelListener(listen);
            arg.setInputStream(inputStream);
            if(StringUtils.isNotEmpty(sheetName)) {
                arg.setSheetName(sheetName);
            }
            fileManager.readExcelBySheetName(arg);
            if (CollectionUtils.isEmpty(listen.getDataList())) {
                log.info("CloudToolsController.updateDeptBind,listen is emp.");
                return Result.newSuccess();
            }
            List<DeptVo> deptVos = listen.getDataList().stream().map(v -> {
                DeptVo deptVo = new DeptVo();
                deptVo.setEi(Integer.valueOf(v.get(0)));
                deptVo.setCrmDeptId(Integer.valueOf(v.get(1)));
                deptVo.setCrmParentId(Integer.valueOf(v.get(2)));
                deptVo.setCrmDeptOwner(Integer.valueOf(v.get(3)));
                deptVo.setDingDeptId(Long.valueOf(v.get(4)));
                deptVo.setDingParentId(Long.valueOf(v.get(5)));
                deptVo.setDingDeptOwner(v.get(6));
                deptVo.setName(v.get(7));
                return deptVo;
            }).collect(Collectors.toList());
            log.info("CloudToolsController.updateDeptBind,deptVos={}.", deptVos);
            cloudToolsService.updateDeptBind(deptVos);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.newSuccess();
    }

    @PostMapping(value = "/employee/deleteEmployeeBind")
    public Result<Void> deleteEmployeeBind(@RequestParam(value = "sheetName", required = false) String sheetName, MultipartFile file) {
        byte[] bytes = new byte[0];
        try {
            bytes = file.getBytes();
            InputStream inputStream = new ByteArrayInputStream(bytes);
            ReadExcel.Arg<Map<Integer, String>> arg = new ReadExcel.Arg<>();
            BaseListener<Map<Integer, String>> listen = new BaseListener<Map<Integer, String>>() {
            };
            arg.setExcelListener(listen);
            arg.setInputStream(inputStream);
            if(StringUtils.isNotEmpty(sheetName)) {
                arg.setSheetName(sheetName);
            }
            fileManager.readExcelBySheetName(arg);
            if (CollectionUtils.isEmpty(listen.getDataList())) {
                log.info("CloudToolsController.deleteEmployeeBind,listen is emp.");
                return Result.newSuccess();
            }
            Integer ei = Integer.valueOf(listen.getDataList().get(0).get(0));
            List<Integer> empIds = listen.getDataList().stream().map(v -> Integer.valueOf(v.get(1))).collect(Collectors.toList());
            log.info("CloudToolsController.deleteEmployeeBind,ei={},empIds={}.", ei, empIds);
            cloudToolsService.deleteEmployeeBind(ei, empIds);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.newSuccess();
    }

    /**
     * 跳转到钉钉工具集页面
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/dingtalktools",method = RequestMethod.GET)
    public ModelAndView dingtalktools(HttpServletResponse response,
                              HttpServletRequest request) throws IOException, ServletException {
        //进行身份校验
        UserVo userVo = getUserVo();
        log.info("CloudToolsController.dingtalktools,userVo={}.", userVo);
        if(ObjectUtils.isEmpty(userVo) || StringUtils.isEmpty(userVo.getEnterpriseAccount()) || userVo.getEmployeeId() == null) {
            request.setAttribute("errorMsg", "没有登陆纷享crm");
            request.setAttribute("propose", "请登录纷享crm，再刷新页面");
            request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
            return null;
        }
        Map<String, List<Integer>> useToolsAccountMap = new Gson().fromJson(ConfigCenter.USE_TOOLS_ACCOUNT, new TypeToken<Map<String, List<Integer>>>() {
        }.getType());
        log.info("CloudToolsController.dingtalktools,useToolsAccountMap={}.", useToolsAccountMap);
        String ea = userVo.getEnterpriseAccount();
        Integer userId = userVo.getEmployeeId();
        if(useToolsAccountMap.containsKey(ea) && useToolsAccountMap.get(ea).contains(userId)) {
            log.info("CloudToolsController.dingtalktools,ea={}.", ea);
            ModelAndView mv = new ModelAndView("/WEB-INF/tools/index.html");
            return mv;
        }
        request.setAttribute("errorMsg", "登陆的纷享crm账号没有权限");
        request.setAttribute("propose", "请登录有权限的纷享crm，再刷新页面，或者向管理员申请工具使用的权限");
        request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
        return null;
    }

    /**
     * 企微创建纷享企业情况
     * @param
     * @return
     */
    @RequestMapping(value = "/queryFsEnterpriseOpen",method =RequestMethod.GET)
    @ResponseBody
    public Result<String> queryFsEnterpriseOpen(@RequestParam String outEa) {
        return cloudToolsService.queryFsEnterpriseOpen(outEa);
    }

    /**
     * 企微创建纷享人员情况
     * @param
     * @return
     */
    @RequestMapping(value = "/queryFsEmployeeOpen",method =RequestMethod.GET)
    @ResponseBody
    public Result<String> queryFsEmployeeOpen(@RequestParam String outEa, @RequestParam String outUserId) {
        return cloudToolsService.queryFsEmployeeOpen(outEa, outUserId);
    }

    /**
     * 查询企业的绑定类型
     * @param
     * @return
     */
    @RequestMapping(value = "/queryEnterpriseBindType",method =RequestMethod.GET)
    @ResponseBody
    public Result<String> queryEnterpriseBindType(@RequestParam String fsEa) {
        return cloudToolsService.queryEnterpriseBindType(fsEa);
    }

    /**
     * 查询纷享人员当前的状态
     * @param
     * @return
     */
    @RequestMapping(value = "/queryFsEmployeeStatus",method =RequestMethod.GET)
    @ResponseBody
    public Result<String> queryFsEmployeeStatus(@RequestParam String outEa, @RequestParam String outUserId) {
        return cloudToolsService.queryFsEmployeeStatus(outEa, outUserId);
    }

    @RequestMapping(value = "/updateFsDeptOwner",method =RequestMethod.GET)
    @ResponseBody
    public Result<Void> updateFsDeptOwner(@RequestParam Integer ei, @RequestParam Integer pageSize, @RequestParam String suiteId) {
        return cloudToolsService.updateFsDeptOwner(ei, pageSize, suiteId);
    }

    @RequestMapping(value = "/updateEventStatus",method =RequestMethod.GET)
    @ResponseBody
    public Result<Void> updateEventStatus(@RequestParam(value = "event") String event,
                                          @RequestParam(value = "status") Integer status,
                                          @RequestParam(value = "timestamp") Long timestamp) {
        return cloudToolsService.updateEventStatus(event, status, timestamp);
    }

    @RequestMapping(value = "/updateEventById",method =RequestMethod.GET)
    @ResponseBody
    public Result<Integer> updateEventById(@RequestParam(value = "event") String event,
                                        @RequestParam(value = "id") Long id,
                                        @RequestParam(value = "status", required = false) Integer status,
                                        @RequestParam(value = "timestamp", required = false) Long timestamp) {
        return cloudToolsService.updateEventById(event, id, status, timestamp);
    }
}
