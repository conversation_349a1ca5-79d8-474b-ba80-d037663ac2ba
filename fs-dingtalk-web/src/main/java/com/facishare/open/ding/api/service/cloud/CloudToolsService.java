package com.facishare.open.ding.api.service.cloud;

import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.common.result.Result;

import java.util.List;

/**
 * 工具类服务
 * <AUTHOR>
 * @Date 2023/6/25 14:13
 * @Version 1.0
 */
public interface CloudToolsService {
    Result<Void> updateDeptBind(List<DeptVo> deptVos);

    Result<Void> deleteEmployeeBind(Integer ei, List<Integer> empIds);

    Result<String> queryFsEnterpriseOpen(String outEa);

    Result<String> queryFsEmployeeOpen(String outEa, String outUserId);

    Result<String> queryEnterpriseBindType(String fsEa);

    Result<String> queryFsEmployeeStatus(String outEa, String outUserId);
    
    Result<Void> updateFsDeptOwner(Integer ei, Integer pageSize, String suiteId);

    Result<Void> updateEventStatus(String event, Integer status, Long timestamp);

    Result<Integer> updateEventById(String event, Long id, Integer status, Long timestamp);
}
