package com.facishare.open.ding.provider.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.enums.DingObjTypeEnum;
import com.facishare.open.ding.api.enums.SyncDirectionEnum;
import com.facishare.open.ding.api.service.DssSyncDataMappingService;
import com.facishare.open.ding.api.vo.CropMappingVo;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.config.ConfigCenter;
import com.facishare.open.ding.provider.dao.SyncDataMappingsDao;
import com.facishare.open.ding.provider.dss.dao.DssSyncDataMappingDao;
import com.facishare.open.ding.provider.dss.entity.DssSYncDataMappingEntity;
import com.facishare.open.ding.provider.entity.SyncDataMappingsEntity;
import com.facishare.open.ding.provider.enums.SyncStatusEnums;
import com.facishare.open.ding.provider.manager.MessageManager;
import com.fxiaoke.message.extrnal.platform.api.ExternalMessageService;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service("dssSyncDataMappingServiceImpl")
public class DssSyncDataMappingServiceImpl implements DssSyncDataMappingService {

    @Autowired
    private DssSyncDataMappingDao dssSyncDataMappingDao;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private MessageManager messageManager;
    @ReloadableProperty("dingtalk.appId")
    private String appId;

    private Map<Integer,String> tenantSyncCount=new HashMap<>();

    List<String>fromCrmObjectApiNames= Arrays.asList("LeadsObj");
    List<String>toCrmObjectApiNames= Arrays.asList("AccountObj","ContactObj");
    List<String>allObjectApiName=Arrays.asList("AccountObj","ContactObj","LeadsObj");


    @Override
    public void dataMappingMove(List<CropMappingVo> cropMappingVos) {
        log.info("data:{}",JSONObject.toJSONString(cropMappingVos));
        for (CropMappingVo cropMappingVo : cropMappingVos) {
            syncDataMoveByTenantId(cropMappingVo.getTenantId(),cropMappingVo.getCropId(),1000);
        }
        for (CropMappingVo cropMappingVo : cropMappingVos) {
            countMoveInfo(cropMappingVo.getTenantId(),cropMappingVo.getCropId());
        }
        sendMessage(JSONObject.toJSONString(tenantSyncCount));
    }

    @Override
    public Map<Integer, String> getMappginCount(List<CropMappingVo> cropMappingVos) {
        if(cropMappingVos!=null){
            for (CropMappingVo cropMappingVo : cropMappingVos) {
                countMoveInfo(cropMappingVo.getTenantId(),cropMappingVo.getCropId());
            }
        }

        Map<Integer,String>rs=new HashedMap();
        rs.putAll(tenantSyncCount);
        tenantSyncCount=new HashedMap();
        return tenantSyncCount;
    }

    private void countMoveInfo(Integer tenantId, String cropId) {
        for (String objectApiName : allObjectApiName) {
            SyncDirectionEnum directionEnum=SyncDirectionEnum.FROM_FXIAOKE;
            if (toCrmObjectApiNames.contains(objectApiName)){
                directionEnum=SyncDirectionEnum.TO_FXIAOKE;
            }
            Integer dssCount;
            if (directionEnum==SyncDirectionEnum.FROM_FXIAOKE){
                dssCount =
                  dssSyncDataMappingDao.countMappingBySourceCrmObjectApiName(String.valueOf(tenantId), objectApiName);
            }else {
                dssCount= dssSyncDataMappingDao.countMappingByDestCrmObjectApiName(String.valueOf(tenantId), objectApiName);
            }

            int count =
              syncDataMappingsDao.querySuccessCount(tenantId, cropId, objectApiName, DingObjTypeEnum.getEnumByCrmApiName(objectApiName).getDingObjApiName(),2);
            tenantSyncCount.put(tenantId,String.format("企业：%s,对象:%s,在集成平台数据为%s,迁移后数据为%s",tenantId,objectApiName,dssCount,count));
        }
    }


    public void syncDataMoveByTenantId(Integer tenantId, String cropId, Integer limit){
        try{
            for (String objectApiName : allObjectApiName) {
                SyncDirectionEnum directionEnum=SyncDirectionEnum.FROM_FXIAOKE;
                if (toCrmObjectApiNames.contains(objectApiName)){
                    directionEnum=SyncDirectionEnum.TO_FXIAOKE;
                }
                Integer offset=0;
                int count = syncDataMoveByTenantIdAndObjectApiName(tenantId,cropId, objectApiName, offset, limit, directionEnum);
                while (count==limit){
                    offset+=limit;
                    count = syncDataMoveByTenantIdAndObjectApiName(tenantId,cropId, objectApiName, offset, limit, directionEnum);
                }
            }
        }catch (Exception e){
            log.error("tenantId：【{}】同步异常：",tenantId,e);
            sendMessage(String.format("当前企业【%s】同步数据异常：%s",tenantId,e.getMessage()));
            return;
        }
    }


    public int syncDataMoveByTenantIdAndObjectApiName(Integer tenantId,String cropId,String objectApiName,Integer offset,Integer limit,SyncDirectionEnum directionEnum){
        List<DssSYncDataMappingEntity> dssSYncDataMappingEntities;
        if (directionEnum==SyncDirectionEnum.FROM_FXIAOKE){
            dssSYncDataMappingEntities =
              dssSyncDataMappingDao.queryMappingBySourceCrmObjectApiName(String.valueOf(tenantId), objectApiName, offset, limit);
        }else {
            dssSYncDataMappingEntities= dssSyncDataMappingDao.queryMappingByDestCrmObjectApiName(String.valueOf(tenantId), objectApiName, offset, limit);
        }
        List<SyncDataMappingsEntity> syncDataMappingsEntities =
          buildSyncDataMappingEntity(tenantId,cropId,dssSYncDataMappingEntities, directionEnum);
        int moved = move(syncDataMappingsEntities);
        log.info("monitor:tenantId:{},cropId:{}写入{}",tenantId,cropId,moved);
        return dssSYncDataMappingEntities.size();
    }


    private List<SyncDataMappingsEntity> buildSyncDataMappingEntity(Integer tenantId,String cropId,List<DssSYncDataMappingEntity> dssSYncDataMappingEntities,
                                                                    SyncDirectionEnum directionEnum){
        List<SyncDataMappingsEntity> syncDataMappingsEntities=new ArrayList<>();
        for (DssSYncDataMappingEntity dssEntity : dssSYncDataMappingEntities) {
            SyncDataMappingsEntity syncDataMappingsEntity=new SyncDataMappingsEntity();
            syncDataMappingsEntity.setCreated(2);
            syncDataMappingsEntity.setRemark("历史数据迁移");
            syncDataMappingsEntity.setDirector(directionEnum.getType());
            syncDataMappingsEntity.setCreateTime(System.currentTimeMillis());
            syncDataMappingsEntity.setUpdateTime(System.currentTimeMillis());
            syncDataMappingsEntity.setEi(tenantId);
            syncDataMappingsEntity.setCropId(cropId);
            syncDataMappingsEntity.setMdStr("");
            syncDataMappingsEntity.setVersion(1);
            syncDataMappingsEntity.setStatus(SyncStatusEnums.SUCCESS.getStatus());
            if (directionEnum==SyncDirectionEnum.TO_FXIAOKE){
                syncDataMappingsEntity.setCrmObjectApiName(dssEntity.getDestObjectApiName());
                syncDataMappingsEntity.setDingObjectApiName(DingObjTypeEnum.getEnumByCrmApiName(syncDataMappingsEntity.getCrmObjectApiName()).getDingObjApiName());
                syncDataMappingsEntity.setCrmDataId(dssEntity.getDestDataId());
                syncDataMappingsEntity.setDingDataId(dssEntity.getSourceDataId());
            }else {
                syncDataMappingsEntity.setCrmObjectApiName(dssEntity.getSourceObjectApiName());
                syncDataMappingsEntity.setDingObjectApiName(DingObjTypeEnum.getEnumByCrmApiName(syncDataMappingsEntity.getCrmObjectApiName()).getDingObjApiName());
                syncDataMappingsEntity.setCrmDataId(dssEntity.getSourceDataId());
                syncDataMappingsEntity.setDingDataId(dssEntity.getDestDataId());
            }
            syncDataMappingsEntities.add(syncDataMappingsEntity);
        }
        return syncDataMappingsEntities;
    }

    @Transactional(rollbackFor = Exception.class)
    public int move(List<SyncDataMappingsEntity> syncDataMappingsEntities){
        Integer count=new Integer(0);
        for (SyncDataMappingsEntity syncDataMappingsEntity : syncDataMappingsEntities) {
            int insert=0;
            try {
                insert= syncDataMappingsDao.insertSyncDataMpping(syncDataMappingsEntity);
            }catch (Exception e){
                if (e instanceof org.springframework.dao.DuplicateKeyException){
                    log.warn("数据存在重复映射：{}",JSONObject.toJSONString(syncDataMappingsEntity));
                }else {
                    throw e;
                }
            }
            if (insert==0){
                log.warn("同步异常,写入失败:{}", JSONObject.toJSONString(syncDataMappingsEntities));
            }else {
                count++;
            }
        }
        return count;
    }


    private void sendMessage(String message){
        Map<String, List<Integer>> superAdminMap = ConfigCenter.SUPER_ADMINS.stream()
                                                                            .map(v -> v.split("\\."))
                                                                            .filter(v -> v.length == 2)
                                                                            .collect(Collectors.groupingBy(v -> v[0],
                                                                              Collectors.mapping(u -> Integer.valueOf(u[1]), Collectors.toList())));
        log.info("send mesage:{}",message);
        superAdminMap.forEach((ea, superUserIds) -> {

            messageManager.sendMessage(ea,superUserIds,message);
        });

    }

}
