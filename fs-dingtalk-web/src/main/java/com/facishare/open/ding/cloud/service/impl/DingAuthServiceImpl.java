package com.facishare.open.ding.cloud.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONPath;
import com.facishare.open.ding.api.arg.DingOutSendMessageArg;
import com.facishare.open.ding.api.model.AppParams;
import com.facishare.open.ding.api.result.*;
import com.facishare.open.ding.api.service.AppAuthService;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.service.cloud.CloudOrderService;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.constants.Constant;
import com.facishare.open.ding.cloud.entity.HighBizDataDo;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.cloud.manager.HttpCloudManager;
import com.facishare.open.ding.cloud.service.api.DingEventService;
import com.facishare.open.ding.cloud.utils.JSApiUtils;
import com.facishare.open.ding.cloud.utils.XorUtils;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2021/5/18 18:54
 * @Version 1.0
 */
@Service("dingAuthServiceImpl")
@Slf4j
public class DingAuthServiceImpl implements DingAuthService {
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private DingCorpMappingService corpMappingService;
    @Autowired
    private AppAuthService appAuthService;
    @Autowired
    private CloudOrderService cloudOrderService;
    @Autowired
    private DingManager dingManager;
    private String DING_ACCESS_CACHE = "ACCESS_%S";
    private String DING_JS_CACHE = "JS_%S";
    public static int DING_SUCCESS = 0;
    @Autowired
    private HttpCloudManager httpCloudManager;
    @Autowired
    @Qualifier("orderEvent17ServiceImpl")
    private DingEventService orderEventService;

    private LoadingCache<String, String> cache = Caffeine.newBuilder().maximumSize(10000).expireAfterWrite(100, TimeUnit.MINUTES).refreshAfterWrite(60, TimeUnit.MINUTES).build(new CacheLoader<String, String>() {
        @Nullable
        @Override
        public String load(@NonNull String key) throws Exception {
            return getAccessToken(key);
        }
    });

    private LoadingCache<String, String> tickCache = Caffeine.newBuilder().maximumSize(10000).expireAfterWrite(80, TimeUnit.MINUTES).refreshAfterWrite(60, TimeUnit.MINUTES).build(new CacheLoader<String, String>() {
        @Nullable
        @Override
        public String load(@NonNull String key) throws Exception {
            return getTicket(key);
        }
    });

    private String getTicket(String corpAppId) {
        String accessToken = getAccessToken(corpAppId);
        String apiUrl = "https://oapi.dingtalk.com/get_jsapi_ticket?access_token=" + accessToken;
        String result = httpCloudManager.getUrl(apiUrl, createHeader());
        Integer errCode = Integer.parseInt(JSONPath.read(result, "$.errcode").toString());
        if (Objects.isNull(errCode) || errCode != DING_SUCCESS) {
            log.warn("getToken appKey或appSecret错误.");
            return null;
        }
        String ticket = JSONPath.read(result, "$.ticket").toString();
        return ticket;
    }


    //获取accessToken
    public String getAccessToken(String authCorpSuiteId) {

        List<String> corpAndApp = Splitter.on("&").splitToList(authCorpSuiteId);
        String accessToken = dingManager.getAccessToken(corpAndApp.get(0), corpAndApp.get(1)).getData();
        log.info("getAccess token:{}", XorUtils.EncodeByXor(accessToken, ConfigCenter.XOR_SECRET_KEY));
        return accessToken;
    }


    @Override
    public Result<DingMappingEmployeeResult> queryEmpById(String dingCorpId, String dingEmpId) {

        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryCorpMappingByCorpId(dingCorpId);
        if (CollectionUtils.isEmpty(corpResult.getData())) {
            return Result.newError(ResultCode.ENTER_PRISE_INIT);
        }
        Integer ei = corpResult.getData().get(0).getEi();
        Result<DingMappingEmployeeResult> crmResult = objectMappingService.queryEmpByDingUserId(ei, dingEmpId);
        if (ObjectUtils.isEmpty(crmResult.getData())) {
            return Result.newError(ResultCode.NOT_BIND_EMP);
        }
        return crmResult;
    }

    @Override
    public Result<DingAppResult> createJsApiSignature(String url, String enterPriseAccount, Long appId) {
        //enterPrise换取CorpId;
        //TODO appId需要换成SuiteId
        Result<List<DingCorpMappingVo>> enterResult = corpMappingService.queryByEa(enterPriseAccount);
        if (CollectionUtils.isEmpty(enterResult.getData())) {
            return Result.newError(ResultCode.ENTER_PRISE_NOT_AUTH);
        }
        //获取签名参数

        appId = ObjectUtils.isEmpty(appId) ? ConfigCenter.APP_CRM_ID : appId;
        String corpId = enterResult.getData().get(0).getDingCorpId();
        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(corpId, appId, null);
        if(ObjectUtils.isEmpty(appAuthResult.getData())){
            //直接从数据库里面取
            appAuthResult = appAuthService.conditionAppAuth(corpId, null, null);
        }
        String suiteId = appAuthResult.getData().get(0).getSuiteId().toString();
        String corpToken = String.format(Constant.DING_CORP_SUITE_TOKEN, corpId, suiteId);
        String ticket = tickCache.get(corpToken);


        if (ObjectUtils.isEmpty(appAuthResult.getData())) {
            return Result.newError(ResultCode.GET_JSAPI_TICKET_FAILE);
        }
        Long agentId = appAuthResult.getData().get(0).getAgentId();
        Long timeStamp = new Date().getTime();
        String signValue = "";
        try {
            signValue = JSApiUtils.sign(ticket, ConfigCenter.NONCE_STR, timeStamp, url);
        } catch (Exception e) {
            e.printStackTrace();
        }
        DingAppResult dingAppResult = DingAppResult.builder().agentId(agentId).nonceStr(ConfigCenter.NONCE_STR).corpId(corpId).signature(signValue).timeStamp(timeStamp).build();
        return Result.newSuccess(dingAppResult);
    }

    @Override
    public Result<DingPersonResult> queryUserInfoByAuth(String authCode, String serviceCorpId, String suiteId) {
        Result<EmployeeDingVo> userByMe = dingManager.queryUserInfo(authCode, suiteId);
        if (userByMe.isSuccess()) {
            Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryCorpMappingByCorpId(serviceCorpId);
            DingPersonResult dingPersonResult = new DingPersonResult();
            dingPersonResult.setUpstreamCorpId(serviceCorpId);
            if (ObjectUtils.isNotEmpty(corpResult.getData())) {
                dingPersonResult.setUpstreamEnterpriseId(corpResult.getData().get(0).getEi());
            }
            dingPersonResult.setMobile(userByMe.getData().getMobile());
            dingPersonResult.setNick(userByMe.getData().getName());
            dingPersonResult.setUnionId(userByMe.getData().getUnionid());
            dingPersonResult.setOpenId(userByMe.getData().getUserid());
            dingPersonResult.setAvatarUrl(userByMe.getData().getAvatarUrl());
            return Result.newSuccess(dingPersonResult);
        }
        return Result.newError(ResultCode.GET_PERSON_AUTH_FAIL.getErrorCode(), userByMe.getErrorMessage());
    }

    @Override
    public Result<DingOutUserResult> queryUserByCode(String code, String appId, String corpId) {
        Result<DingOutUserResult> userByCode = dingManager.getUserByCode(code, appId, corpId);
        log.info("manager queryUserByCode:{}", userByCode);
        return userByCode;
    }

    @Override
    public Result<List<EmployeeDingVo>> queryRootUser(String dingCorpId, String suiteId) {
        List<EmployeeDingVo> employeeDingVoList = dingManager.queryUserByDept(dingCorpId, 1L, suiteId);
        return Result.newSuccess(employeeDingVoList);
    }

    @Override
    public Result<Void> mockCreateOrder(String data, String corpId) {
        HighBizDataDo bizDataDo = new HighBizDataDo();
        bizDataDo.setBizData(data);
        bizDataDo.setCorpId(corpId);
        orderEventService.executeEvent(bizDataDo);
        return Result.newSuccess();
    }

    @Override
    public Result<String> queryAccessToken(String dingCorpId, String suiteId) {
        return dingManager.getAccessToken(dingCorpId, suiteId);
    }

    @Override
    public Result<String> sendMessage(DingOutSendMessageArg dingOutSendMessageArg) {
        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryCorpMappingByCorpId(dingOutSendMessageArg.getDingCorpId());
        if (ObjectUtils.isEmpty(corpResult.getData())) {
            return Result.newError(ResultCode.ENTERPRISE_NOT_SUPPORT_APP);
        }

        String empIds = Joiner.on(",").join(dingOutSendMessageArg.getDingEmpIds());
        dingOutSendMessageArg.getDataMap().put("userIdList", empIds);
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(dingOutSendMessageArg.getSuiteId());
        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(dingOutSendMessageArg.getDingCorpId(), Long.parseLong(appParams.getAppId()), null);
        if (ObjectUtils.isEmpty(appAuthResult)) {
            return Result.newError(ResultCode.ENTERPRISE_NOT_SUPPORT_APP);
        }
        Long agentId = appAuthResult.getData().get(0).getAgentId();
        Result<String> supportResult = dingManager.supportSendMessage(agentId, dingOutSendMessageArg.getDingCorpId(), dingOutSendMessageArg.getDataMap(), dingOutSendMessageArg.getTemplateId(), dingOutSendMessageArg.getSuiteId());
        return supportResult;
    }

    @Override
    public Result<String> dingCorpIDtoEA(String dingCorpId) {
        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryCorpMappingByCorpId(dingCorpId);
        if(CollectionUtils.isEmpty(corpResult.getData())){
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        String ea=corpResult.getData().get(0).getEa();
        return Result.newSuccess(ea);
    }

    @Override
    public Result<String> EAtoDingCorpId(String ea) {
        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryByEa(ea);
        if(CollectionUtils.isEmpty(corpResult.getData())){
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        String dingCorpId=corpResult.getData().get(0).getDingCorpId();
        return Result.newSuccess(dingCorpId);
    }

    @Override
    public Result<DingCorpMappingVo> queryEnterpriseByCorpId(String dingCorpId) {
        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryCorpMappingByCorpId(dingCorpId);
        if(CollectionUtils.isEmpty(corpResult.getData())){
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        return Result.newSuccess(corpResult.getData().get(0));
    }

    @Override
    public Result<DingCorpMappingVo> queryEnterpriseByEA(String ea) {
        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryByEa(ea);
        if(CollectionUtils.isEmpty(corpResult.getData())){
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        return Result.newSuccess(corpResult.getData().get(0));
    }

    @Override
    public Result<DingOrderResult> queryLastOrder(String dingCorpId) {
        OrderInfoResult lastOrder = cloudOrderService.queryLastOrder(dingCorpId,null);
        if(ObjectUtils.isEmpty(lastOrder)){
            return Result.newError(ResultCode.NOT_ORDER_DATA);
        }
        DingOrderResult dingOrderResult= DingOrderResult.builder().corpId(dingCorpId).ea(lastOrder.getEa())
                .goodsCode(lastOrder.getGoodsCode()).itemCode(lastOrder.getItemCode()).maxPeople(lastOrder.getMaxOfPeople()).minPeople(lastOrder.getMinOfPeople())
                .orderId(lastOrder.getOrderId().toString()).payFee(lastOrder.getPayFee()).quantity(lastOrder.getSubQuantity()).
                        serviceStartTime(lastOrder.getServiceStartTime()).serviceStopTime(lastOrder.getServiceStopTime()).build();
        return Result.newSuccess(dingOrderResult);
    }

    @Override
    public Result<List<DingMappingEmployeeResult>> batchGetDingEmps(String ea, List<Integer> empIds) {
        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryByEa(ea);
        if(CollectionUtils.isEmpty(corpResult.getData())){
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        Integer ei=corpResult.getData().get(0).getEi();
        Result<List<DingMappingEmployeeResult>> resultList = objectMappingService.batchGetEmpIds(ei, empIds);
        return resultList;
    }

    @Override
    public Result<EmployeeDingVo> getPersonalData(String appId, String tmpAuthCode) {
        Result<EmployeeDingVo> personalTmpCode = dingManager.getPersonalTmpCode(appId, tmpAuthCode);
        return personalTmpCode;
    }

    @Override
    public Result<EmployeeDingVo> queryUserInfoByMe(String authCode, String appId) {
        Result<EmployeeDingVo> employeeDingVoResult = dingManager.queryUserInfo(authCode, appId);
        return employeeDingVoResult;
    }

    public static Map<String, String> createHeader() {
        Map<String, String> headers = Maps.newHashMapWithExpectedSize(1);
        headers.put("Content-Type", "application/json");
        return headers;
    }
}
