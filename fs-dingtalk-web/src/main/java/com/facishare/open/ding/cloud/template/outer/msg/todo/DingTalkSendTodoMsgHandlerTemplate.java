package com.facishare.open.ding.cloud.template.outer.msg.todo;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.result.HttpResponseMessage;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.utils.OkHttp3MonitorUtils4Cloud;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.msg.SendMsgHandlerTemplate;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 钉钉发送Todo消息模板
 * <AUTHOR>
 * @date 20240927
 */
@Slf4j
@Component
public abstract class DingTalkSendTodoMsgHandlerTemplate extends SendMsgHandlerTemplate {
    protected static String DINGTALK_TODO = "https://api.dingtalk.com/v1.0/todo/users/";

    protected void updateTodoStatus(String operator, String taskId, Map<String, String> dingTalkToken) {
        Gson gson = new Gson();
        String updateToDo = DINGTALK_TODO + operator + "/tasks/" + taskId + "?operatorId=" + operator;
        Map<String, Object> dingTalk = Maps.newHashMap();
        boolean flag = true;
        dingTalk.put("done", flag);
        HttpResponseMessage updateToDoMessageResult = OkHttp3MonitorUtils4Cloud.sendOkHttp3Put(updateToDo, dingTalkToken, gson.toJson(dingTalk));
        JSONObject jsonObject = JSONObject.parseObject(updateToDoMessageResult.getContent());
        if (updateToDoMessageResult.getStatusCode() == 200) {
            String dealResult = String.valueOf(jsonObject.get("result"));
            if("true".equals(dealResult)) {
                log.info("CloudExternalTodoServiceImpl.dealTodo,updateTodoStatus,operator={},taskId={}", operator, taskId);
            }
        }
    }

    protected Boolean isCrmBizType(String bizType){
        return ConfigCenter.CRM_TO_BIZ_TYPES.contains(bizType) || ConfigCenter.WORK_ORDER_BIZ_TYPES.contains(bizType);
    }
}
