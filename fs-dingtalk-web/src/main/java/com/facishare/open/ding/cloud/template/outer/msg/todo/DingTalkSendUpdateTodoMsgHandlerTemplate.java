package com.facishare.open.ding.cloud.template.outer.msg.todo;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.open.ding.api.result.AppAuthResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.AppAuthService;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.arg.SendCardMessageArg;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.fxiaoke.message.extrnal.platform.model.arg.UpdateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.result.UpdateTodoResult;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 钉钉发送updateTodo消息模板
 * <AUTHOR>
 * @date 20241008
 */
@Slf4j
@Component
public class DingTalkSendUpdateTodoMsgHandlerTemplate extends DingTalkSendTodoMsgHandlerTemplate {
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private DingCorpMappingService corpMappingService;
    @Autowired
    private AppAuthService appAuthService;
    @Autowired
    private DingManager dingManager;

    @Override
    public void buildMsg(MethodContext context) {
        log.info("DingTalkSendUpdateTodoMsgHandlerTemplate.buildMsg,context={}",context);
        UpdateTodoArg updateTodoArg = context.getData();

        log.info("DingTalkSendUpdateTodoMsgHandlerTemplate.buildMsg,updateTodoArg={}", updateTodoArg);
        //StopWatch stopWatch = StopWatch.create("trace createTodo:" + updateTodoArg.getEa());//处理markdown语法
        UpdateTodoResult result = new UpdateTodoResult();
        result.setCode(200);
        result.setMessage("发送成功");
        if(!isCrmBizType(updateTodoArg.getBizType())){
            //return result;
            log.info("DingTalkSendUpdateTodoMsgHandlerTemplate.buildMsg,isCrmBizType()=false,bizType={}",
                    updateTodoArg.getBizType());
            context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
            return;
        }
        StringBuilder markdown = new StringBuilder();
        if (CollectionUtils.isNotEmpty(updateTodoArg.getForm())) {
            for (int i = 0; i < updateTodoArg.getForm().size(); i++) {
                if(i==0){
                    markdown.append("<font color=\"#181C25\" style=\"line-height:22px;font-size:16px;\">"+updateTodoArg.getForm().get(i).getKey()+":"+updateTodoArg.getForm().get(i).getValue()+"</font>\n"+  " <br>\n" );
                }else{
                    markdown.append("<font color=\"#A2A3A5\" style=\"line-height:20px;font-size:14px;\">"+updateTodoArg.getForm().get(i).getKey()+"：</font><font color=\"#181C25\" style=\"line-height:20px;font-size:14px;\">"+updateTodoArg.getForm().get(i).getValue()+"</font>"+" <br>\n");
                }
            }
        }
        String objectApiName = updateTodoArg.getExtraDataMap().get("objectApiName");
        String objectId = updateTodoArg.getExtraDataMap().get("objectId");
        String instanceId = updateTodoArg.getExtraDataMap().get("workflowInstanceId");
        Integer taskId = updateTodoArg.getGenerateUrlType();
        List<Integer> userIds = updateTodoArg.getReceiverIds();
        Result<List<DingCorpMappingVo>> mappingResult = corpMappingService.queryByEi(updateTodoArg.getEi());
        Result<List<DingMappingEmployeeResult>> dataResult = objectMappingService.batchQueryMapping(updateTodoArg.getEi(), userIds);
        if(CollectionUtils.isEmpty(dataResult.getData())){
//            log.info("crm no user bind ea:{},userIds:{}",updateTodoArg.getEa(),updateTodoArg.getReceiverIds());
//            return result;
            log.info("DingTalkSendUpdateTodoMsgHandlerTemplate.buildMsg,batchQueryMapping,crm no user bind ea:{},userIds:{}",
                    updateTodoArg.getEa(),updateTodoArg.getReceiverIds());
            context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
            return;
        }
        List<String> dingEmpIds=dataResult.getData().stream().map(DingMappingEmployeeResult::getDingEmployeeId).collect(Collectors.toList());

        String dingCorpId = mappingResult.getData().get(0).getDingCorpId();
        //获取对应的agentid
        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(dingCorpId, ConfigCenter.APP_CRM_ID, null);
        if(ObjectUtils.isEmpty(appAuthResult.getData())){
//            log.info("enterprise not support send todo :{}",dingCorpId);
//            return result;
            log.info("DingTalkSendUpdateTodoMsgHandlerTemplate.buildMsg,enterprise not support send todo :{}",
                    dingCorpId);
            context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
            return;
        }
        Map<String, Object> argMap = Maps.newHashMap();
        argMap.put("objectApiName", objectApiName);
        argMap.put("objectId", objectId);
        argMap.put("instanceId", ObjectUtils.isEmpty(instanceId)?"100":instanceId);
        argMap.put("taskId", taskId);
        argMap.put("markdown", markdown);
        Long agentId = appAuthResult.getData().get(0).getAgentId();
        String empIds= Joiner.on(",").join(dingEmpIds);
        argMap.put("userIdList", empIds);
//        Result<String> messageResult = dingManager.sendCardMessage(agentId, dingCorpId, argMap, ConfigCenter.MESSAGE_CARD_ID,true,ConfigCenter.CRM_SUITE_ID);
//        return result;

        SendCardMessageArg arg = new SendCardMessageArg();
        arg.setAgentId(agentId);
        arg.setDingCorpId(dingCorpId);
        arg.setDingMessageArg(argMap);
        arg.setTemplateId(ConfigCenter.MESSAGE_CARD_ID);
        arg.setIsCardMessage(true);
        arg.setSuiteId(ConfigCenter.CRM_SUITE_ID);
//        dingManager.sendCardMessage(arg);

        context.setResult(TemplateResult.newSuccess());
        context.setData(arg);
    }

    @Override
    public void sendMsg(MethodContext context) {
        log.info("DingTalkSendUpdateTodoMsgHandlerTemplate.sendMsg,context={}",context);
        SendCardMessageArg arg = context.getData();
        Result<String> sendResult = dingManager.sendCardMessage(arg);
        log.info("DingTalkSendUpdateTodoMsgHandlerTemplate.sendMsg,sendCardMessage,sendResult={}",sendResult);

        UpdateTodoResult result = new UpdateTodoResult();
        result.setCode(200);
        result.setMessage("发送成功");

        context.setResult(TemplateResult.newSuccess(result));
    }
}
