package com.facishare.open.ding.provider.dao;

import com.facishare.open.ding.api.result.PollingSyncResult;
import com.facishare.open.ding.provider.arg.PollingSyncDataEntity;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/5/11 17:09
 * @Version 1.0
 */
public interface PollingSyncDao extends ICrudMapper<PollingSyncDataEntity> {

    @Select("select * from polling_sync_time where event_level=#{syncType}")
    PollingSyncResult queryAllResult(@Param("syncType") Integer syncType);

    @Update("update polling_sync_time set last_sync_time =#{lastSyncTime} where event_level=#{eventLevel}")
    Integer updateSyncTime(@Param("lastSyncTime") Long lastSyncTime,@Param("eventLevel") Integer level );

}
