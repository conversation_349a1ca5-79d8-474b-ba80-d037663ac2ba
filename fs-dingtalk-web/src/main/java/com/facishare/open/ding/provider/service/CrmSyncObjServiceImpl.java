package com.facishare.open.ding.provider.service;

import com.facishare.open.ding.api.CrmSyncObjVo;
import com.facishare.open.ding.api.arg.NeedSyncDataArg;
import com.facishare.open.ding.api.enums.DingObjTypeEnum;
import com.facishare.open.ding.api.model.NeedSyncDataModel;
import com.facishare.open.ding.api.result.CrmSyncObjResult;
import com.facishare.open.ding.api.service.CrmSyncObjService;
import com.facishare.open.ding.provider.arg.CrmSyncObjEntity;
import com.facishare.open.ding.provider.config.ConfigCenter;
import com.facishare.open.ding.provider.dao.CrmSyncObjDao;
import com.facishare.open.ding.provider.utils.BeanUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.annotations.Insert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/18 16:30
 * @Version 1.0
 */
@Service("crmSyncObjServiceImpl")
@Slf4j
public class CrmSyncObjServiceImpl implements CrmSyncObjService {

    @Autowired
    private CrmSyncObjDao crmSyncObjDao;

    @Override
    public CrmSyncObjResult queryNeedSyncData(NeedSyncDataModel needSyncDataModel) {
        List<CrmSyncObjEntity> crmSyncObjEntities = crmSyncObjDao.conditionNeedSynData(needSyncDataModel.getNeedSyncDataArg(), needSyncDataModel.getPageNum(), needSyncDataModel.getPageSize());
        List<CrmSyncObjVo> crmSyncObjVoList = BeanUtil.copyList(crmSyncObjEntities, CrmSyncObjVo.class);
        CrmSyncObjResult result = new CrmSyncObjResult();
        result.setCrmSyncObjVos(crmSyncObjVoList);
        result.setIsHasNextPage((CollectionUtils.isEmpty(crmSyncObjEntities) || crmSyncObjEntities.size() < needSyncDataModel.getPageSize()) ? Boolean.FALSE : Boolean.TRUE);
        return result;
    }

    @Override
    public Integer initSetting(String dingCorpId, Integer ei) {
        List<CrmSyncObjEntity> objEntities=Lists.newArrayList();
        Arrays.stream(DingObjTypeEnum.values()).forEach(item ->{
            CrmSyncObjEntity crmSyncObjEntity=new CrmSyncObjEntity();
            crmSyncObjEntity.setCorpId(dingCorpId);
            crmSyncObjEntity.setEi(ei);
            crmSyncObjEntity.setDingApiName(item.getDingObjApiName());
            crmSyncObjEntity.setCrmApiName(item.getCrmObjApiName());
            crmSyncObjEntity.setIsInit(1);
            crmSyncObjEntity.setLastSyncTime(0L);
            objEntities.add(crmSyncObjEntity);
        });
        Integer count = crmSyncObjDao.batchSaveData(objEntities);
        return count;
    }

    @Override
    public Integer updateStatus(NeedSyncDataArg needSyncDataArg) {
        Integer count = crmSyncObjDao.updateInit(needSyncDataArg);
        return count;
    }

    @Override
    public Integer queryIsSyncData(String dingCorpId) {
        Integer count = crmSyncObjDao.queryIsSyncData(dingCorpId);
        return count;
    }

    @Override
    public Integer updateLastSyncTIme(NeedSyncDataArg needSyncDataArg) {
        Integer count = crmSyncObjDao.updateLastSyncTIme(needSyncDataArg);
        return count;
    }
}
