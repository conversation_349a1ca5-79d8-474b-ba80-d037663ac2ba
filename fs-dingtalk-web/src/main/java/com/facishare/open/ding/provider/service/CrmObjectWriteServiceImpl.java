package com.facishare.open.ding.provider.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.open.ding.api.enums.DingObjTypeEnum;
import com.facishare.open.ding.api.enums.SyncDirectionEnum;
import com.facishare.open.ding.api.model.StandardData;
import com.facishare.open.ding.api.result.HttpResponseMessage;
import com.facishare.open.ding.api.service.CrmObjectWriterService;
import com.facishare.open.ding.api.service.ObjectHandleService;
import com.facishare.open.ding.api.utils.MD5Util;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.provider.crm.CrmUrlUtils;
import com.facishare.open.ding.provider.dao.SyncDataMappingsDao;
import com.facishare.open.ding.provider.entity.SyncDataMappingsEntity;
import com.facishare.open.ding.provider.enums.SyncStatusEnums;
import com.facishare.open.ding.provider.manager.FiledConverMananger;
import com.facishare.open.ding.provider.manager.SyncDataMananger;
import com.facishare.open.ding.provider.utils.BuriedSitesStatisticsUtils;
import com.facishare.open.ding.provider.utils.OkHttp3MonitorUtils;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service("crmObjectWriteServiceImpl")
public class CrmObjectWriteServiceImpl implements CrmObjectWriterService {

    @Autowired
    private ObjectHandleServiceFactory objectHandleServiceFactory;

    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;

    @Autowired
    private SyncDataMananger syncDataMananger;

    @Override
    public Result writeCrmObjectData(Integer ei, String corpId,StandardData standardData) {

        String crmObjApiName = standardData.getObjAPIName();
        String dingObjApiName = DingObjTypeEnum.getEnumByCrmApiName(crmObjApiName).getDingObjApiName();

        ObjectHandleService objectHandleService =
          objectHandleServiceFactory.getSpecicalObjectHandleServiceMap(crmObjApiName);

        if (objectHandleService==null){
            return Result.newError(ResultCode.NOT_SUPPORT_OBJECT);
        }

        //获取id映射记录，这行代码别乱移动。。。
        String instanceId=standardData.getMasterFieldVal().get("_id").toString();

        //字段转换
        standardData=FiledConverMananger.converDataField(ei,corpId,standardData);

        //特殊字段处理
        objectHandleService.handlerSpecialObjectHandler(ei,corpId,standardData);

        //判断中间表状态
        //获取当前的同步方向的映射
        SyncDataMappingsEntity syncDataMappingsEntity=null;
        List<SyncDataMappingsEntity> syncDataMappingsEntitys=syncDataMananger.loadSyncDataMapping(ei,corpId,crmObjApiName,dingObjApiName, SyncDirectionEnum.TO_FXIAOKE,instanceId);
        String mdstr=MD5Util.getMD5(JSONObject.toJSONString(standardData));

        if (CollectionUtils.isEmpty(syncDataMappingsEntitys)){
            syncDataMappingsEntity=
              SyncDataMappingsEntity.builder().ei(ei)
                                    .cropId(corpId)
                                    .crmObjectApiName(crmObjApiName)
                                    .dingObjectApiName(dingObjApiName)
                                    .crmDataId(ei+UUID.randomUUID().toString())
                                    .dingDataId(instanceId)
                                    .director(SyncDirectionEnum.TO_FXIAOKE.getType())
                                    .mdStr(mdstr)
                                    .status(SyncStatusEnums.ERROR.getStatus())
                                    .version(1)
                                    .created(SyncStatusEnums.CREATED_FAILED.getStatus())
                                    .remark("创建中")
                                    .createTime(System.currentTimeMillis())
                                    .updateTime(System.currentTimeMillis())
                                    .build();
            int insert = syncDataMananger.save(syncDataMappingsEntity);
            if (insert<=0){
                return Result.newError("中间表创建失败");
            }
            syncDataMappingsEntitys=syncDataMananger.loadSyncDataMapping(ei,corpId,crmObjApiName,dingObjApiName, SyncDirectionEnum.TO_FXIAOKE,instanceId);

        }
        Optional<SyncDataMappingsEntity> first = syncDataMappingsEntitys.stream()
                                                                        .filter(t -> t.getDirector()
                                                                          == SyncDirectionEnum.TO_FXIAOKE.getType())
                                                                        .findFirst();
        Optional<SyncDataMappingsEntity> created = syncDataMappingsEntitys.stream()
                                                                        .filter(t -> t.getCreated()
                                                                          ==SyncStatusEnums.CREATED.getStatus())
                                                                        .findFirst();

        if (first.isPresent()){
            syncDataMappingsEntity=first.get();
        }else {
            return Result.newError("中间表创建失败");
        }

        //双向都没有映射记录走新增逻辑,或者双向失败
        Result<String> result;
        if (!created.isPresent()){
            //新增一个记录
            //后面支持从对象同步。在加特殊接口方法处理
            result= createCrmObject(String.valueOf(ei),standardData.getObjAPIName(), standardData.getMasterFieldVal());
        }else{
            if (syncDataMappingsEntity.getCreated()==SyncStatusEnums.CREATED.getStatus()){
                if (mdstr.equals(syncDataMappingsEntity.getMdStr())){
                    log.info("当前数据未发生变化：{}",standardData);
                    return Result.newSuccess();
                }
            }
            //后面支持从对象同步。在加特殊接口方法处理
            result = updateCrmObject(String.valueOf(ei),standardData.getObjAPIName(),created.get().getCrmDataId(), standardData.getMasterFieldVal());
        }


       syncDataMappingsEntity.setCrmDataId(syncDataMappingsEntity.getCrmDataId());
       syncDataMappingsEntity.setUpdateTime(System.currentTimeMillis());
       if (result.isSuccess()){
           syncDataMappingsEntity.setStatus(SyncStatusEnums.SUCCESS.getStatus());
           syncDataMappingsEntity.setCreated(SyncStatusEnums.CREATED.getStatus());
           syncDataMappingsEntity.setCrmDataId(result.getData());
           syncDataMappingsEntity.setRemark("同步成功");
       }else {
           syncDataMappingsEntity.setStatus(SyncStatusEnums.ERROR.getStatus());
           syncDataMappingsEntity.setRemark(result.getErrorMessage());
       }
       int update =syncDataMappingsDao.updateSyncDataMappingById(syncDataMappingsEntity.getCrmDataId(),syncDataMappingsEntity.getDingDataId(),
         mdstr,syncDataMappingsEntity.getStatus(),syncDataMappingsEntity.getRemark(),
         System.currentTimeMillis(),syncDataMappingsEntity.getVersion(),syncDataMappingsEntity.getCreated(),syncDataMappingsEntity.getId());
       //int update = syncDataMappingsDao.update(syncDataMappingsEntity);
       if (update<=0){
           log.warn("同步crm成功，更新中间表映射失败");
       }
       return result;
    }

    public Result<String> createCrmObject(String ei,String objectApiName,ObjectData objectData){

        Map<String,String> hearsMap= Maps.newHashMap();
        hearsMap.put("x-fs-ei",ei);
        hearsMap.put("x-fs-userinfo","-10000");
        if (!objectData.containsKey("default__c")){
            objectData.put("record_type","default__c");
        }
        objectData.put("object_describe_api_name",objectApiName);
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("object_data", objectData);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(CrmUrlUtils.buildCrmAddUrl(objectApiName), hearsMap, JSONObject
          .toJSONString(paramMap));
        Integer code=Integer.parseInt(JSONPath.read(httpResponseMessage.getContent(), "$.code").toString());
        String message=JSONPath.read(httpResponseMessage.getContent(), "$.message").toString();
        log.info("trace ei:{}, objectData:{}, crm createDept message:{}",ei,objectData,httpResponseMessage.getContent());
        if(ResultCode.SUCCESS.getErrorCode().equals(code)){
            Object deptId = JSONPath.read(httpResponseMessage.getContent(), "$.data.objectData._id");
            return Result.newSuccess(deptId.toString());
        }
        return Result.newError(code,message);
    }

    public Result<String> updateCrmObject(String ei,String objectApiName,String crmDataId,ObjectData objectData){

        Map<String,String> hearsMap= Maps.newHashMap();
        hearsMap.put("x-fs-ei",ei);
        hearsMap.put("x-fs-userinfo","-10000");
        if (!objectData.containsKey("default__c")){
            objectData.put("record_type","default__c");
        }
        objectData.put("_id",crmDataId);
        objectData.put("object_describe_api_name",objectApiName);
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("object_data", objectData);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(CrmUrlUtils.buildCrmUpdateUrl(objectApiName), hearsMap, JSONObject
          .toJSONString(paramMap));
        Integer code=Integer.parseInt(JSONPath.read(httpResponseMessage.getContent(), "$.code").toString());
        String message=JSONPath.read(httpResponseMessage.getContent(), "$.message").toString();
        log.info("trace ei:{}, objectData:{}, crm createDept message:{}",ei,objectData,httpResponseMessage.getContent());
        if(ResultCode.SUCCESS.getErrorCode().equals(code)){
            Object deptId = JSONPath.read(httpResponseMessage.getContent(), "$.data.objectData._id");
            return Result.newSuccess(deptId.toString());
        }
        return Result.newError(code,message);
    }

}
