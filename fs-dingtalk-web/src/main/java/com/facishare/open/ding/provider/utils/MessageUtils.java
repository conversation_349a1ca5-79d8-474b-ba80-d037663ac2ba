package com.facishare.open.ding.provider.utils;

import com.facishare.open.ding.provider.model.result.SyncResult;
import com.facishare.open.ding.api.enums.ModuleTypeEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * Created by system on 2018/6/14.
 */
@Slf4j
public class MessageUtils {

    public static final int INVALID = -1;

    public static final int INIT = 0;

    public static final int SYNC = 1;

    public static final int PRODUCT_CATEGORY = 1;

    public static final int PRODUCT_UNIT = 2;


    /**
     * 拼接消息提示，针对产品，客户，销售订单
     * @param module
     * @param type
     * @param result
     * @return
     */
    public static String format(ModuleTypeEnum module, int type, SyncResult result) {
        StringBuilder message = new StringBuilder();

        if (module == ModuleTypeEnum.PRODUCT) {
            message.append("产品");
        } else if (module == ModuleTypeEnum.CUSTOMER) {
            message.append("客户");
        } else if (module == ModuleTypeEnum.SALESORDER) {
            message.append("销售订单");
        } else if (module == ModuleTypeEnum.PAYMENT) {
            message.append("回款");
        } else if (module == ModuleTypeEnum.ERPSTOCK) {
            message.append("ERP库存");
        }else if(module == ModuleTypeEnum.STOCK){
            message.append("库存");
        } else if (module == ModuleTypeEnum.CONTACT) {
            message.append("联系人");
        } else if (module == ModuleTypeEnum.INVOICE) {
            message.append("开票");
        } else if(module == ModuleTypeEnum.DELIVERYNOTE){
            message.append("发货单");
        }

        if (type == INIT) {
            message.append("初始化");
        } else if (type == INVALID) {
            message.append("作废");
        } else if (type == SYNC) {
            message.append("同步");
        } else {
            message.append("未知");
        }

        if (result.getCode() == SyncResult.FAILURE) {
            message.append("中止，等待下次重试，原因：").append(result.getMessage());
        } else if (result.getCode() == SyncResult.SUCCESS) {
            if (result.getSuccess() > 0) {
                message.append("成功").append(result.getSuccess()).append("条，");
            }
            if (result.getFailure() > 0) {
                message.append("失败").append(result.getFailure()).append("条，");
            }
            message.append("详情请参考业务日志管理页面");
        } else if (result.getCode() == SyncResult.NO_NEED) {
            message.append("数据不存在");
        } else {
            message.append("异常，原因：").append(result.getMessage());
        }

        return message.toString();
    }

    /**
     * 拼接消息提示，针对产品分类，产品单位
     * @param module
     * @param type
     * @param result
     * @return
     */
    public static String specialFormat(int module, int type, SyncResult result) {
        StringBuilder message = new StringBuilder();

        if (module == PRODUCT_CATEGORY) {
            message.append("产品分类");
        } else {
            message.append("产品单位");
        }

        if (type == INIT) {
            message.append("初始化");
        } else {
            message.append("同步");
        }

        if (result.getCode() == SyncResult.FAILURE) {
            message.append("失败，数据同步中断，等待下次重试，原因：").append(result.getMessage());
        }

        return message.toString();
    }

}
