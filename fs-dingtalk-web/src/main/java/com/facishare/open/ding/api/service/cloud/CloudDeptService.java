package com.facishare.open.ding.api.service.cloud;

import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.result.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/14 11:34
 * @Version 1.0
 */
public interface CloudDeptService {

    //查询部门
    Result<DeptVo> queryDept(Integer ei,Long deptId);

    //创建部门
    Result<Integer> createDept(Dept dept,String dingCorpId,Integer ei,String suiteId);

    //修改部门
   Result<Integer> modifyDept(Dept dept,String dingCorpId,Integer ei,String suiteId);

    //删除部门
    Result<Integer> removeDept(Long deptId,String dingCorpId,Integer ei);

    //查询部门总数（用作是否初始化企业的判断）

    Result<Void> updateDeptBind(List<DeptVo> deptVos);

    Result<List<DeptVo>> queryDepartments(Integer ei, Integer page, Integer size);

    Result<Integer> updateDeptVo(DeptVo vo);
}
