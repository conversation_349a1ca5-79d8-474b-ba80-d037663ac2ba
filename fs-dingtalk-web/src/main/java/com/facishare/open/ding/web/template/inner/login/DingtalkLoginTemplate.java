package com.facishare.open.ding.web.template.inner.login;

import com.alibaba.fastjson.JSONPath;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.web.manager.DingtalkApiManager;
import com.facishare.open.ding.web.template.model.GetOutUserInfoByCodeArg;
import com.facishare.open.ding.web.template.model.OutUserModel;
import com.facishare.open.ding.web.utils.HttpManager;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.inner.login.LoginTemplate;
import com.facishare.userlogin.api.model.CreateUserTokenDto;
import com.facishare.userlogin.api.service.SSOLoginService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class DingtalkLoginTemplate extends LoginTemplate {
    @Resource
    private SSOLoginService ssoLoginService;
    @Resource
    private DingtalkApiManager dingtalkApiManager;

    @Override
    public void getOutUserInfoByCode(MethodContext context) {
        log.info("DingtalkLoginTemplate.getOutUserInfoByCode,context={}", context);
        GetOutUserInfoByCodeArg arg = context.getData();
        Result<OutUserModel> result = dingtalkApiManager.getOutUserInfoByCode(arg);
        log.info("DingtalkLoginTemplate.getOutUserInfoByCode,result={}", result);
        context.setResult(TemplateResult.newSuccess(result));
    }

    @Override
    public void genFsTicket(MethodContext context) {
        log.info("DingtalkLoginTemplate.genFsTicket,context={}", context);
        //钉钉不需要实现
        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void getFsUserInfoByTicket(MethodContext context) {
        log.info("DingtalkLoginTemplate.getFsUserInfoByTicket,context={}", context);
        //钉钉不需要实现
        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void createUserToken(MethodContext context) {
        super.createUserToken(context);
        log.info("DingtalkLoginTemplate.createUserToken,context={}", context);
        CreateUserTokenDto.Argument userTokenArg = context.getData();
        CreateUserTokenDto.Result result = ssoLoginService.createUserToken(userTokenArg);
        log.info("DingtalkLoginTemplate.createUserToken,result={}", result);
        context.setResult(TemplateResult.newSuccess(result));
    }
}
