package com.facishare.open.ding.web.base;


import com.facishare.open.ding.api.exception.DingtalkException;
import com.facishare.open.ding.web.ajax.AjaxCode;
import com.facishare.open.ding.web.ajax.AjaxResult;
import com.google.common.base.Strings;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>controller的父类</p>
 *
 * @<NAME_EMAIL>
 * @version 1.0
 * @dateTime 2017/9/11 15:56
 */
public abstract class BaseController {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    public UserVo getUserVo() {
        return UserContextHolder.get().get();
    }

    protected void checkParamNotBlank(Object obj, String message) {
        if (null == obj) {
            throw new DingtalkException(AjaxCode.PARAM_ERROR, message);
        }

        if (obj instanceof String && Strings.isNullOrEmpty((String) obj)) {
            throw new DingtalkException(AjaxCode.PARAM_ERROR, message);
        }
    }

    protected void checkParamTrue(boolean trueValue, String message) {
        if (!trueValue) {
            throw new DingtalkException(AjaxCode.PARAM_ERROR, message);
        }
    }

    protected void checkParamRegex(String obj, String regex, String message) {
        checkParamNotBlank(obj, message);
        obj = obj.trim();
        if (null != regex && !obj.matches(regex)) {
            throw new DingtalkException(AjaxCode.PARAM_ERROR, message);
        }
    }

    /**
     * 异常通用处理
     *
     * @param request 请求.
     * @param e       异常
     * @throws Exception
     */
    @ExceptionHandler
    @ResponseBody
    public AjaxResult exception(HttpServletRequest request, Exception e) throws Exception {
        if (e instanceof DingtalkException) {
            logger.warn("req [ " + request.getRequestURI() + " ] " + e.getLocalizedMessage(), e);
            DingtalkException biz = (DingtalkException) e;
            return new AjaxResult(biz.getErrCode(), biz.getErrDescription());
        }
        logger.error("req [ " + request.getRequestURI() + " ] " + e.getLocalizedMessage(), e);
        return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "系统异常");
    }

}
