package com.facishare.open.ding.provider.service;

import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.vo.UserMappingVo;
import com.facishare.open.ding.common.utils.BeanUtil;
import com.facishare.open.ding.provider.arg.UserMappingEntity;
import com.facishare.open.ding.api.result.UserAppResult;
import com.facishare.open.ding.api.service.UserAppMappingService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.dao.DingMappingEmployeeDao;
import com.facishare.open.ding.provider.dao.UserAppMappingDao;
import com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/5/10 15:49
 * @Version 1.0
 */
@Service("userAppMappingServiceImpl")
public class UserAppMappingServiceImpl implements UserAppMappingService {
    
    @Autowired
    private UserAppMappingDao userAppMappingDao;
    @Autowired
    private DingMappingEmployeeDao dingMappingEmployeeDao;
    
    @Override
    public Result<List<UserAppResult>> queryByDingEmpId(String dingCorpId,Long appId, String dingEmpId) {
       List<UserAppResult> listResult = userAppMappingDao.conditionQueryMapping(dingCorpId, appId,dingEmpId);
        return Result.newSuccess(listResult);
    }

    @Override
    public Result<List<UserAppResult>> conditionQueryEmp(String dingCorpId, String dingEmpId) {
        List<UserAppResult> userAppResults = userAppMappingDao.conditionQueryMapping(dingCorpId, null, dingEmpId);
        return Result.newSuccess(userAppResults);
    }

    @Override
    public Result<Integer> insertDingEmp(UserMappingVo userMappingArg) {
        UserMappingEntity userMappingEntity=new UserMappingEntity();
        BeanUtils.copyProperties(userMappingArg,userMappingEntity);
        int count = userAppMappingDao.insert(userMappingEntity);
        return Result.newSuccess(count);
    }

    @Override
    public Result<List<UserAppResult>> queryMappingByAppId(String dingCorpId, Long appId) {
        List<UserAppResult> mappingResult = userAppMappingDao.conditionQueryMapping(dingCorpId, appId,null);
        return Result.newSuccess(mappingResult);
    }

    @Override
    public Result<Integer> removeEmp(String dingCorpId, Long appId, String dingEmpId) {

        int count = userAppMappingDao.deleteByAppId(dingCorpId,dingEmpId,appId);
        return Result.newSuccess(count);
    }

    @Override
    public Result<Integer> fixEmp(Integer empId) {

        List<UserAppResult> userAppResults = userAppMappingDao.conditionQueryMappingByEmpId(empId);
        userAppResults.forEach(item ->{
            DingMappingEmployeeResult dingEmployeeId = dingMappingEmployeeDao.findDingEmployeeId(item.getEi(), item.getDingEmpId());

           Integer count = userAppMappingDao.updateEmpByEmpId(dingEmployeeId.getEmployeeId().toString(), dingEmployeeId.getDingEmployeeId(), item.getEi());
        });
        return Result.newSuccess(userAppResults.size());
    }
}
