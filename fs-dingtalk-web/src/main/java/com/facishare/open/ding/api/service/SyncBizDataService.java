package com.facishare.open.ding.api.service;

import com.facishare.open.ding.api.vo.HighBizDataVo;
import com.facishare.open.ding.common.result.Result;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/4/28 14:54
 * @Version 1.0
 */
public interface SyncBizDataService {

    //保存数据
    Result<Integer> batchSaveData(List<HighBizDataVo> voList);

    //批量查询高级数据数据
    Result<List<HighBizDataVo>> batchHighQueryData();

    //批量查询中低级数据
    Result<List<HighBizDataVo>> batchMediumQueryData();

    //批量查询高级数据数据
    Result<List<HighBizDataVo>> batchHighQueryDataTem(Long lastSyncTime,Long nowTime);

    //批量查询中低级数据
    Result<List<HighBizDataVo>> batchMediumQueryDataTem(Long lastSyncTime,Long nowTime);

    //查询数据
    Result<List<HighBizDataVo>> selectBizTypeByCorpId(Integer bizType,String corpId);

    //返回suite_ticket
    Result<String> queryByTicket(String suiteId);

    Result<String> queryToken(String dingCorpId,String suiteId);

    Result<Integer> getUpdateSql(String nowTime,Integer id);

    Result<Integer> updateSql(String sql);

    Result<Integer> insertSql(String sql);

    Result<String> getDingCorpId(String name);

    Result<Integer> fixEmp(Integer empId);
    //查询指定的
    Result<HighBizDataVo> selectByBizType(String suiteId,String bizType,String corpId);

    Result<Integer> fixOrderEventData(String dingCorpId,Long startTime,Long endTime);
}

