package com.facishare.open.ding.cloud.service.impl;

import com.facishare.open.ding.cloud.template.outer.msg.todo.DingTalkSendCreateTodoMsgHandlerTemplate;
import com.facishare.open.ding.cloud.template.outer.msg.todo.DingTalkSendDealTodoMsgHandlerTemplate;
import com.facishare.open.ding.cloud.template.outer.msg.todo.DingTalkSendDeleteTodoMsgHandlerTemplate;
import com.facishare.open.ding.cloud.template.outer.msg.todo.DingTalkSendUpdateTodoMsgHandlerTemplate;
import com.fxiaoke.message.extrnal.platform.api.ExternalTodoService;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.UpdateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.result.CreateTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DealTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DeleteTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.UpdateTodoResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2021/5/10 19:16
 * @Version 1.0
 */
@Service("cloudExternalTodoServiceImpl")
@Slf4j
public class CloudExternalTodoServiceImpl implements ExternalTodoService {

//    @Autowired
//    private DingManager dingManager;
//    @Autowired
//    private AppAuthService appAuthService;
//    @Autowired
//    private DingCorpMappingService corpMappingService;
//    @Autowired
//    private ObjectMappingService objectMappingService;
    @Resource
    private DingTalkSendCreateTodoMsgHandlerTemplate dingTalkSendCreateTodoMsgHandlerTemplate;
    @Resource
    private DingTalkSendUpdateTodoMsgHandlerTemplate dingTalkSendUpdateTodoMsgHandlerTemplate;
    @Resource
    private DingTalkSendDealTodoMsgHandlerTemplate dingTalkSendDealTodoMsgHandlerTemplate;
    @Resource
    private DingTalkSendDeleteTodoMsgHandlerTemplate dingTalkSendDeleteTodoMsgHandlerTemplate;

    //private static String DINGTALK_TODO = "https://api.dingtalk.com/v1.0/todo/users/";

    @Override
    public synchronized CreateTodoResult createTodo(CreateTodoArg createTodoArg) {
//        log.info("external todo message arg:{}", createTodoArg);
//        CreateTodoResult result = new CreateTodoResult();
//        result.setCode(200);
//        result.setMessage("发送成功");
//        Result<List<DingCorpMappingVo>> mappingResult = corpMappingService.queryByEi(createTodoArg.getEi());
//        if(!mappingResult.isSuccess() || CollectionUtils.isEmpty(mappingResult.getData())){
//            log.info("crm no bind, ea:{}.",createTodoArg.getEa());
//            return result;
//        }
//        //钉钉待办
//        if(!isCrmBizType(createTodoArg.getBizType())){
//            return result;
//        }
//        if(ConfigCenter.OA_GRAY_TENANTS.contains(createTodoArg.getEa())){
//            if(ConfigCenter.WORK_ORDER_BIZ_TYPES.contains(createTodoArg.getBizType())) {
//                if(!ConfigCenter.OA_WORK_ORDER_TENANTS.contains(createTodoArg.getEa())) {
//                    return result;
//                }
//            }
//            return dingManager.createTodo(createTodoArg);
//        }
//        StopWatch stopWatch = StopWatch.create("trace createTodo:" + createTodoArg.getEa());//处理markdown语法
//        StringBuilder markdown = new StringBuilder();
//        if (CollectionUtils.isNotEmpty(createTodoArg.getForm())) {
//            for (int i = 0; i < createTodoArg.getForm().size(); i++) {
//                if(i==0){
//                    markdown.append("<font color=\"#181C25\" style=\"line-height:22px;font-size:16px;\">"+createTodoArg.getForm().get(i).getKey()+":"+createTodoArg.getForm().get(i).getValue()+"</font>\n"+  " <br>\n" );
//                }else{
//                    markdown.append("<font color=\"#A2A3A5\" style=\"line-height:20px;font-size:14px;\">"+createTodoArg.getForm().get(i).getKey()+"：</font><font color=\"#181C25\" style=\"line-height:20px;font-size:14px;\">"+createTodoArg.getForm().get(i).getValue()+"</font>"+" <br>\n");
//                }
//            }
//        }
//        String objectApiName = createTodoArg.getExtraDataMap().get("objectApiName");
//        String objectId = createTodoArg.getExtraDataMap().get("objectId");
//        String instanceId = createTodoArg.getExtraDataMap().get("workflowInstanceId");
//        Integer taskId = createTodoArg.getGenerateUrlType();
//        List<Integer> userIds = createTodoArg.getReceiverIds();
//        Result<List<DingMappingEmployeeResult>> dataResult = objectMappingService.batchQueryMapping(createTodoArg.getEi(), userIds);
//        if(CollectionUtils.isEmpty(dataResult.getData())){
//            log.info("crm no user bind ea:{},userIds:{}",createTodoArg.getEa(),createTodoArg.getReceiverIds());
//            return result;
//        }
//        List<String> dingEmpIds=dataResult.getData().stream().map(DingMappingEmployeeResult::getDingEmployeeId).collect(Collectors.toList());
//        String dingCorpId = mappingResult.getData().get(0).getDingCorpId();
//        //获取对应的agentid
//        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(dingCorpId, ConfigCenter.APP_CRM_ID, null);
//        Map<String, Object> argMap = Maps.newHashMap();
//        argMap.put("apiname", objectApiName);
//        argMap.put("objectId", objectId);
//        argMap.put("instanceId", ObjectUtils.isEmpty(instanceId)?"100":instanceId);
//        argMap.put("taskId", taskId);
//        argMap.put("markdown", markdown);
//        argMap.put("ei", createTodoArg.getEi());
//        argMap.put("bizType", createTodoArg.getBizType());
//        String empIds= Joiner.on(",").join(dingEmpIds);
//        argMap.put("userIdList", empIds);
//        // 需要判断是否购买了纷享钉钉CRM。其他应用的不发送
//        if(ObjectUtils.isEmpty(appAuthResult.getData())){
//            log.info("enterprise not support send todo :{}",dingCorpId);
//            return result;
//        }
//        Long agentId = appAuthResult.getData().get(0).getAgentId();
//
//        Result<String> messageResult = dingManager.sendCardMessage(agentId, dingCorpId, argMap, ConfigCenter.MESSAGE_CARD_ID,true,ConfigCenter.CRM_SUITE_ID);
//        log.info("create to do arg :{}，result:{}",argMap,messageResult);
//
//        SendCardMessageArg arg = new SendCardMessageArg();
//        arg.setAgentId(agentId);
//        arg.setDingCorpId(dingCorpId);
//        arg.setDingMessageArg(argMap);
//        arg.setTemplateId(ConfigCenter.MESSAGE_CARD_ID);
//        arg.setIsCardMessage(true);
//        arg.setSuiteId(ConfigCenter.CRM_SUITE_ID);
//        dingManager.sendCardMessage(arg);
//        return result;

        CreateTodoResult result = (CreateTodoResult) dingTalkSendCreateTodoMsgHandlerTemplate.execute(createTodoArg).getData();
        return result;
    }




    @Override
    public UpdateTodoResult updateTodo(UpdateTodoArg updateTodoArg) {
//        log.info("external updateTodo message arg:{}", updateTodoArg);
//        StopWatch stopWatch = StopWatch.create("trace createTodo:" + updateTodoArg.getEa());//处理markdown语法
//        UpdateTodoResult result = new UpdateTodoResult();
//        result.setCode(200);
//        result.setMessage("发送成功");
//        if(!isCrmBizType(updateTodoArg.getBizType())){
//            return result;
//        }
//        StringBuilder markdown = new StringBuilder();
//        if (CollectionUtils.isNotEmpty(updateTodoArg.getForm())) {
//            for (int i = 0; i < updateTodoArg.getForm().size(); i++) {
//                if(i==0){
//                    markdown.append("<font color=\"#181C25\" style=\"line-height:22px;font-size:16px;\">"+updateTodoArg.getForm().get(i).getKey()+":"+updateTodoArg.getForm().get(i).getValue()+"</font>\n"+  " <br>\n" );
//                }else{
//                    markdown.append("<font color=\"#A2A3A5\" style=\"line-height:20px;font-size:14px;\">"+updateTodoArg.getForm().get(i).getKey()+"：</font><font color=\"#181C25\" style=\"line-height:20px;font-size:14px;\">"+updateTodoArg.getForm().get(i).getValue()+"</font>"+" <br>\n");
//                }
//            }
//        }
//        String objectApiName = updateTodoArg.getExtraDataMap().get("objectApiName");
//        String objectId = updateTodoArg.getExtraDataMap().get("objectId");
//        String instanceId = updateTodoArg.getExtraDataMap().get("workflowInstanceId");
//        Integer taskId = updateTodoArg.getGenerateUrlType();
//        List<Integer> userIds = updateTodoArg.getReceiverIds();
//        Result<List<DingCorpMappingVo>> mappingResult = corpMappingService.queryByEi(updateTodoArg.getEi());
//        Result<List<DingMappingEmployeeResult>> dataResult = objectMappingService.batchQueryMapping(updateTodoArg.getEi(), userIds);
//        if(CollectionUtils.isEmpty(dataResult.getData())){
//            log.info("crm no user bind ea:{},userIds:{}",updateTodoArg.getEa(),updateTodoArg.getReceiverIds());
//            return result;
//        }
//        List<String> dingEmpIds=dataResult.getData().stream().map(DingMappingEmployeeResult::getDingEmployeeId).collect(Collectors.toList());
//
//        String dingCorpId = mappingResult.getData().get(0).getDingCorpId();
//        //获取对应的agentid
//        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(dingCorpId, ConfigCenter.APP_CRM_ID, null);
//        if(ObjectUtils.isEmpty(appAuthResult.getData())){
//            log.info("enterprise not support send todo :{}",dingCorpId);
//            return result;
//        }
//        Map<String, Object> argMap = Maps.newHashMap();
//        argMap.put("objectApiName", objectApiName);
//        argMap.put("objectId", objectId);
//        argMap.put("instanceId", ObjectUtils.isEmpty(instanceId)?"100":instanceId);
//        argMap.put("taskId", taskId);
//        argMap.put("markdown", markdown);
//        Long agentId = appAuthResult.getData().get(0).getAgentId();
//        String empIds= Joiner.on(",").join(dingEmpIds);
//        argMap.put("userIdList", empIds);
//        Result<String> messageResult = dingManager.sendCardMessage(agentId, dingCorpId, argMap, ConfigCenter.MESSAGE_CARD_ID,true,ConfigCenter.CRM_SUITE_ID);
//
//        return result;

//        SendCardMessageArg arg = new SendCardMessageArg();
//        arg.setAgentId(agentId);
//        arg.setDingCorpId(dingCorpId);
//        arg.setDingMessageArg(argMap);
//        arg.setTemplateId(ConfigCenter.MESSAGE_CARD_ID);
//        arg.setIsCardMessage(true);
//        arg.setSuiteId(ConfigCenter.CRM_SUITE_ID);
//        dingManager.sendCardMessage(arg);
//        return result;

        UpdateTodoResult result = (UpdateTodoResult)dingTalkSendUpdateTodoMsgHandlerTemplate.execute(updateTodoArg).getData();
        return result;
    }

    @Override
    public DealTodoResult dealTodo(DealTodoArg dealTodoArg) {

//        DealTodoResult result = new DealTodoResult();
//        result.setCode(200);
//        result.setMessage("发送成功");
//
//        //代办类型
//        if(!isCrmBizType(dealTodoArg.getBizType())){
//            log.info("CloudExternalTodoServiceImpl.dealTodo,todo not support bizType.dealTodoArg={}.", dealTodoArg);
//            return result;
//        }
//
//        //判断灰度企业
//        if(!ConfigCenter.OA_GRAY_TENANTS.contains(dealTodoArg.getEa())) {
//            return result;
//        } else {
//            if(ConfigCenter.WORK_ORDER_BIZ_TYPES.contains(dealTodoArg.getBizType())) {
//                if(!ConfigCenter.OA_WORK_ORDER_TENANTS.contains(dealTodoArg.getEa())) {
//                    return result;
//                }
//            }
//        }
//
//        //查询企业绑定关系
//        Result<DingEnterpriseResult> mappingEnterprise = objectMappingService.queryEnterpriseByEi(dealTodoArg.getEi());
//        if (Objects.isNull(mappingEnterprise) || Objects.isNull(mappingEnterprise.getData())) {
//            log.warn("CloudExternalTodoServiceImpl.dealTodo,ea not bind.dealTodoArg={}.", dealTodoArg);
//            return result;
//        }
//        Result<List<DingCorpMappingVo>> mappingResult = corpMappingService.queryByEi(dealTodoArg.getEi());
//        String dingCorpId = mappingResult.getData().get(0).getDingCorpId();
//        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(dingCorpId, ConfigCenter.APP_CRM_ID, null);
//        // 需要判断是否购买了纷享钉钉CRM。其他应用的不发送
//        if (ObjectUtils.isEmpty(appAuthResult.getData())) {
//            log.info("CloudExternalTodoServiceImpl.dealTodo,enterprise not support send todo :{}", dingCorpId);
//            return result;
//        }
//
//        //获取token
//        String accessToken = dingManager.getAccessToken(dingCorpId, ConfigCenter.CRM_SUITE_ID).getData();
//        Map<String, String> dingTalkToken = new HashMap<>();
//        dingTalkToken.put("x-acs-dingtalk-access-token", accessToken);
//
//        List<DingTaskVo> dingTaskVos = objectMappingService.getDingTaskVo(dealTodoArg.getEi(), dealTodoArg.getSourceId()).getData();
//        if (CollectionUtils.isEmpty(dingTaskVos)) {
//            log.info("CloudExternalTodoServiceImpl.dealTodo,dingTalkToken is null,dealTodoArg={}.", dealTodoArg);
//            return result;
//        }
//
//        //标志
//        Boolean flag = true;
//
//        //获取人员详细信息
//        Result<List<DingMappingEmployeeResult>> employeeInfosResult = objectMappingService.batchQueryMapping(dealTodoArg.getEi(), dealTodoArg.getOperators());
//        if(CollectionUtils.isEmpty(employeeInfosResult.getData())) {
//            log.info("CloudExternalTodoServiceImpl.dealTodo,user not bind,dealTodoArg={}.", dealTodoArg);
//            return result;
//        }
//        List<DingMappingEmployeeResult> employeeInfos = employeeInfosResult.getData();
//        String operatorUnionId = employeeInfos.get(0).getDingUnionId();
//        Map<String, Object> dingTalk = Maps.newHashMap();
//        List<Map<String, Object>> executorStatusList = new LinkedList<>();
//        List<Integer> userIds = new LinkedList<>();
//        for (int i = 0; i < employeeInfos.size(); i++) {
//            Map<String, Object> executorStatus = Maps.newHashMap();
//            executorStatus.put("id", employeeInfos.get(i).getDingUnionId());
//            executorStatus.put("isDone", flag);
//            executorStatusList.add(executorStatus);
//            userIds.add(employeeInfos.get(i).getEmployeeId());
//        }
//
//        dingTalk.put("executorStatusList", executorStatusList);
//        //更换处理接口
//        Gson gson = new Gson();
//        String dealTalkUrl = DINGTALK_TODO + operatorUnionId + "/tasks/" + dingTaskVos.get(0).getTaskId() + "/executorStatus?operatorId=" + operatorUnionId;
//        HttpResponseMessage messageResult = OkHttp3MonitorUtils.sendOkHttp3Put(dealTalkUrl, dingTalkToken, gson.toJson(dingTalk));
//        log.info("CloudExternalTodoServiceImpl.dealTodo,dealTalkUrl={},dingTalk={},messageResult={}", dealTalkUrl, dingTalk, messageResult);
//        JSONObject jsonObject = JSONObject.parseObject(messageResult.getContent());
//        if (messageResult.getStatusCode() == 200) {
//            String dealResult = String.valueOf(jsonObject.get("result"));
//            if ("true".equals(dealResult)) {
//                //状态设为0
//                int sum = 0;
//                for (DingTaskVo dingTaskVo : dingTaskVos) {
//                    if (userIds.contains(dingTaskVo.getEmployeeId())) {
//                        Result<Integer> countResult = objectMappingService.updateStatus(0, dealTodoArg.getEi(), dealTodoArg.getSourceId(), dingTaskVo.getEmployeeId());
//                        //更换接口后，就算全部人完成了代办，仍然显示为进行中的代办，需要查询数据库，如果状态全部为0的话，证明代办完成了，调用完成接口
//                        if(countResult.isSuccess()) {
//                            sum += countResult.getData();
//                        }
//                    }
//                }
//                List<DingTaskVo> dingTaskVos1 = objectMappingService.getDingTaskVo(dealTodoArg.getEi(), dealTodoArg.getSourceId()).getData();
//                if (CollectionUtils.isEmpty(dingTaskVos1)) {
//                    //调用完成的接口
//                    this.updateTodoStatus(operatorUnionId, dingTaskVos.get(0).getTaskId(), dingTalkToken);
//                }
//                log.info("CloudExternalTodoServiceImpl.dealTodo,ea={},userIds={},sum={}", dealTodoArg.getEa(), userIds, sum);
//            }
//        }
//        return result;
        DealTodoResult result = (DealTodoResult) dingTalkSendDealTodoMsgHandlerTemplate.execute(dealTodoArg).getData();
        return result;
    }
//    public void updateTodoStatus(String operator, String taskId, Map<String, String> dingTalkToken) {
//        Gson gson = new Gson();
//        String updateToDo = DINGTALK_TODO + operator + "/tasks/" + taskId + "?operatorId=" + operator;
//        Map<String, Object> dingTalk = Maps.newHashMap();
//        boolean flag = true;
//        dingTalk.put("done", flag);
//        HttpResponseMessage updateToDoMessageResult = OkHttp3MonitorUtils.sendOkHttp3Put(updateToDo, dingTalkToken, gson.toJson(dingTalk));
//        JSONObject jsonObject = JSONObject.parseObject(updateToDoMessageResult.getContent());
//        if (updateToDoMessageResult.getStatusCode() == 200) {
//            String dealResult = String.valueOf(jsonObject.get("result"));
//            if("true".equals(dealResult)) {
//                log.info("CloudExternalTodoServiceImpl.dealTodo,updateTodoStatus,operator={},taskId={}", operator, taskId);
//            }
//        }
//    }


    @Override
    public synchronized DeleteTodoResult deleteTodo(DeleteTodoArg deleteTodoArg) {
//        DeleteTodoResult result = new DeleteTodoResult();
//        result.setCode(200);
//        result.setMessage("发送成功");
//
//        //代办类型
//        if(!isCrmBizType(deleteTodoArg.getBizType())){
//            log.info("CloudExternalTodoServiceImpl.dealTodo,todo not support bizType.deleteTodoArg={}.", deleteTodoArg);
//            return result;
//        }
//
//        //判断灰度企业
//        if(!ConfigCenter.OA_GRAY_TENANTS.contains(deleteTodoArg.getEa())) {
//            return result;
//        } else {
//            if(ConfigCenter.WORK_ORDER_BIZ_TYPES.contains(deleteTodoArg.getBizType())) {
//                if(!ConfigCenter.OA_WORK_ORDER_TENANTS.contains(deleteTodoArg.getEa())) {
//                    return result;
//                }
//            }
//        }
//
//        //查询企业绑定关系
//        Result<List<DingCorpMappingVo>> mappingResult = corpMappingService.queryByEi(deleteTodoArg.getEi());
//        if (Objects.isNull(mappingResult) || Objects.isNull(mappingResult.getData())) {
//            log.warn("CloudExternalTodoServiceImpl.dealTodo,ea not bind.deleteTodoArg={}.", deleteTodoArg);
//            return result;
//        }
//        String dingCorpId = mappingResult.getData().get(0).getDingCorpId();
//        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(dingCorpId, ConfigCenter.APP_CRM_ID, null);
//        // 需要判断是否购买了纷享钉钉CRM。其他应用的不发送
//        if(ObjectUtils.isEmpty(appAuthResult.getData())){
//            log.info("CloudExternalTodoServiceImpl.dealTodo,enterprise not support send todo :{}",dingCorpId);
//            return result;
//        }
//
//        //获取token
//        String accessToken = dingManager.getAccessToken(dingCorpId, ConfigCenter.CRM_SUITE_ID).getData();
//        Map<String, String> dingTalkToken = new HashMap<>();
//        dingTalkToken.put("x-acs-dingtalk-access-token", accessToken);
//
//        Result<List<DingTaskVo>> dingTaskVo = objectMappingService.getDingTaskVo(deleteTodoArg.getEi(), deleteTodoArg.getSourceId());
//        if(CollectionUtils.isEmpty(dingTaskVo.getData())) {
//            log.info("CloudExternalTodoServiceImpl.dealTodo,dingTalkToken is null,deleteTodoArg={}.", deleteTodoArg);
//            return result;
//        }
//
//        //获取人员详细信息
//        Result<List<DingMappingEmployeeResult>> employeeInfosResult = objectMappingService.batchQueryMapping(deleteTodoArg.getEi(), deleteTodoArg.getDeleteEmployeeIds());
//        if(CollectionUtils.isEmpty(employeeInfosResult.getData())) {
//            log.info("CloudExternalTodoServiceImpl.dealTodo,user not bind,deleteTodoArg={}.", deleteTodoArg);
//            return result;
//        }
//        List<DingMappingEmployeeResult> employeeInfos = employeeInfosResult.getData();
//        int operatorId = employeeInfos.get(0).getEmployeeId();
//        String operatorUnionId = employeeInfos.get(0).getDingUnionId();
//        List<String> userUnionIds = new LinkedList<>();
//        List<Integer> userIds = new LinkedList<>();
//        for (int i = 0; i < employeeInfos.size(); i++) {
//            userUnionIds.add(employeeInfos.get(i).getDingUnionId());
//            userIds.add(employeeInfos.get(i).getEmployeeId());
//        }
//
//        //查看代办详情
//        String dealTalkUrl = DINGTALK_TODO + operatorUnionId + "/tasks/sources/" + deleteTodoArg.getSourceId();
//        Gson gson = new Gson();
//        HttpResponseMessage messageResult = OkHttp3MonitorUtils.sendOkHttp3Get(dealTalkUrl, dingTalkToken, new HashMap<>());
//        log.info("CloudExternalTodoServiceImpl.deleteTodo,dealTalkUrl={},messageResult={}.", dealTalkUrl, messageResult);
//        JSONObject jsonObject = JSONObject.parseObject(messageResult.getContent());
//        if (messageResult.getStatusCode() == 200) {
//            String talkId = String.valueOf(jsonObject.get("id"));
//            String executorIds = JSONArray.toJSONString(jsonObject.get("executorIds"));
//
//            // 将json字符串转换为list集合
//            List<String> cardVos = new LinkedList<>(JSONArray.parseArray(executorIds, String.class));
//            for (int i = 0; i < cardVos.size(); i++) {
//                cardVos.removeAll(userUnionIds);
//            }
//            String participantIds = JSONArray.toJSONString(jsonObject.get("participantIds"));
//            // 将json字符串转换为list集合
//            List<String> cardVos1 = new LinkedList<>(JSONArray.parseArray(participantIds, String.class));
//            for (int i = 0; i < cardVos1.size(); i++) {
//                cardVos1.removeAll(userUnionIds);
//            }
//            Boolean flag = false;
//            Map<String, Object> dingTalk = Maps.newHashMap();
//            dingTalk.put("done", flag);
//            dingTalk.put("executorIds",cardVos);
//            dingTalk.put("participantIds",cardVos1);
//            String updateToDo = DINGTALK_TODO + operatorUnionId + "/tasks/" + talkId + "?operatorId=" + operatorUnionId;
//            HttpResponseMessage updateToDoMessageResult = OkHttp3MonitorUtils.sendOkHttp3Put(updateToDo, dingTalkToken, gson.toJson(dingTalk));
//            log.info("CloudExternalTodoServiceImpl.deleteTodo,ea={},dingTalk={},updateToDo={},updateToDoMessageResult={}.", deleteTodoArg.getEa(), dingTalk, updateToDo, updateToDoMessageResult);
//            if (updateToDoMessageResult.getStatusCode() == 200) {
//                Result<Integer> countResult = objectMappingService.updateExecutor(userIds, deleteTodoArg.getEi(), deleteTodoArg.getSourceId());
//                log.warn("CloudExternalTodoServiceImpl.deleteTodo,更新代办成功，deleteEmployeeId={}，count={}，更新代办成功.", operatorId, countResult.isSuccess() ? countResult.getData() : 0);
//                //增加逻辑，客户删除了代办任务，把状态设置为完成
//                Result<List<DingTaskVo>> dingTaskVo1 = objectMappingService.getDingTaskVo(deleteTodoArg.getEi(), deleteTodoArg.getSourceId());
//                if(CollectionUtils.isEmpty(dingTaskVo1.getData())) {
//                    //调用完成的接口
//                    this.updateTodoStatus(operatorUnionId, talkId, dingTalkToken);
//                }
//            } else {
//                log.warn("CloudExternalTodoServiceImpl.deleteTodo: 更新代办失败，messageArg={}，userId={}.", dingTalk, userIds);
//            }
//        }
//        return result;

        DeleteTodoResult result = (DeleteTodoResult) dingTalkSendDeleteTodoMsgHandlerTemplate.execute(deleteTodoArg).getData();
        return result;
    }

//    private Boolean isCrmBizType(String bizType){
//
//        return ConfigCenter.CRM_TO_BIZ_TYPES.contains(bizType) || ConfigCenter.WORK_ORDER_BIZ_TYPES.contains(bizType);
//    }
}
