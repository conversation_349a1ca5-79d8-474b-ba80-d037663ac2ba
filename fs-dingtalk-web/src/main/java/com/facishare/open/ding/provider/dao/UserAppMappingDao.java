package com.facishare.open.ding.provider.dao;

import com.facishare.open.ding.api.vo.UserMappingVo;
import com.facishare.open.ding.provider.arg.UserMappingEntity;
import com.facishare.open.ding.api.result.UserAppResult;
import com.facishare.open.ding.common.result.Result;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/10 10:51
 * @Version 1.0
 */
public interface UserAppMappingDao extends ICrudMapper<UserMappingEntity> {

    @Select("select * from user_app_mapping where ding_corp_id=#{dingCorpId} and ding_emp_id=#{dingEmpId}")
    List<UserAppResult> queryByDingEmpId(@Param("dingCorpId") String dingCorpId, @Param("dingEmpId") String dingEmpId);

    @Select("<script>" +
            "select * from user_app_mapping where ding_corp_id=#{dingCorpId}" +
            "<if test='appId!=null'>" +
            "and app_id=#{appId}" +
            "</if>" +
            "<if test='dingEmp!=null'>" +
            "and ding_emp_id=#{dingEmp}" +
            "</if>" +
            "</script>")
    List<UserAppResult> conditionQueryMapping (@Param("dingCorpId") String dingCorpId,@Param("appId") Long appId,@Param("dingEmp") String dingEmpId);

    @Select("<script>" +
            "select * from user_app_mapping where crm_emp_id=#{crmEmpId}"+
            "</script>")
    List<UserAppResult> conditionQueryMappingByEmpId (@Param("crmEmpId") Integer crmEmpId);


    @Select("<script>" +
            "update user_app_mapping set crm_emp_id=#{crmEmpId} where ding_emp_id=#{dingEmpId} and ei=#{ei}"+
            "</script>")
    Integer updateEmpByEmpId (@Param("crmEmpId") String crmEmpId,@Param("dingEmpId") String dingEmpId,@Param("ei") Integer ei);

    @Delete("DELETE FROM user_app_mapping WHERE ding_corp_id=#{dingCorpId} and ding_emp_id=#{dingEmp} and app_id=#{appId}")
    Integer deleteByAppId(@Param("dingCorpId") String dingCorpId,@Param("dingEmp") String dingEmpId,@Param("appId")Long appId);

    @Insert("insert ignore into user_app_mapping (ei,ding_corp_id,crm_emp_id,ding_emp_id,app_id)" +
            "values ( #{item.ei},#{item.dingCorpId},#{item.crmEmpId},#{item.dingEmpId},#{appId})")
    Integer insertAppInfo(@Param("item")UserMappingVo mappingVo);

}
