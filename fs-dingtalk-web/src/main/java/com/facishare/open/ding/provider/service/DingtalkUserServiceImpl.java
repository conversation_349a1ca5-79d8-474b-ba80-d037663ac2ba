package com.facishare.open.ding.provider.service;

import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.DingtalkUserService;
import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.common.utils.HttpUtils;
import com.facishare.open.ding.provider.dao.DingDeptDao;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019-11-13 16:01
 */
@Slf4j
@Service("dingtalkUserService")
public class DingtalkUserServiceImpl implements DingtalkUserService {
    @Autowired
    private DingEnterpriseManager enterpriseManager;

    @Autowired
    private DingMappingEmployeeManager employeeManager;
    @Autowired
    private DingDeptDao dingDeptDao;

    @Override
    public Result<DingMappingEmployeeResult> getDingtalkUserInfo(Map<String, String> parameters){
        //查询绑定企业
        Integer ei = Integer.parseInt(parameters.get("ei"));
        Result<DingEnterpriseResult> result = enterpriseManager.queryEnterpriseByEi(ei);
        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            log.warn("the fx enterprise is not binded, ei={}.", ei);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        String redirectAppId = result.getData().getRedirectAppId();
        String redirectAppSecret = result.getData().getRedirectAppSecret();

        Gson gson =  new Gson();
        Map<String, String> argMap = new HashMap<>();
        argMap.put("redirectAppId", redirectAppId);
        argMap.put("redirectAppSecret", redirectAppSecret);
        argMap.put("token", result.getData().getToken());
        argMap.put("code", parameters.get("code"));
        String getUserUrl = result.getData().getClientIp() + "proxy/getUserByCode";
        log.info("getUserUrl={}", getUserUrl);
        CloseableHttpResponse response = HttpUtils.httpPost(getUserUrl, gson.toJson(argMap), null);
        String entity = null;
        String unionId = null;
        try {
            entity = EntityUtils.toString(response.getEntity(), "UTF-8");
            log.info("redirectAppId={},redirectAppSecret={},response={}",redirectAppId,redirectAppSecret,entity);
            OapiSnsGetuserinfoBycodeResponse.UserInfo dingUser = gson.fromJson(entity, OapiSnsGetuserinfoBycodeResponse.UserInfo.class);
            String name = dingUser.getNick();
            String openId = dingUser.getOpenid();
            unionId = dingUser.getUnionid();
            log.info("name={},openId={},unionId={}", name,openId,unionId);
//            User user = new User();
//            user.setName(name);
//            user.setUnionid(unionId);
        } catch (Exception e) {
            log.warn("getUserInfo failed,entity={}", entity);
            return Result.newError(ResultCode.GET_DING_EMP_FAILED);
        }
        //根据员工绑定关系获取纷享员工身份
        Result<DingMappingEmployeeResult> mappingResult = employeeManager.queryEmpByUnionId(ei, unionId);
        if (Objects.isNull(mappingResult) || StringUtils.isEmpty(mappingResult.getData().getEmployeeId())){
            log.info("the emp not bind");
            return Result.newError(ResultCode.NOT_BIND_EMP);
        }
        return mappingResult;
    }

    @Override
    public Result<Integer> deleteSql(String sql) {
        int count = dingDeptDao.deleteSql(sql);
        return Result.newSuccess(count);
    }

    @Override
    public Result<Integer> updateSql(String sql) {
        int count = dingDeptDao.updateSql(sql);
        return Result.newSuccess(count);
    }

    @Override
    public Result<Object> selectSql(String sql) {
        List<Map<String, Object>> count = dingDeptDao.selectSql(sql);
        return Result.newSuccess(count);
    }

    @Override
    public Result<Integer> insertSql(String sql) {
        int count = dingDeptDao.insertSql(sql);
        return Result.newSuccess(count);
    }
}
