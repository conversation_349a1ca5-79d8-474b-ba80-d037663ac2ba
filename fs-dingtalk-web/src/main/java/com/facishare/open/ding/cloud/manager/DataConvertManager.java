package com.facishare.open.ding.cloud.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.constants.ObjType;
import com.facishare.open.ding.api.enums.DingObjTypeEnum;
import com.facishare.open.ding.api.model.DingObjData;
import com.facishare.open.ding.api.model.StandardData;
import com.facishare.open.ding.cloud.utils.PhoneFormatCheckUtils;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Maps;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import groovy.json.StringEscapeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2022/1/18 21:21 字段转换
 * @Version 1.0
 */
@Service
@Slf4j
public class DataConvertManager {


    /**
     * dingcorpId后期可拓展支持自定义字段等，目前先不用
     *
     * @param objData
     * @param dingCorpId
     * @return
     */
    public StandardData convertData(DingObjData objData, String dingCorpId) {

        StandardData standardData = convertStandardData(objData);
        return standardData;
    }

    //根据字段描述与字段类型做对应的数据转换
    public static StandardData convertStandardData(DingObjData objData) {
        StandardData standardData = new StandardData();
        ObjectData masterFieldVal = new ObjectData();
        DingObjTypeEnum enumByDingApiName = DingObjTypeEnum.getEnumByDingApiName(objData.getObjectType());
        if (enumByDingApiName==null){
            return null;
        }
        standardData.setObjAPIName(enumByDingApiName.getCrmObjApiName());
        //获取字段
        List<Map<String, Object>> objFieldType = enumByDingApiName.getObjFieldType();
        // 字段类型
        for (int i = 0; i < objFieldType.size(); i++) {
            String fieldApiName = objFieldType.get(i).get("name").toString();
            String type = objFieldType.get(i).get("type").toString();
            Object objValue = objData.getData().get(fieldApiName);
            if(ObjectUtils.isEmpty(objValue))continue;
            String value = objData.getData().get(fieldApiName).toString();
            DocumentContext documentContext =
                    JsonPath.using(Configuration.defaultConfiguration().addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL,
                            Option.SUPPRESS_EXCEPTIONS)).parse(value);
            switch (type) {
                case ObjType
                        .TEXT:
                    masterFieldVal.put(fieldApiName, value);
                break;
                case ObjType.ADDRESS:
                        Object address = documentContext.read( "$.value");
                    Object replaceValue = Optional.ofNullable(address).orElseGet(() -> StringUtils.EMPTY);
                    String addressValue = replaceValue.toString().replaceAll(",", "");
                    masterFieldVal.put(fieldApiName, addressValue);
                    break;
                case ObjType.PHONE:
                    String phone = documentContext.read("$.value");
                    masterFieldVal.put(fieldApiName, phone);
                    break;
                case ObjType.LOOK_UP:
                    String referencedValue = documentContext.read("$.extendValue");
                    if(StringUtils.isEmpty(referencedValue))break;
                    //转义
                    String transferValueLookUp= StringEscapeUtils.unescapeJava(referencedValue);
                    String upValue= JsonPath.read(transferValueLookUp, "$.list[0].instanceId");
//                    List<String> lookupList = Optional.ofNullable(referenceIds).orElseGet(() -> Collections.emptyList());
//                    String upValue = lookupList.get(0);
                    masterFieldVal.put(fieldApiName, upValue);
                    break;
                case ObjType.SELECT:
                    
                    String selectValue = documentContext.read("$.extendValue");
                    if(ObjectUtils.isEmpty(selectValue))break;
                    String transferValue = StringEscapeUtils.unescapeJava(selectValue);
                    String objSelectValue = JsonPath.read(transferValue, "$.key");
                    masterFieldVal.put(fieldApiName, objSelectValue);
                    break;
                default:
                    masterFieldVal.put(fieldApiName, value);
                    break;


            }
        }
        masterFieldVal.put("creator_userid",objData.getCreatorUserId());
        masterFieldVal.put("_id",objData.getInstanceId());
        standardData.setMasterFieldVal(masterFieldVal);
        return standardData;
    }

    //CRM到ERP数据转换
    public Map<String,Object> convertData2Ding(Map<String,Object> data,String objType) {
        Map<String,Object> dataMap=Maps.newHashMap();
        DingObjTypeEnum enumByDingApiName = DingObjTypeEnum.getEnumByDingApiName(objType);
        // 字段类型
        List<Map<String, Object>> objFieldType = enumByDingApiName.getObjFieldType();
        for (int i = 0; i < objFieldType.size(); i++) {
            String fieldApiName = objFieldType.get(i).get("name").toString();
            String type = objFieldType.get(i).get("type").toString();
            Object objValue = data.get(fieldApiName);
            if (ObjectUtils.isEmpty(objValue)) continue;
            String value = objValue.toString();
            switch (type) {
                case ObjType
                        .TEXT:
                    dataMap.put(fieldApiName, value);
                    break;
                case ObjType.ADDRESS:
                    Map<String,String> detailMap=Maps.newHashMap();
                    detailMap.put("name",value);
                    Map<String,Object> addressMap=Maps.newHashMap();
                    addressMap.put("detail", detailMap);
                    String detail = JSONObject.toJSONString(addressMap);
                    Map<String,String> addressDataMap=Maps.newHashMap();
                    addressDataMap.put("extendValue",detail);
                    addressDataMap.put("value",value);
                    dataMap.put(fieldApiName, JSONObject.toJSONString(addressDataMap));
                    break;
                case ObjType.PHONE:
                    Map<String,String> phoneDataMap=Maps.newHashMap();
                    phoneDataMap.put("extendValue","");
                    phoneDataMap.put("value",value);
                    dataMap.put(fieldApiName,JSONObject.toJSONString(phoneDataMap));
                    break;
                case ObjType.LOOK_UP:
                   //暂时不支持
                    break;
                case ObjType.SELECT:
                    //暂时不支持
                    break;
                default:
//                    masterFieldVal.put(fieldApiName, value);
                    break;

            }
        }
        dataMap.put("creator_userid",data.get("creator_userid"));
        return dataMap;
    }

  private String convertExtendData(String phone){
      boolean phoneLegal = PhoneFormatCheckUtils.isPhoneLegal(phone);
      if(phoneLegal){
          //返回手机号码的格式
          return "{\"mode\":\"phone\",\"countryKey\":\"CN\",\"flag\":\"C\",\"countryCode\":\"+86\",\"areaNumber\":\"\",\"flagPy\":\"Z\",\"countryName\":\"China\",\"countryNameZh\":\"中国\",\"countryNamePy\":\"ZHONGGUO\"}";
      } else{
          //返回电话号码的格式
          return "";
      }
  }









}
