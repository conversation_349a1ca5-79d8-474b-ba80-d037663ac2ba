package com.facishare.open.ding.provider.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.config.ConfigCenter;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.dingding.DingUrl;
import com.facishare.open.ding.api.arg.DingMessageArg;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.facishare.open.ding.api.utils.HttpRequestUtils;
import com.facishare.open.webhook.messagesend.service.ExternalOaTodoService;
import com.facishare.restful.common.StopWatch;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.UpdateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.result.CreateTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DealTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DeleteTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.UpdateTodoResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <p>接入webhook消息推送</p> 代办
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-10-15 20:06
 */
@Slf4j
@Service("externalOaTodoServiceImpl")
public class ExternalOaTodoServiceImpl implements ExternalOaTodoService {
    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;

    @Autowired
    private DingMappingEmployeeManager employeeManager;

    @ReloadableProperty("mid.authorize.url")
    private String MID_URL;// = "https://www.fxiaoke.com/dingtalk/business/authorize?direct_uri=";

    //客户端
    private static final String DING_SINGLE_URL = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?response_type=code&scope=snsapi_auth&state=STATE";

    //网页版
    private static final String DING_SINGLE_URL_WEB = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?response_type=code&scope=snsapi_login&state=STATE";

    private static final String MESSAGE_TYPE = "action_card";

    private static final String TOKEN_INVALID_CODE = "88";

    @ReloadableProperty("sso.redirect.url.source")
    private String ssoRedirectUrlSource;

    private LoadingCache<String, String> cache = Caffeine.newBuilder().maximumSize(10000).expireAfterWrite(100, TimeUnit.MINUTES).refreshAfterWrite(90, TimeUnit.MINUTES).build(new CacheLoader<String, String>() {
        @Nullable
        @Override
        public String load(@NonNull String key) throws Exception {
            Result<DingEnterpriseResult> dingEnterpriseResultResult = dingEnterpriseManager.queryEnterpriseByEi(Integer.valueOf(key));
            DingEnterpriseResult result = dingEnterpriseResultResult.getData();
            return DingRequestUtil.getToken(result.getClientIp(), result.getAppKey(), result.getAppSecret());
        }
    });

    @Override
    public CreateTodoResult createTodo(CreateTodoArg arg) {
        StopWatch stopWatch = StopWatch.create("trace createTodo:" + arg.getEa());
        log.info("ExternalOaToDO messageArg:{}", arg);
        CreateTodoResult result = new CreateTodoResult();
        result.setCode(200);
        //判断灰度的企业
        if(ConfigCenter.OA_GRAY_TENANTS.contains(arg.getEa())){
            //TODO 走推送代办的逻辑

            return result;
        }


        if (Objects.isNull(arg)) {
            log.warn("sendMessage param is null");
            result.setMessage("sendMessage param is null");
            return result;
        }
        Integer ei = arg.getEi();
        Result<DingEnterpriseResult> enterpriseResult = dingEnterpriseManager.queryEnterpriseByEi(ei);
        stopWatch.lap("getEnterprise");
        if (Objects.isNull(enterpriseResult) || Objects.isNull(enterpriseResult.getData())) {
            log.warn("the fx enterprise is not binded, ei={}.", ei);
            result.setMessage("the fx enterprise is not binded");
            return result;
        }
        List<Integer> empUsers = arg.getReceiverIds();
        if (CollectionUtils.isEmpty(empUsers)) {
            log.warn("toUser is empty, arg={}.", arg);
            result.setMessage("stoUser is empty");
            return result;
        }
        StringBuilder strBuilder = new StringBuilder();
        for (Integer fxId : empUsers) {
            DingMappingEmployeeResult mappingEmployeeResult = employeeManager.queryMappingEmployeeByEi(ei, fxId);
            if (Objects.isNull(mappingEmployeeResult)) {
                log.info("emp not bind,ei={},fxId={}", ei, fxId);
                continue;
            }
            strBuilder.append(mappingEmployeeResult.getDingEmployeeId()).append(",");
        }
        if (strBuilder.length() <= 0) {
            log.info("no user need to send message");
            result.setMessage("no user need to send message");
            return result;
        }
        stopWatch.lap("getUser");
        strBuilder.deleteCharAt(strBuilder.length() - 1);
        Gson gson = new Gson();
        String clientUrl = DingRequestUtil.appendUrl(enterpriseResult.getData().getClientIp());
        Map<String, Object> messageArg = new HashMap<>();
        String accessToken = cache.get(String.valueOf(arg.getEi()));
        if (StringUtils.isEmpty(accessToken)) {
            log.warn("createTodo not accessToken  ea:{}", arg.getEa());
            return result;
        }
        stopWatch.lap("getToken");
        String proxyMessageUrl = DingUrl.CORP_MESSAGE.concat("?access_token=").concat(accessToken);
        messageArg.put("url", proxyMessageUrl);//钉钉发送消息的url
        messageArg.put("type", "POST");
        messageArg.put("token", enterpriseResult.getData().getToken());

        DingMessageArg dingMessageArg = new DingMessageArg();
        dingMessageArg.setAgent_id(enterpriseResult.getData().getAgentId());
        dingMessageArg.setUserid_list(strBuilder.toString());
        dingMessageArg.getMsg().setMsgtype(MESSAGE_TYPE);
        //优化卡片消息
        StringBuilder markdown = new StringBuilder();
        if (CollectionUtils.isNotEmpty(arg.getForm())) {
            for (int i = 0; i < arg.getForm().size(); i++) {
                if (i == 0) {
                    markdown.append("# ").append(arg.getForm().get(i).getKey()).append(": ").append(arg.getForm().get(i).getValue()).append("\n");
                } else {
                    markdown.append("## ").append(arg.getForm().get(i).getKey()).append(": ").append(arg.getForm().get(i).getValue()).append("\n");
                }

            }
        }
        dingMessageArg.getMsg().getAction_card().setMarkdown(!StringUtils.isEmpty(markdown.toString()) ? markdown.toString() : arg.getContent());
        String time = Long.toString(Calendar.getInstance().getTimeInMillis() / 1000);
        dingMessageArg.getMsg().getAction_card().setTitle(arg.getTitle() + time);
        dingMessageArg.getMsg().getAction_card().setSingle_title("查看详情");

        String objectApiName = arg.getExtraDataMap().get("objectApiName");
        String objectId = arg.getExtraDataMap().get("objectId");
        String instanceId = arg.getExtraDataMap().get("workflowInstanceId");
        String directUri = MID_URL + "?ei=" + arg.getEi() + "&apiname=" + objectApiName
                + "&id=" + objectId + "&instanceId=" + instanceId + "&taskId=" + arg.getGenerateUrlType();
        StringBuilder stringBuilder = new StringBuilder();
        String directAppId = enterpriseResult.getData().getRedirectAppId();
        stringBuilder.append(DING_SINGLE_URL).append("&appid=").append(directAppId).append("&redirect_uri=").append(URLEncoder.encode(directUri));
        log.info("finalUrl = {}", stringBuilder.toString());
        dingMessageArg.getMsg().getAction_card().setSingle_url(stringBuilder.toString());//需要跳转到纷享的url

        messageArg.put("data", dingMessageArg);
        Object messageResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));
        JSONObject jsonObject = JSONObject.parseObject(messageResult.toString());
        stopWatch.lap("sendMessage");
        if (Objects.isNull(messageResult) || ObjectUtils.isEmpty(jsonObject.get("errcode"))) {
            log.warn("向钉钉发送待办消息失败，messageArg={}.", messageArg);
            return result;
        }
        if (!HttpRequestUtils.DING_SUCCESS.equals(jsonObject.get("errcode"))) {
            if (jsonObject.get("errcode").equals(TOKEN_INVALID_CODE)) {
                Object refreshResult = refreshRequest(messageArg, ei, clientUrl);
                log.info("向钉钉发送待办消息重试，messageArg={},messageResult={}.", messageArg, refreshResult);
            }
            log.info("向钉钉发送待办消息重试，messageArg={},messageResult={}.", messageArg, messageResult);
        }
        stopWatch.log();
        result.setMessage("发送成功");
        return result;
    }

    private Object refreshRequest(Map<String, Object> messageArg,Integer ei, String clientUrl) {
        //重新获取token，发送消息
        Gson gson = new Gson();
        cache.invalidate(String.valueOf(ei));
        String refreshToken = cache.get(String.valueOf(ei));
        String refreshUrl = DingUrl.CORP_MESSAGE.concat("?access_token=").concat(refreshToken);
        messageArg.put("url", refreshUrl);//钉钉发送消息的url
        Object refreshResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));
        return refreshResult;
    }


    @Override
    public UpdateTodoResult updateTodo(UpdateTodoArg arg) {
        log.info("UpdateTodoArg arg:{}",arg);
        UpdateTodoResult result = new UpdateTodoResult();
        result.setCode(200);

        if (Objects.isNull(arg)) {
            log.warn("sendMessage param is null");
            result.setMessage("sendMessage param is null");
            return result;
        }
        Integer ei = arg.getEi();
        Result<DingEnterpriseResult> enterpriseResult = dingEnterpriseManager.queryEnterpriseByEi(ei);
        if (Objects.isNull(enterpriseResult) || Objects.isNull(enterpriseResult.getData())) {
            log.warn("the fx enterprise is not binded, ei={}.", ei);
            result.setMessage("the fx enterprise is not binded");
            return result;
        }
        List<Integer> empUsers = arg.getReceiverIds();
        if (CollectionUtils.isEmpty(empUsers)) {
            log.warn("toUser is empty, arg={}.", arg);
            result.setMessage("toUser is empty");
            return result;
        }
        StringBuilder strBuilder = new StringBuilder();
        for (Integer fxId : empUsers) {
            DingMappingEmployeeResult mappingEmployeeResult = employeeManager.queryMappingEmployeeByEi(ei, fxId);
            if (Objects.isNull(mappingEmployeeResult)) {
                log.info("emp not bind,ei={},fxId={}", ei, fxId);
                continue;
            }
            strBuilder.append(mappingEmployeeResult.getDingEmployeeId()).append(",");
        }
        if (strBuilder.length() <= 0) {
            log.info("no user need to send message");
            result.setMessage("no user need to send message");
            return result;
        }
        strBuilder.deleteCharAt(strBuilder.length() - 1);
        Gson gson = new Gson();
        String clientUrl = DingRequestUtil.appendUrl(enterpriseResult.getData().getClientIp());
        Map<String, Object> messageArg = new HashMap<>();
        String accessToken = DingRequestUtil.getToken(enterpriseResult.getData().getClientIp(), enterpriseResult.getData().getAppKey(), enterpriseResult.getData().getAppSecret());
        String proxyMessageUrl = DingUrl.CORP_MESSAGE.concat("?access_token=").concat(accessToken);
        messageArg.put("url", proxyMessageUrl);//钉钉发送消息的url
        messageArg.put("type", "POST");
        messageArg.put("token", enterpriseResult.getData().getToken());

        DingMessageArg dingMessageArg = new DingMessageArg();
        dingMessageArg.setAgent_id(enterpriseResult.getData().getAgentId());
        dingMessageArg.setUserid_list(strBuilder.toString());
        dingMessageArg.getMsg().setMsgtype(MESSAGE_TYPE);
        String title = !StringUtils.isEmpty(arg.getTitle()) ? arg.getTitle() : "";
        String content = !StringUtils.isEmpty(arg.getContent()) ? arg.getContent() : "";
        dingMessageArg.getMsg().getAction_card().setMarkdown("# " + title + "\n## " + content);
        String time = Long.toString(Calendar.getInstance().getTimeInMillis() / 1000);
        dingMessageArg.getMsg().getAction_card().setTitle(title + time);
        dingMessageArg.getMsg().getAction_card().setSingle_title("查看详情");

        String objectApiName = arg.getExtraDataMap().get("objectApiName");
        String objectId = arg.getExtraDataMap().get("objectId");
        String instanceId = arg.getExtraDataMap().get("workflowInstanceId");
        String directUri = MID_URL + "?ei=" + arg.getEi() + "&apiname=" + objectApiName
                + "&id=" + objectId + "&instanceId=" + instanceId + "&taskId=" + arg.getGenerateUrlType();
        StringBuilder stringBuilder = new StringBuilder();
        String directAppId = enterpriseResult.getData().getRedirectAppId();
        stringBuilder.append(DING_SINGLE_URL).append("&appid=").append(directAppId).append("&redirect_uri=").append(URLEncoder.encode(directUri));
        log.info("finalUrl = {}", stringBuilder.toString());
        dingMessageArg.getMsg().getAction_card().setSingle_url(stringBuilder.toString());//需要跳转到纷享的url

        messageArg.put("data", dingMessageArg);
        Object messageResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));
        JSONObject jsonObject = JSONObject.parseObject(messageResult.toString());

        if (Objects.isNull(messageResult) || ObjectUtils.isEmpty(jsonObject.get("errcode"))) {
            log.warn("向钉钉发送待办消息失败，messageArg={}.", messageArg);
            return result;
        }

        if (!HttpRequestUtils.DING_SUCCESS.equals(jsonObject.get("errcode"))) {
            if (jsonObject.get("errcode").equals(TOKEN_INVALID_CODE)) {
                Object refreshResult = refreshRequest(messageArg, ei, clientUrl);
                log.info("向钉钉发送待办消息重试，messageArg={},messageResult={}.", messageArg, refreshResult);
            }
            log.info("向钉钉发送待办消息重试，messageArg={},messageResult={}.", messageArg, messageResult);

        }
        result.setMessage("发送成功");
        return result;
    }

    @Override
    public DealTodoResult dealTodo(DealTodoArg dealTodoArg) {
        log.info("DealTodoArg arg:{}",dealTodoArg);
        //TODO 处理代办
        return null;
    }

    @Override
    public DeleteTodoResult deleteTodo(DeleteTodoArg var1) {
        //删除代办

        return null;
    }
}
