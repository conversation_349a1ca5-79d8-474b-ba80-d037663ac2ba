package com.facishare.open.ding.provider.service.object;

import com.facishare.open.ding.api.enums.DingObjTypeEnum;
import com.facishare.open.ding.api.enums.SyncDirectionEnum;
import com.facishare.open.ding.api.exception.SyncDataException;
import com.facishare.open.ding.api.model.StandardData;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.DingMappingEmployeeService;
import com.facishare.open.ding.api.service.ObjectHandleService;
import com.facishare.open.ding.provider.entity.SyncDataMappingsEntity;
import com.facishare.open.ding.provider.manager.SyncDataMananger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Service("ContactObj")
public class ContactObjHandleServiceImpl implements ObjectHandleService {

    @Autowired
    private DingMappingEmployeeService employeeService;
    @Autowired
    private SyncDataMananger syncDataMananger;

    @Override
    public void handlerSpecialObjectHandler(Integer ei, String corpId, StandardData standardData) {

        Object creatorUserId = standardData.getMasterFieldVal().get("owner");
        if (!StringUtils.isEmpty(creatorUserId)){
            DingMappingEmployeeResult mappingByDingUserId =
              employeeService.findMappingByDingUserId(ei, String.valueOf(creatorUserId));
            if (mappingByDingUserId!=null){
                standardData.getMasterFieldVal().put("owner", Arrays.asList(String.valueOf(mappingByDingUserId.getEmployeeId())));
            }else {
                standardData.getMasterFieldVal().put("owner", Arrays.asList("-10000"));
            }
        }else {
            standardData.getMasterFieldVal().put("owner", Arrays.asList("-10000"));
        }

        Object account_id = standardData.getMasterFieldVal().get("account_id");
        if (!StringUtils.isEmpty(account_id)){
            List<SyncDataMappingsEntity> mappingsEntities =
              syncDataMananger.loadSyncDataMapping(ei, corpId, "AccountObj",
                DingObjTypeEnum.getEnumByCrmApiName("AccountObj").getDingObjApiName()
                , SyncDirectionEnum.TO_FXIAOKE, account_id.toString());
            Optional<SyncDataMappingsEntity> first =
              mappingsEntities.stream().filter(t -> t.getCreated() == 2).findFirst();
            if (first.isPresent()){
                standardData.getMasterFieldVal().put("account_id",first.get().getCrmDataId());
            }else {
               throw new SyncDataException(String.format("客户对象account_id【%s】未同步，无法转换id",account_id));
            }
        }
    }

}
