package com.facishare.open.ding.web.controller.cloud;


import com.facishare.open.ding.api.model.OrderModel;
import com.facishare.open.ding.api.result.*;
import com.facishare.open.ding.api.service.*;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.web.base.BaseController;
import com.facishare.open.ding.web.base.UserVo;
import com.facishare.open.ding.web.constants.ConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>钉钉云业务回调接口</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-08-20 10:03
 */
@CrossOrigin
@Slf4j
@RestController
@RequestMapping("/auth")
public class CloudAuthController extends BaseController {
    @Autowired
    private DingCorpMappingService corpMappingService;
    @Autowired
    private DingAuthService dingAuthService;

    @RequestMapping(value = "/isMainGroup", method = RequestMethod.GET)
    @ResponseBody
    public Result<IsMainGroupResult> isMainGroup() {
        UserVo userVo = getUserVo();
        if (ObjectUtils.isEmpty(userVo)) {
            return Result.newError(ResultCode.USER_NO_LOGIN);
        }
        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryByEi(userVo.getEnterpriseId());
        if (CollectionUtils.isEmpty(corpResult.getData())) {
            return Result.newError(ResultCode.ENTER_PRISE_NOT_AUTH);
        }
        String mainCorpId = corpResult.getData().get(0).getDingMainCorpId();
        boolean isMainGroup = StringUtils.isEmpty(mainCorpId) ? true : false;
        IsMainGroupResult result = IsMainGroupResult.builder().appId(ConfigCenter.APP_CRM_ID).IsMainGroup(isMainGroup).mainCorpId(mainCorpId).build();
        return Result.newSuccess(result);
    }


    @RequestMapping(value = "/getUnionId", method = RequestMethod.GET)
    @ResponseBody
    public Result<List<EmployeeDingVo>> getUnionId(@RequestParam("corpId") String dingCorpId, @RequestParam("suiteId") String suiteId) {
        //获取unionId.仅支持根部门下的人员获取
        Result<List<EmployeeDingVo>> rootUserResult = dingAuthService.queryRootUser(dingCorpId, suiteId);
        return rootUserResult;
    }

    @RequestMapping(value = "/createOrder", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> createOrder(@RequestParam("dingCorpId") String corpId,@RequestBody String data) {
        log.info("createOrder ing...");
        Result<Void> voidResult = dingAuthService.mockCreateOrder(data,corpId);
        return voidResult;
    }


}
