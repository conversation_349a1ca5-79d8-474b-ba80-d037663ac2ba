package com.facishare.open.ding.provider.manager;

import com.facishare.open.ding.api.result.BindFxEaResult;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.provider.dao.DingEnterpriseDao;
import com.facishare.open.ding.provider.entity.DingEnterprise;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.common.utils.BeanUtil;

import java.util.List;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p>类的详细说明</p>
 *
 * @version 1.0
 * @dateTime 2018-07-12 11:08
 *
 */
@Slf4j
@Component
public class DingEnterpriseManager {

    @Autowired
    private DingEnterpriseDao dingEnterpriseDao;

    private static final Integer BIND = 1;

    private static final Integer NOTBIND = 0;

    public Result<DingEnterpriseResult> queryEnterpriseByEa(String ea) {
        if(StringUtils.isBlank(ea)){
            log.warn("queryKcEnterpriseByEa param illegal, ea=[{}].", ea);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        DingEnterpriseResult dingEnterpriseResult = BeanUtil.copy(dingEnterpriseDao.findByEA(ea), DingEnterpriseResult.class);

        if (dingEnterpriseResult ==null){
            dingEnterpriseResult = new DingEnterpriseResult();
            dingEnterpriseResult.setIsBind(NOTBIND);
            return Result.newSuccess(dingEnterpriseResult);
        }
        else {
//            decrypt(dingEnterpriseResult);
            dingEnterpriseResult.setIsBind(BIND);
        }
        return Result.newSuccess(dingEnterpriseResult);
    }

    public Result<DingEnterpriseResult> queryEnterpriseByEi(Integer ei) {
        if(Objects.isNull(ei)){
            log.warn("queryKcEnterpriseByEi param illegal, ei=[{}].", ei);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        DingEnterpriseResult dingEnterpriseResult = BeanUtil.copy(dingEnterpriseDao.findByEI(ei), DingEnterpriseResult.class);

        if (Objects.isNull(dingEnterpriseResult)){
            dingEnterpriseResult = new DingEnterpriseResult();
            dingEnterpriseResult.setIsBind(NOTBIND);
            return Result.newSuccess(dingEnterpriseResult);
        }else {
            dingEnterpriseResult.setIsBind(BIND);
        }
        return Result.newSuccess(dingEnterpriseResult);
    }

    /**
     * 通过钉钉corpId查询绑定企业信息
     * @param corpId
     * @return
     */
    public Result<DingEnterpriseResult> queryEnterpriseByDingCorpId(String corpId) {
        if(StringUtils.isEmpty(corpId)){
            log.warn("queryKcEnterpriseByEi param illegal, corpId=[{}].", corpId);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        DingEnterpriseResult dingEnterpriseResult = BeanUtil.copy(dingEnterpriseDao.findByDingCorpId(corpId), DingEnterpriseResult.class);

        if (Objects.isNull(dingEnterpriseResult)){
            log.warn("the enterprise not bind, corpId=[{}].", corpId);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        return Result.newSuccess(dingEnterpriseResult);
    }

    @Transactional
    public Result<Integer> saveEnterprise(DingEnterprise enterprise){
        if (Objects.isNull(enterprise)){
            log.warn("saveEnterprise param illegal, enterprise=[{}].", enterprise);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        Integer count = dingEnterpriseDao.insert(enterprise);
        return Result.newSuccess(count);
    }

    public Result<Integer> deleteEnterprise(DingEnterprise enterprise){
        if (Objects.isNull(enterprise)){
            log.warn("saveEnterprise param illegal, enterprise=[{}].", enterprise);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        Integer count = dingEnterpriseDao.delete(enterprise);
        return Result.newSuccess(count);
    }

    public Result<DingEnterpriseResult> queryKcEnterprise(String ea) {
        if(StringUtils.isBlank(ea)){
            log.warn("queryKcEnterprise param illegal, ea=[{}].", ea);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        return Result.newSuccess(BeanUtil.copy(dingEnterpriseDao.findByEA(ea), DingEnterpriseResult.class));
    }

    @Transactional
    public Result<Integer> updateEnterprise(DingEnterprise dingEnterprise){
        if (dingEnterprise ==null){
            log.warn("updateKcEnterprise param illegal, dingEnterprise=[{}].", dingEnterprise);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        Integer count = dingEnterpriseDao.updateEnterpriseByEi(dingEnterprise);
        return Result.newSuccess(count);
    }

    public boolean queryKcEnterpriseByCorpId(String corpId) {
        if(StringUtils.isBlank(corpId)){
            log.warn("queryKcEnterpriseByCloudId param illegal, corpId=[{}].", corpId);
            return true;
        }
        DingEnterprise dingEnterprise = dingEnterpriseDao.findByDingCorpId(corpId);
        if (Objects.nonNull(dingEnterprise)){
            return true;
        }
        return false;
    }

    public Result<List<BindFxEaResult>> queryBindFxEa(Integer offset, Integer limit){
        return Result.newSuccess(dingEnterpriseDao.queryBindFxEa(offset, limit));
    }

    //修改回调地址使用
    public Result<Integer> updateCallBack(DingEnterprise dingEnterprise){
        if (dingEnterprise ==null){
            log.warn("updateKcEnterprise param illegal, dingEnterprise=[{}].", dingEnterprise);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        Integer count = dingEnterpriseDao.updateEnterpriseByEi(dingEnterprise);
        return Result.newSuccess(count);
    }

}
