package com.facishare.open.ding.provider.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.enums.SyncDirectionEnum;
import com.facishare.open.ding.api.exception.SyncDataException;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.dao.SyncDataMappingsDao;
import com.facishare.open.ding.provider.entity.SyncDataMappingsEntity;
import com.facishare.open.ding.provider.utils.BuriedSitesStatisticsUtils;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class SyncDataMananger {

    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;

    @Autowired
    private DingCorpMappingService dingCorpMappingService;

    public LoadingCache<String, String> cache = Caffeine.newBuilder().maximumSize(10000).expireAfterWrite(100, TimeUnit.MINUTES).refreshAfterWrite(90, TimeUnit.MINUTES).build(new CacheLoader<String, String>() {
        @Nullable
        @Override
        public String load(@NonNull String key) throws Exception {
            Result<List<DingCorpMappingVo>> listResult = dingCorpMappingService.queryByEi(Integer.valueOf(key));
            if (listResult.getData()!=null&&listResult.getData().size()>0){
                DingCorpMappingVo result = listResult.getData().get(0);
                return result.getEnterpriseName();
            }
            return null;
        }
    });

    //获取数据映射
    public List<SyncDataMappingsEntity> loadSyncDataMapping(Integer ei, String corpId, String crmObjectApiName, String dingObjApiName,
                                                      SyncDirectionEnum direction, String dataId){
        List<SyncDataMappingsEntity> syncDataMappings=null;
        if (direction==SyncDirectionEnum.FROM_FXIAOKE){
            syncDataMappings =
              syncDataMappingsDao.findMappingByObjAndCrmDataId(ei, corpId, crmObjectApiName, dingObjApiName, dataId);
        }else {
           syncDataMappings =
              syncDataMappingsDao.findMappingByObjAndDingDataId(ei, corpId, crmObjectApiName, dingObjApiName, dataId);
        }
        return syncDataMappings;
    }


    public int save(SyncDataMappingsEntity syncDataMappingsEntity){
        if (syncDataMappingsEntity.getId()==null){
            try {
                String name=cache.get(String.valueOf(syncDataMappingsEntity.getEi()));
                name=name==null?String.valueOf(syncDataMappingsEntity.getEi()):name;
                BuriedSitesStatisticsUtils.uploadBuriedStitesLog(String.valueOf(syncDataMappingsEntity.getEi()),name
                  ,syncDataMappingsEntity.getCrmObjectApiName(),syncDataMappingsEntity.getDirector());
            }catch (Exception e){
                log.warn("上报神策异常:{}",e.getMessage());
            }
            try{
                return syncDataMappingsDao.insertSyncDataMpping(syncDataMappingsEntity);
            }catch (Exception e){
                if (e instanceof org.springframework.dao.DuplicateKeyException){
                    log.warn("数据存在重复映射,忽略：{}", JSONObject.toJSONString(syncDataMappingsEntity));
                    return 0;
//                    throw  new SyncDataException("数据已经存在映射，可忽略");
                }
                throw e;
            }
        }else {
           return syncDataMappingsDao.update(syncDataMappingsEntity);
        }
    }


}
