package com.facishare.open.ding.api.service;

import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.common.result.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/7 16:45
 * @Version 1.0
 */
public interface DingCorpMappingService {

    Result<Integer> insertCorpMapping(DingCorpMappingVo dingCorpMappingVo);

    Result<List<DingCorpMappingVo>> queryCorpMappingByCorpId(String dingCorpId);

    Result<DingCorpMappingVo> queryMappingByAppId(String dingCorpId,Long appCode);

    Result<DingCorpMappingVo> queryMappingByConnector(String dingCorpId,Long appCode,int connector);

    Result<String> getToken(String dingCorpId);

    Result<Integer> updateRepeatIndex(Integer ei);

    Result<Integer> updateInitStatus(Integer ei);

    int update(DingCorpMappingVo dingCorpMappingVo);

    DingCorpMappingVo queryByConnector(int connector,int ei,Long appCode);

    Result<List<DingCorpMappingVo>> queryByEi(Integer ei);

    Result<List<DingCorpMappingVo>> queryByEa(String ea);

    /**
     * 更新企业绑定拓展字段
     * @param fsEa
     * @param extendField
     * @param extendValue
     * @return
     */
    Result<Void> updateEnterpriseExtend(String fsEa, String extendField, Object extendValue);

}
