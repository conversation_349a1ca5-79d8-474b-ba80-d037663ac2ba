package com.facishare.open.ding.cloud.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.eservice.base.result.EserviceResult;
import com.facishare.eservice.rest.cases.model.DingObjectDataListModel;
import com.facishare.eservice.rest.cases.service.EserviceObjectService;
import com.facishare.eservice.rest.common.HeaderObj;
import com.facishare.open.ding.api.arg.ComponentQueryArg;
import com.facishare.open.ding.api.arg.ErpEmployeeArg;
import com.facishare.open.ding.api.enums.DingObjTypeEnum;
import com.facishare.open.ding.api.model.DingObjData;
import com.facishare.open.ding.api.model.StandardData;
import com.facishare.open.ding.api.result.DingStorageResult;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.limiter.CrmRateLimiter;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.google.common.collect.Maps;
import com.jayway.jsonpath.JsonPath;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/1/17 10:05 数据推送相关接口
 * @Version 1.0
 */
@Service
@Slf4j
public class DataPushManager {
    @Autowired
    private HttpCloudManager httpCloudManager;
    @Autowired
    private DingCorpMappingService corpMappingService;
    @Autowired
    private DataConvertManager dataConvertManager;
    @Autowired
    private EserviceObjectService eserviceObjectService;


    public Result<Void> pushData(DingObjData dingObjData, String dingCorpId) {

        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryCorpMappingByCorpId(dingCorpId);
        if (CollectionUtils.isEmpty(corpResult.getData())) {
            log.info("push data error,corp not bind:{}", dingCorpId);
            return Result.newSuccess();
        }

        Map<String, String> hearMap = Maps.newHashMap();
        hearMap.put("id", dingObjData.getInstanceId());
        StandardData standardData = dataConvertManager.convertData(dingObjData, dingCorpId);
        String objApiName = DingObjTypeEnum.getEnumByDingApiName(dingObjData.getObjectType()).getCrmObjApiName();
        DingObjTypeEnum crmApiName = DingObjTypeEnum.getEnumByDingApiName(dingObjData.getObjectType());
        if (ObjectUtils.isEmpty(crmApiName)) {
            log.info("obj type not support:{}", dingObjData);
            return Result.newSuccess();
        }
        standardData.setObjAPIName(objApiName);
        //添加主键的数据ID
        String idFieldName = crmApiName.getIdFieldName();
        standardData.getMasterFieldVal().put(idFieldName, dingObjData.getInstanceId());
        String completeUrl = ConfigCenter.ERP_PUSH_DATA_URL.concat("/open/objdata/push2?version=%s&objectApiName=%s&tenantId=%s");
        String dingApiName = crmApiName.getDingObjApiName();
        if (crmApiName.getDingObjApiName().equals("crm_customer_personal")) {
            dingApiName = "crm_customer";
        }
        String pushDataUrl = String.format(completeUrl, "v1", dingApiName, corpResult.getData().get(0).getEi());
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            log.info("CrmManager.pushData,interface limit");
            return Result.newError(ResultCode.SERVER_BUSY);
        }
        String result = httpCloudManager.postUrl(pushDataUrl, standardData, hearMap);
        log.info("erp push data :{},result:{}", dingObjData, result);
        return Result.newSuccess();
    }

    //初始化策略 有可能出现license在erp那边查询不及时的问题
//    @Retryable( value = Exception.class,maxAttempts = 3, backoff = @Backoff(delay = 3000, multiplier = 2))
    public Result<String> initErpSetting(String ei) {
        String initUrl = ConfigCenter.ERP_PUSH_DATA_URL.concat("/out/ding/crm/connector/start?tenantId=").concat(ei);
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            log.info("CrmManager.initErpSetting,interface limit");
            return Result.newError(ResultCode.SERVER_BUSY);
        }
        String result = httpCloudManager.postUrl(initUrl, Maps.newHashMap(), createHeader());
        log.info("init erpSetting result:{}", result);
        String errCode = JsonPath.read(result, "$.errCode");

        if (!"s106240000".equals(errCode)) {
            try {
                for (int j = 0; j < 10; j++) {
                    Thread.sleep(3000);
                    //限速
                    if(!CrmRateLimiter.isAllowed(null)) {
                        log.info("CrmManager.initErpSetting,retry interface limit");
                        return Result.newError(ResultCode.SERVER_BUSY);
                    }
                    result = httpCloudManager.postUrl(initUrl, Maps.newHashMap(), createHeader());
                    errCode = JsonPath.read(result, "$.errCode");
                    if ("s106240000".equals(errCode)) {
                        break;
                    }
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        return Result.newSuccess();
    }

    //插入员工数据
    public Result<String> batchSaveEmployeeData(String ei, List<ErpEmployeeArg> erpEmployeeArgs) {
        String initEmpUrl = ConfigCenter.ERP_PUSH_DATA_URL.concat("/out/ding/crm/connector/batchBindEmployeeMapping?tenantId=").concat(ei);
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            log.info("CrmManager.batchSaveEmployeeData,interface limit");
            return Result.newError(ResultCode.SERVER_BUSY);
        }
        String initResult = httpCloudManager.postUrl(initEmpUrl, erpEmployeeArgs, createHeader());
        log.info("batchSaveEmployeeData initResult result:{}", initResult);
        return Result.newSuccess(initResult);
    }

    public static Map<String, String> createHeader() {
        Map<String, String> headers = Maps.newHashMapWithExpectedSize(1);
        headers.put("Content-Type", "application/json");
        return headers;
    }

    //获取服务通工单数据
    public DingStorageResult<DingObjectDataListModel.Result> getEserviceWorkData(ComponentQueryArg componentQueryArg) {
        HeaderObj headerObj = HeaderObj.newInstance(componentQueryArg.getEi());
        DingObjectDataListModel.Arg.Arg1 build = DingObjectDataListModel.Arg.Arg1.builder().accountId(componentQueryArg.getAccountId()).apiName(componentQueryArg.getApiName()).
                fsEa(componentQueryArg.getEa()).fsUserId(componentQueryArg.getFsUserId()).offset(componentQueryArg.getOffset()).limit(componentQueryArg.getLimit()).build();
//        DingObjectDataListModel.Arg.Arg1 eserviceArg = BeanUtil.copy(componentQueryArg, DingObjectDataListModel.Arg.Arg1.class);
        DingObjectDataListModel.Arg arg = new DingObjectDataListModel.Arg();
        arg.setArg1(build);

        EserviceResult<DingObjectDataListModel.Result> objectDataList = eserviceObjectService.getObjectDataList(headerObj, arg);
        return DingStorageResult.newSuccess(objectDataList.getData());
    }

    //推送单条数据
    public String syncCustomerDataInTime(String customerId, String customerName, String tenantId) {
        String completeUrl = ConfigCenter.ERP_PUSH_DATA_URL.concat("/out/ding/crm/connector/addCustomer2CRM?tenantId=%s&customerId=%s&customerName=%s");
        String syncDataUrl = String.format(completeUrl, tenantId, customerId, customerName);
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            log.info("CrmManager.syncCustomerDataInTime,interface limit");
            return null;
        }
        String result = httpCloudManager.postUrl(syncDataUrl, Maps.newHashMap(), createHeader());
        log.info("syncCustomerDataInTime customerId:{},result:{}", customerId, result);
        return result;
    }


}
