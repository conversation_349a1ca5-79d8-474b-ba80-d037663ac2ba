package com.facishare.open.ding.web.utils;

import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * trace 工具类
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/9
 */
@Slf4j
public class TraceUtil {

    public static String get(){
        TraceContext context = TraceContext.get();
        String traceId = context.getTraceId();
        return traceId;
    }
    /**
     * 如果原来有traceid，会移除
     * @param parentId
     */
    public static void initTrace(String parentId) {
        removeTrace();
        TraceContext context = TraceContext.get();
        String traceId = context.getTraceId();
        if (StringUtils.isNotEmpty(traceId)) {
            log.info("trans trace id,old:{},new:{}",traceId,parentId);
        }
        context.setTraceId(parentId);
        MDC.put("traceId", parentId);
    }

    public static void initTraceWithFormat(String tenantId){
        TraceUtil.initTrace(String.format("J-E.%s.-10000-erp%s", tenantId, TimeUtil.hms()));
    }

    public static String addChildTrace(String childId) {
        //对子id增加urlEncode，防止放到header异常
        childId = encodeTrace(childId);
        TraceContext context = TraceContext.get();
        String traceId = context.getTraceId();
        if (StringUtils.isEmpty(traceId)) {
            traceId = "anon";
        }
        traceId = traceId + "-" + childId;
        context.setTraceId(traceId);
        MDC.put("traceId", traceId);
        return traceId;
    }

    public static String removeChildTrace(String childId) {
        //对子id增加urlEncode，防止放到header异常
        childId = encodeTrace(childId);
        TraceContext context = TraceContext.get();
        String traceId = context.getTraceId();
        if (StringUtils.isEmpty(traceId)) {
            traceId = "anon";
        }
        String remove = "-"+childId;
        if (!traceId.endsWith(remove)){
            log.warn(" trace id invalid，context:{},traceId:{}",context,traceId);
        }
        traceId = StringUtils.removeEnd(traceId,remove);
        context.setTraceId(traceId);
        MDC.put("traceId", traceId);
        return traceId;
    }

    public static void removeTrace() {
        TraceContext.remove();
        MDC.remove("traceId");
    }

    public static String encodeTrace(String childId){
        try {
            childId = URLEncoder.encode(childId, "utf-8").replaceAll("%","");
            if (childId.length()>64){
                childId = childId.substring(0,64);
            }
        } catch (UnsupportedEncodingException e) {
            log.error("encode error",e);
        }
        return childId;
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        initTrace("test");
        String s = addChildTrace("华为有限公司");
        System.out.println(s);
    }
}
