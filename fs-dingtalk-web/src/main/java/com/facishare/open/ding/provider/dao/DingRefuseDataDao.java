package com.facishare.open.ding.provider.dao;

import com.facishare.open.ding.provider.arg.AppAuthEntity;
import com.facishare.open.ding.provider.arg.DingRefuseDataEntity;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/27 18:09
 * @Version 1.0
 */
public interface DingRefuseDataDao extends ICrudMapper<DingRefuseDataEntity> {
    @Insert("<script>" +
            "insert ignore into ding_refuse_data" +
            "(biz_data,ei,corp_id,refuse_app_id,refuse_suite_id,active_status,order_id) values " +
            "(#{dingRefuseDataEntity.bizData},#{dingRefuseDataEntity.ei},#{dingRefuseDataEntity.corpId},#{dingRefuseDataEntity.refuseAppId},#{dingRefuseDataEntity.refuseSuiteId},#{dingRefuseDataEntity.activeStatus},#{dingRefuseDataEntity.orderId})" +
            "</script>")
    Integer insertData(@Param("dingRefuseDataEntity") DingRefuseDataEntity dingRefuseDataEntity);

    @Update("<script>" +
            "update ding_refuse_data set active_status=#{status} " +
            "where corp_id=#{corpId} " +
            "</script>")
    Integer updateStatus(@Param("status")Integer status,@Param("corpId") String corpId);

    @Select("<script>" +
            "select count(*) from ding_refuse_data " +
            "where corp_id=#{corpId} and refuse_suite_id=#{suiteId} and active_status=1" +
            "</script>")
    Integer queryRefuseData( @Param("corpId") String corpId, @Param("suiteId") String suiteId);
}
