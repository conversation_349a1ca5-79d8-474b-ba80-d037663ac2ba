package com.facishare.open.ding.provider.task.executor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2020/7/24 10:10
 * @Version 1.0
 */
@Slf4j
public class DeleteEmployeeTask implements Runnable {

    private Map<String, User> deleteMap;
    private Integer ei;
    @Autowired
    private DingMappingEmployeeManager dingMappingEmployeeManager;
    @Autowired
    private ObjectMappingService objectMappingService;

    public DeleteEmployeeTask(Map<String, User> deleteMap, Integer ei) {
        this.deleteMap = deleteMap;
        this.ei=ei;
    }

    @Override
    public void run() {
          if (MapUtils.isNotEmpty(deleteMap)) {
                              deleteMap.entrySet().forEach(item -> {
                                  DingMappingEmployeeResult dingMappingEmployee = new DingMappingEmployeeResult();
                                 dingMappingEmployee.setEi(ei);
                                  dingMappingEmployee.setDingEmployeeId(item.getValue().getUserid());
                                 //停用纷享员工
                                  DingMappingEmployeeResult mappingEmp = dingMappingEmployeeManager.findIsBindByDingId(dingMappingEmployee);
                                  if (Objects.nonNull(mappingEmp) && Objects.nonNull(mappingEmp.getEmployeeId())) {
                                      Integer fxEmpId = mappingEmp.getEmployeeId();
                                      String fxEmpName = mappingEmp.getEmployeeName();
                                      objectMappingService.stopFxEmployee(ei, fxEmpId, fxEmpName);
                                  }
                                  //删除绑定关系
                                  dingMappingEmployeeManager.deleteBind(ei, item.getValue().getUserid());
                              });
                          }
    }
}
