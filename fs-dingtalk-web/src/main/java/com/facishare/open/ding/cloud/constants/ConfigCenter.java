package com.facishare.open.ding.cloud.constants;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.ding.api.model.*;
import com.facishare.open.ding.cloud.utils.SecurityUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;
import org.omg.CORBA.PUBLIC_MEMBER;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/4/27 20:37
 * @Version 1.0
 */
@Slf4j
public class ConfigCenter {
    public static String TOKEN = "";
    public static String ENCODING_AES_KEY = "";
    public static String SUITE_KEY = "";
    public static String SUITE_SECRET = "u";
    //public static String SUITE_TICKET = "";
    public static String RESET_URL = "http://**************:15056/";
    //    钉钉应用不同产品规格对应CRM不同的版本
    public static Map<String, String> GOODS_CODE = Maps.newHashMap();
    public static Map<String, List<NeighborProductQuantity>> MORE_APP_NEIGHBOR= Maps.newHashMap();
    public static String MORE_APP_NEIGHBOR_VALUE= "";
    public static String DING_STANDARD_CODE="";//标准版
    public static String DING_STANDARDPRO_CODE="";//专业版
    public static String DING_STRENGTHEN_CODE="";//旗舰版
    //钉钉产品编码对应appId
    public static Map<String, Long> MARKETING_APP_MAPPING = Maps.newHashMap();
    public static Integer ORDER_BUY_TYPE = 1;
    public static Integer ORDER_TRY_TYPE = 2;
    public static Integer TRY_MAX_PEOPLE = 100;
    public static String USE_ROLE_URL = "http://172.31.101.246:8431/API/v1/inner/rest/";
    public static String TRY_GOOD = "DT_GOODS_881621847020813_572028";
    public static String TEMP_CODE = "";
    public static Long APP_CRM_ID = 70480L;
    public static String MESSAGE_CARD_ID = "cef7b8d5fcdd4ab4ba6d4c8038759759";
    public static String MESSAGE_TEXT_ID = "ba1a582827d44f9c96f48d903eff81e2";
    public static String COMMENT_MESSAGE_ID = "06ce6f7ce8914c74bd1adcaa6397e8af";
    public static String COMMENT_CAR_MESSAGE_ID = "0393fad556894e178e08b3c8477bae1c";
    public static List<String> PROXY_PORT = Lists.newArrayList();
    //ISV corpId
    public static final String ISV_CORP_ID="ding0c8ee222b9a38265f2c783f7214b6d69";
    //ISV SSO SECRET
    public static final String SSO_SECRET="9VAlCsvJtV5RRC7ekvSeKqYVRLTQNzSe_r-nv7_SkjAjXSBA6Hr0I1-lyvHiqJt1";
    /**
     * 高级事件
     */
    public static List<Integer> HIGH_EVENT_TYPE=Lists.newArrayList(2,4,7,17,32,37,63,67);
    /**
     * 中低级事件
     */
    public static List<Integer> MEDIUM_EVENT_TYPE=Lists.newArrayList(13,14,15,16,20,22);

    //应用角色映射
    public static Map<Long, String> ROLE_MAP = Maps.newHashMap();
    //自定义字符串
    public static String NONCE_STR="Re6y5aksSpZaCRUwC";
    public static String h5Url="";
    public static String CRM_REST_OBJ_URL="http://172.31.101.246:17263/API/v1/rest/object";
    public static Map<String, AppParams> APP_PARAMS_MAP= Maps.newHashMap();
    public static Map<String, AppParams> APP_PARAMS_MAP_APP= Maps.newHashMap();
    public static String CRM_SUITE_ID="16740006";
    //钉钉CRM产品编码。规格编码的父类
    public static String CRM_GOODS_CODE="DT_GOODS_881621847020813";
    //crm版本的权重 标准版 1 专业版2 旗舰版 3
    public static Map<String,Integer> CRM_VERSION_WEIGHT=Maps.newHashMap();
    //钉钉商品规格版本的权重 标准版 1 专业版2 旗舰版 3
    public static Map<String,Integer> DING_VERSION_WEIGHT=Maps.newHashMap();
    //CRM版本对应的CRM版本-钉钉规格-crm产品ID
    public static Map<String, CRMDingProductMap> CRM_DING_PRODUCT_MAP=Maps.newHashMap();
    //版本编号
    public static String CRM_DING_STANDARDPRO_VERSION="dingtalk_standardpro_edition";
    //营销通小程序的suiteId
    public static String MARKETING_SUITE_ID="19854003";
    //消息模板id
    public static String CARD_TEMPLATE_ID="";
    public static String ROBOT_CODE="";
    public static String CHAT_REDIRECT_URL="";
    //灰度企业
    public static Set<String> OA_GRAY_TENANTS= Sets.newHashSet();
    //推送ERP数据平台的地址
    public static String ERP_PUSH_DATA_URL="https://www.ceshi112.com/erp/syncdata";
    //需要添加预置ERP-钉钉渠道的应用ID
    public static Set<String> INIT_SUITE_ID_CONNECT_CHANNEL = Sets.newHashSet();
    //历史数据分页大小
    public static Integer PAGE_SIZE=10;
    //永久免费的itemCode
    public static Set<String> FOREVER_FREE_ITEM_CODE= Sets.newHashSet();
    //直接同步附属产品
    public static Set<String> STRAIGHT_SYNC_PRODUCTS= Sets.newHashSet();
    //服务通的suiteID
    public static String SERVICE_SUITE_ID="";
    //支持的代办类型
   public static List<String> CRM_TO_BIZ_TYPES=Lists.newArrayList("401","402","403","404","406","408","410","411","452","456","457","460");
   //不需要同步角色的企业
   public static Set<String> NOT_SYNC_ROLE= Sets.newHashSet();
   //工单流代办类型
   public static Set<String> WORK_ORDER_BIZ_TYPES= Sets.newHashSet();
    //需要工单流的企业
   public static Set<String> OA_WORK_ORDER_TENANTS= Sets.newHashSet();
   //需要请求转发的方法
   public static Set<String> INVOKE_METHODS = Sets.newHashSet();
   //每次拉取的大小
   public static Integer DING_EVENT_ACCOUNT = 10;
    /**
     * 文件预览路径，%s为带ext的npath
     */
    public static String PREVIEW_FILE_PATH = "https://www.ceshi112.com/FSC/EM/File/GetByPath?path=%s";
    /**
     * 消息体最长数量
     */
    public static Integer NOTICE_MAX_SIZE = 500;
    /**
     * erp数据同步appId，这里共用一下
     */
    public static String ERP_SYNC_DATA_APP_ID = "FSAID_9897f5";
    /**
     * 接收告警人
     */
    public static Set<String> NOTIFICATION_MEMBERS = Sets.newHashSet();
    /**
     * 接收企信告警人
     */
    public static Set<String> ENTERPRISE_OPEN_NOTIFICATION_MEMBERS = Sets.newHashSet();
    /**
     * 接收告警企业
     */
    public static String NOTIFICATION_EA = "ddqybhzyl";
    /**
     * Xor加密密钥
     */
    public static String XOR_SECRET_KEY= "";
    public static String AVA_FS_COMMON_WEBVIEW_URL = "https://www.ceshi112.com/hcrm/dingtalk/#/ava_fs_common/pages/webview/index?url={url}";
    public static String DING_FUNCTION_URL = "https://www.ceshi112.com/hcrm/dingtalk/function/";
    public static String DING_FILE_URL = "https://www.ceshi112.com/dps/preview/bypath?path={path}&showHeader=1";

    //密钥
    public static String BASE64_SECRET = "";

    public static String CRM_RATE_LIMIT = "{\"defaultRateLimit\":20,\"84883\":2}";

    public static boolean isTemGray = Boolean.TRUE;

    public static Integer createCrmAccount = 30;

    public static String DING_ATME_URL = "https://www.ceshi112.com/hcrm/dingtalk?feedId={feedId}#/feed/detail";

    public static String CRM_SHORT_URL = "http://**************:13020/egress-api-service/api/v2/private-short-urls";

    public static String FILE_VIEW_URL = "ava-preview-nfile?forceRedirectH5=true&nfDatas={nfDatas}";

    static {
        ConfigFactory.getInstance().getConfig("fs-open-dingtalk-all", config -> {
            BASE64_SECRET = config.get("BASE64_SECRET", BASE64_SECRET);
            TOKEN = config.get("TOKEN", TOKEN);
            DING_STANDARD_CODE = config.get("DING_STANDARD_CODE", DING_STANDARD_CODE);
            DING_STANDARDPRO_CODE = config.get("DING_STANDARDPRO_CODE", DING_STANDARDPRO_CODE);
            DING_STRENGTHEN_CODE = config.get("DING_STRENGTHEN_CODE", DING_STRENGTHEN_CODE);
            ENCODING_AES_KEY = config.get("ENCODING_AES_KEY", ENCODING_AES_KEY);
            TEMP_CODE = config.get("TEMP_CODE", TEMP_CODE);
            SUITE_KEY = config.get("SUITE_KEY", SUITE_KEY);
            SUITE_SECRET = config.get("SUITE_SECRET", SUITE_SECRET);
            //SUITE_TICKET = config.get("SUITE_TICKET", SUITE_TICKET);//remove only
            RESET_URL = config.get("RESET_URL", RESET_URL);
            GOODS_CODE = JSONObject.parseObject(config.get("GOODS_CODE"), new TypeReference<Map<String, String>>() {
            });
            String data=config.get("MORE_APP_NEIGHBOR");
            MORE_APP_NEIGHBOR = JSONObject.parseObject(config.get("MORE_APP_NEIGHBOR"), new TypeReference<Map<String, List<NeighborProductQuantity>>>() {
            });
            MARKETING_APP_MAPPING = JSONObject.parseObject(config.get("MARKETING_APP_MAPPING"), new TypeReference<Map<String, Long>>() {
            });
            ROLE_MAP = JSONObject.parseObject(config.get("ROLE_MAP"), new TypeReference<Map<Long, String>>() {
            });
            TRY_MAX_PEOPLE = config.getInt("TRY_MAX_PEOPLE", TRY_MAX_PEOPLE);
            PROXY_PORT = Splitter.on(":").splitToList(config.get("PROXY_PORT", ""));
            USE_ROLE_URL = config.get("USE_ROLE_URL", USE_ROLE_URL);
            APP_CRM_ID = config.getLong("APP_CRM_ID", APP_CRM_ID);
            MESSAGE_CARD_ID = config.get("MESSAGE_CARD_ID", MESSAGE_CARD_ID);
            MESSAGE_TEXT_ID = config.get("MESSAGE_TEXT_ID", MESSAGE_TEXT_ID);
            COMMENT_MESSAGE_ID = config.get("COMMENT_MESSAGE_ID", COMMENT_MESSAGE_ID);
            COMMENT_CAR_MESSAGE_ID = config.get("COMMENT_CAR_MESSAGE_ID", COMMENT_CAR_MESSAGE_ID);
            TRY_GOOD = config.get("TRY_GOOD", TRY_GOOD);
            h5Url = config.get("SUITE_KEY", h5Url);
            CRM_REST_OBJ_URL = config.get("CRM_REST_OBJ_URL", CRM_REST_OBJ_URL);
            CRM_SUITE_ID = config.get("CRM_SUITE_ID", CRM_SUITE_ID);
            List<AppParams> configParamsMap = JSONArray.parseArray(config.get("APP_PARAMS_MAP"), AppParams.class);
            APP_PARAMS_MAP=configParamsMap.stream().collect(Collectors.toMap(AppParams::getSuiteId, Function.identity(),(key1, key2) -> key2));
            APP_PARAMS_MAP_APP=configParamsMap.stream().collect(Collectors.toMap(AppParams::getAppId, Function.identity(),(key1, key2) -> key2));
            CRM_GOODS_CODE = config.get("CRM_GOODS_CODE", CRM_GOODS_CODE);
            CRM_VERSION_WEIGHT = JSONObject.parseObject(config.get("CRM_VERSION_WEIGHT"),new TypeReference<Map<String,Integer>>(){});
            DING_VERSION_WEIGHT = JSONObject.parseObject(config.get("DING_VERSION_WEIGHT"),new TypeReference<Map<String,Integer>>(){});
            CRM_DING_PRODUCT_MAP = JSONObject.parseObject(config.get("CRM_DING_PRODUCT_MAP"),new TypeReference<Map<String,CRMDingProductMap>>(){});
            MARKETING_SUITE_ID = config.get("MARKETING_SUITE_ID", MARKETING_SUITE_ID);
            SERVICE_SUITE_ID = config.get("SERVICE_SUITE_ID", SERVICE_SUITE_ID);
            CARD_TEMPLATE_ID = config.get("CARD_TEMPLATE_ID", CARD_TEMPLATE_ID);
            ROBOT_CODE = config.get("ROBOT_CODE", ROBOT_CODE);
            CHAT_REDIRECT_URL = config.get("CHAT_REDIRECT_URL", CHAT_REDIRECT_URL);
            PAGE_SIZE = config.getInt("PAGE_SIZE", PAGE_SIZE);
            OA_GRAY_TENANTS= ImmutableSet.copyOf(Splitter.on(";").split(config.get("OA_GRAY_TENANTS", "")));
            FOREVER_FREE_ITEM_CODE= ImmutableSet.copyOf(Splitter.on(";").split(config.get("FOREVER_FREE_ITEM_CODE", "")));
            ERP_PUSH_DATA_URL = config.get("ERP_PUSH_DATA_URL", ERP_PUSH_DATA_URL);
            INIT_SUITE_ID_CONNECT_CHANNEL = ImmutableSet.copyOf(Splitter.on(",").split(config.get("INIT_SUITE_ID_CONNECT_CHANNEL", "")));
            STRAIGHT_SYNC_PRODUCTS = ImmutableSet.copyOf(Splitter.on(",").split(config.get("STRAIGHT_SYNC_PRODUCTS", "")));
            NOT_SYNC_ROLE = ImmutableSet.copyOf(Splitter.on(",").split(config.get("NOT_SYNC_ROLE", "")));
            CRM_TO_BIZ_TYPES = ImmutableList.copyOf(Splitter.on(",").split(config.get("CRM_TO_BIZ_TYPES", "401,402,403,404,406,408,410,411,452,456,457,460")));
            OA_WORK_ORDER_TENANTS= ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("OA_WORK_ORDER_TENANTS", "")));
            WORK_ORDER_BIZ_TYPES=  ImmutableSet.copyOf(
                    Splitter.on(",").split(config.get("WORK_ORDER_BIZ_TYPES", "")));
            INVOKE_METHODS = ImmutableSet.copyOf(
                    Splitter.on(",").split(config.get("INVOKE_METHODS", "")));
            DING_EVENT_ACCOUNT = config.getInt("DING_EVENT_ACCOUNT", DING_EVENT_ACCOUNT);
            PREVIEW_FILE_PATH = config.get("PREVIEW_FILE_PATH", PREVIEW_FILE_PATH);
            NOTICE_MAX_SIZE = config.getInt("NOTICE_MAX_SIZE", NOTICE_MAX_SIZE);
            ERP_SYNC_DATA_APP_ID = config.get("ERP_SYNC_DATA_APP_ID", ERP_SYNC_DATA_APP_ID);
            NOTIFICATION_EA = config.get("NOTIFICATION_EA", NOTIFICATION_EA);
            NOTIFICATION_MEMBERS =  ImmutableSet.copyOf(Splitter.on(",").split(config.get("NOTIFICATION_MEMBERS", "")));
            ENTERPRISE_OPEN_NOTIFICATION_MEMBERS =  ImmutableSet.copyOf(Splitter.on(",").split(config.get("ENTERPRISE_OPEN_NOTIFICATION_MEMBERS", "")));
            XOR_SECRET_KEY = config.get("XOR_SECRET_KEY", XOR_SECRET_KEY);
            AVA_FS_COMMON_WEBVIEW_URL = config.get("AVA_FS_COMMON_WEBVIEW_URL", AVA_FS_COMMON_WEBVIEW_URL);
            DING_FUNCTION_URL = config.get("DING_FUNCTION_URL", DING_FUNCTION_URL);
            DING_FILE_URL = config.get("DING_FILE_URL", DING_FILE_URL);
            CRM_RATE_LIMIT = config.get("CRM_RATE_LIMIT", CRM_RATE_LIMIT);
            isTemGray = config.getBool("isGray", Boolean.TRUE);
            createCrmAccount = config.getInt("createCrmAccount", 30);
            DING_ATME_URL = config.get("DING_ATME_URL", DING_ATME_URL);
            CRM_SHORT_URL = config.get("CRM_SHORT_URL", CRM_SHORT_URL);
            FILE_VIEW_URL = config.get("FILE_VIEW_URL", FILE_VIEW_URL);
        });

        //解密
//        TOKEN = SecurityUtil.decryptStr(TOKEN);
//        ENCODING_AES_KEY = SecurityUtil.decryptStr(ENCODING_AES_KEY);
//        SUITE_KEY = SecurityUtil.decryptStr(SUITE_KEY);
//        SUITE_SECRET = SecurityUtil.decryptStr(SUITE_SECRET);
        //SUITE_TICKET = SecurityUtil.decryptStr(SUITE_TICKET);
    }
}
