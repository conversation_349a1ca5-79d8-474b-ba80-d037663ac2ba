package com.facishare.open.ding.api.service;

import com.facishare.open.ding.api.model.FsUserInfoModel;
import com.facishare.open.ding.api.result.*;
import com.facishare.open.ding.api.vo.*;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.common.model.UserVo;
import com.facishare.open.ding.common.result.Result;

import java.util.List;
import java.util.Map;

/**
 * <p>类的详细说明</p>
 *
 * @version 1.0
 * @dateTime 2018-07-12 10:51
 */
public interface ObjectMappingService {

    /**
     * 根据EI查询绑定了的职员
     *
     * @param ei
     * @return
     */
    Result<Map<String, Object>> queryMappingEmployee(BindEmpVo vo, Integer ei);

    /**
     * 根据钉钉的unionId检索
     */
    Result<List<DingMappingEmployeeResult>> queryInfoByUserId(String unionId);
    /**
     * 根据CrmEmpId 查询绑定
     */
    Result<List<DingMappingEmployeeResult>> batchQueryMapping(Integer ei,List<Integer> crmIds);

    /**
     * 批量查询crm员工ID
     */
    Result<List<Integer>> queryMappingCrm(Integer ei);

    /**
     * 根据EI查询钉钉中未同步的职员
     *
     * @param ei
     * @return
     */
    Result<Map<String, Object>> queryNewEmployee(BindEmpVo vo, Integer ei, String ea, Integer userId);


    /**
     * 初始化后拉全量组织架构数据
     *
     * @param ei
     * @return
     */
    Result<Map<String, Object>> syncPullOrganizationData(Integer ei, String ea, Integer userId);

    /**
     * 立即同步钉钉员工
     *
     * @param ei
     * @param userId
     * @return
     */
    Result<Boolean> syncNewEmployee(Integer ei, Integer userId);

    Result<List<DingMappingEmployeeResult>> getEmployeeFs(Integer ei);

    /**
     * 批量保存绑定的职员
     *
     * @param list
     * @return
     */
    Result<Map<String, Integer>> bindEmployee(List<DingMappingEmployeeResult> list, Integer ei, Integer employeeId);

    /**
     * 解绑
     *
     * @param ei
     * @param cloudCode
     * @return
     */
    Result<Integer> deleteBind(Integer ei, String cloudCode);

    /**
     * 保存设置
     *
     * @param dingMappingEmployeeResult
     * @return
     */
    Result<Integer> saveOperation(DingMappingEmployeeResult dingMappingEmployeeResult);

    /**
     * 创建纷享职员
     *
     * @param vo
     * @return
     */
    Result<Integer> createFxEmployee(EmployeeVo vo);

    /**
     * 创建部门
     */
    Result<Void> obtainOrderDept(Integer ei, String ea, List<Dept> orderDepts);

    /**
     * model2批量创建员工
     *
     * @param vo
     * @return
     */
    Result<Integer> batchCreateFxEmployee(List<CreateCrmEmployeeVo> vo, Integer ei, Integer userId);

    Result<Void> modifyFxEmployee(Integer ei, int fxEmpId, User user);

    Result<Void> stopFxEmployee(Integer ei, Integer fxEmpId, String fxEmpName);

    Result<Void> updateEmp(String corpId, List<String> userIds);

    Result<Void> stopEmp(String corpId, List<String> userIds);

    Result<DingMappingEmployeeResult> queryEmployeeByUnionId(Integer ei, String unionId);

    Result<DingMappingEmployeeResult> queryEmpByDingUserId(Integer ei, String dingUserId);

    Result<List<BindFxUserResult>> getBindEiAndUser(Integer offset, Integer limit);

    Result<List<BindFxEaResult>> getBindEa(Integer offset, Integer limit);

    //条件查询员工
    Result<ConditionEmployeeResult> conditionEmployee(QueryEmployeeVo queryEmployeeVo, Integer enterpriseId);

    //返回部门
    Result<List<DeptVo>> conditionDepts(Integer enterpriseId);

    //创建部门
    Result<Integer> createFxDept(String corpId, List<Long> depts);

    //修改部门
    Result<Integer> modifyFxDept(String corpId, List<Long> depts);

    //删除部门
    Result<Integer> removeFxDept(String corpId, List<Long> depts);

    //删除部门中间表数据
    Result<Integer> removeDataDept(Integer corpId, Integer dept);

    //创建部门负责人
    Result<Void> createDeptOwner(String clientIp, String accessToken, String token, Integer ei);

    //重新映射部门
    Result<Void> againEmployeeMapping(Integer ei);

    //不依赖外部接口
    Result<Void> independenceAgainMapping(Integer ei);


    //模式二初始化部门人员
    Result<Void> initModelEmployee(Integer ei, Integer userId, String ea);

    //插入人员中间表
    Result<Void> allPullEmployeeInsert(Integer ei, Integer userId, String ea);

    //全量upsert部门信息
    Result<Void> upsertAllDept(Integer ei, Integer userId, String ea);

    //查询没有主部门的人员判断消息
    Result<Void> fixNoMainDept(Integer ei, Integer userId, String ea);

    //处理根级部门的信息
    Result<Void> fixTreeDept(Integer ei, String ea, Integer userId);

    //删除员工
    Result<Integer> deleteEmpByDingId(Integer ei, String empId);

    //删除员工
    Result<Integer> deleteEmpByFsId(Integer ei, Integer empId);

    Result<DingEnterpriseResult> queryEnterpriseByEi(Integer ei);

    Result<Integer> getSourceCount(int ei, String sourceId);

    Result<String> queryUserId(int ei, int receiverId);

    Result<List<String>> queryUserIds(int ei, List<Integer> receiverIds);
    //批量查询钉钉员工ID
    Result<List<DingMappingEmployeeResult>> batchGetEmpIds(Integer ei,List<Integer> empIds);


    Result<List<DingTaskVo>> getDingTaskVo(int ei, String sourceId);

    Result<Integer> insertSource(DingTaskVo dingTaskVo);

    Result<Integer> updateStatus(int status,int ei, String sourceId, int employeeId);

    Result<Integer> updateExecutor(List<Integer> deleteEmployeeIds, int ei, String sourceId);

    Result<UserVo> getUserDetail(String accessToken,String userId,String clientIp);

    Result<FsUserInfoModel> getFsUserInfo(String outEa, String outUserId);
}
