package com.facishare.open.ding.cloud.template.outer.msg.todo;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.result.AppAuthResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.result.HttpResponseMessage;
import com.facishare.open.ding.api.service.AppAuthService;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.api.vo.DingTaskVo;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.cloud.utils.OkHttp3MonitorUtils4Cloud;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.fxiaoke.message.extrnal.platform.model.result.DeleteTodoResult;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 钉钉发送deleteTodo消息模板
 * <AUTHOR>
 * @date 20241008
 */
@Slf4j
@Component
public class DingTalkSendDeleteTodoMsgHandlerTemplate extends DingTalkSendTodoMsgHandlerTemplate {
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private DingCorpMappingService corpMappingService;
    @Autowired
    private AppAuthService appAuthService;
    @Autowired
    private DingManager dingManager;

    @Override
    public void buildMsg(MethodContext context) {
        log.info("DingTalkSendDeleteTodoMsgHandlerTemplate.buildMsg,context={}",context);
        DeleteTodoArg deleteTodoArg = context.getData();

        DeleteTodoResult result = new DeleteTodoResult();
        result.setCode(200);
        result.setMessage("发送成功");

        //代办类型
        if(!isCrmBizType(deleteTodoArg.getBizType())){
//            log.info("CloudExternalTodoServiceImpl.dealTodo,todo not support bizType.deleteTodoArg={}.", deleteTodoArg);
//            return result;
            log.info("DingTalkSendDeleteTodoMsgHandlerTemplate.buildMsg,todo not support bizType, deleteTodoArg={}",
                    deleteTodoArg);
            context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
            return;
        }

        //判断灰度企业
        if(!ConfigCenter.OA_GRAY_TENANTS.contains(deleteTodoArg.getEa())) {
            //return result;
            context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
            return;
        } else {
            if(ConfigCenter.WORK_ORDER_BIZ_TYPES.contains(deleteTodoArg.getBizType())) {
                if(!ConfigCenter.OA_WORK_ORDER_TENANTS.contains(deleteTodoArg.getEa())) {
                    //return result;
                    context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
                    return;
                }
            }
        }

        //查询企业绑定关系
        Result<List<DingCorpMappingVo>> mappingResult = corpMappingService.queryByEi(deleteTodoArg.getEi());
        if (Objects.isNull(mappingResult) || Objects.isNull(mappingResult.getData())) {
//            log.warn("CloudExternalTodoServiceImpl.dealTodo,ea not bind.deleteTodoArg={}.", deleteTodoArg);
//            return result;
            log.info("DingTalkSendDeleteTodoMsgHandlerTemplate.buildMsg,queryByEi,ea not bind.deleteTodoArg={}",
                    deleteTodoArg);
            context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
            return;
        }
        String dingCorpId = mappingResult.getData().get(0).getDingCorpId();
        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(dingCorpId, ConfigCenter.APP_CRM_ID, null);
        // 需要判断是否购买了纷享钉钉CRM。其他应用的不发送
        if(ObjectUtils.isEmpty(appAuthResult.getData())){
//            log.info("CloudExternalTodoServiceImpl.dealTodo,enterprise not support send todo :{}",dingCorpId);
//            return result;
            log.info("DingTalkSendDeleteTodoMsgHandlerTemplate.buildMsg,conditionAppAuth,enterprise not support send todo={}",
                    dingCorpId);
            context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
            return;
        }

        //获取token
        String accessToken = dingManager.getAccessToken(dingCorpId, ConfigCenter.CRM_SUITE_ID).getData();
        Map<String, String> dingTalkToken = new HashMap<>();
        dingTalkToken.put("x-acs-dingtalk-access-token", accessToken);

        Result<List<DingTaskVo>> dingTaskVo = objectMappingService.getDingTaskVo(deleteTodoArg.getEi(), deleteTodoArg.getSourceId());
        if(CollectionUtils.isEmpty(dingTaskVo.getData())) {
//            log.info("CloudExternalTodoServiceImpl.dealTodo,dingTalkToken is null,deleteTodoArg={}.", deleteTodoArg);
//            return result;
            log.info("DingTalkSendDeleteTodoMsgHandlerTemplate.buildMsg,getDingTaskVo,dingTalkToken is null,deleteTodoArg={}",
                    deleteTodoArg);
            context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
            return;
        }

        //获取人员详细信息
        Result<List<DingMappingEmployeeResult>> employeeInfosResult = objectMappingService.batchQueryMapping(deleteTodoArg.getEi(), deleteTodoArg.getDeleteEmployeeIds());
        if(CollectionUtils.isEmpty(employeeInfosResult.getData())) {
//            log.info("CloudExternalTodoServiceImpl.dealTodo,user not bind,deleteTodoArg={}.", deleteTodoArg);
//            return result;
            log.info("DingTalkSendDeleteTodoMsgHandlerTemplate.buildMsg,batchQueryMapping,user not bind,deleteTodoArg={}",
                    deleteTodoArg);
            context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
            return;
        }
        List<DingMappingEmployeeResult> employeeInfos = employeeInfosResult.getData();
        int operatorId = employeeInfos.get(0).getEmployeeId();
        String operatorUnionId = employeeInfos.get(0).getDingUnionId();
        List<String> userUnionIds = new LinkedList<>();
        List<Integer> userIds = new LinkedList<>();
        for (int i = 0; i < employeeInfos.size(); i++) {
            userUnionIds.add(employeeInfos.get(i).getDingUnionId());
            userIds.add(employeeInfos.get(i).getEmployeeId());
        }

        //查看代办详情
        String dealTalkUrl = DINGTALK_TODO + operatorUnionId + "/tasks/sources/" + deleteTodoArg.getSourceId();
        Gson gson = new Gson();
        HttpResponseMessage messageResult = OkHttp3MonitorUtils4Cloud.sendOkHttp3Get(dealTalkUrl, dingTalkToken, new HashMap<>());
        log.info("DingTalkSendDeleteTodoMsgHandlerTemplate.buildMsg,dealTalkUrl={},messageResult={}.", dealTalkUrl, messageResult);
        JSONObject jsonObject = JSONObject.parseObject(messageResult.getContent());
        if (messageResult.getStatusCode() == 200) {
            String talkId = String.valueOf(jsonObject.get("id"));
            String executorIds = JSONArray.toJSONString(jsonObject.get("executorIds"));

            // 将json字符串转换为list集合
            List<String> cardVos = new LinkedList<>(JSONArray.parseArray(executorIds, String.class));
            for (int i = 0; i < cardVos.size(); i++) {
                cardVos.removeAll(userUnionIds);
            }
            String participantIds = JSONArray.toJSONString(jsonObject.get("participantIds"));
            // 将json字符串转换为list集合
            List<String> cardVos1 = new LinkedList<>(JSONArray.parseArray(participantIds, String.class));
            for (int i = 0; i < cardVos1.size(); i++) {
                cardVos1.removeAll(userUnionIds);
            }
            Boolean flag = false;
            Map<String, Object> dingTalk = Maps.newHashMap();
            dingTalk.put("done", flag);
            dingTalk.put("executorIds",cardVos);
            dingTalk.put("participantIds",cardVos1);
            String updateToDo = DINGTALK_TODO + operatorUnionId + "/tasks/" + talkId + "?operatorId=" + operatorUnionId;
            HttpResponseMessage updateToDoMessageResult = OkHttp3MonitorUtils4Cloud.sendOkHttp3Put(updateToDo, dingTalkToken, gson.toJson(dingTalk));
            log.info("DingTalkSendDeleteTodoMsgHandlerTemplate.buildMsg,ea={},dingTalk={},updateToDo={},updateToDoMessageResult={}.", deleteTodoArg.getEa(), dingTalk, updateToDo, updateToDoMessageResult);
            if (updateToDoMessageResult.getStatusCode() == 200) {
                Result<Integer> countResult = objectMappingService.updateExecutor(userIds, deleteTodoArg.getEi(), deleteTodoArg.getSourceId());
                log.warn("DingTalkSendDeleteTodoMsgHandlerTemplate.buildMsg,更新代办成功，deleteEmployeeId={}，count={}，更新代办成功.", operatorId, countResult.isSuccess() ? countResult.getData() : 0);
                //增加逻辑，客户删除了代办任务，把状态设置为完成
                Result<List<DingTaskVo>> dingTaskVo1 = objectMappingService.getDingTaskVo(deleteTodoArg.getEi(), deleteTodoArg.getSourceId());
                if(CollectionUtils.isEmpty(dingTaskVo1.getData())) {
                    //调用完成的接口
                    this.updateTodoStatus(operatorUnionId, talkId, dingTalkToken);
                }
            } else {
                log.warn("DingTalkSendDeleteTodoMsgHandlerTemplate.buildMsg: 更新代办失败，messageArg={}，userId={}.", dingTalk, userIds);
            }
        }
        //return result;

        context.setResult(TemplateResult.newSuccess());
        context.setData(result);
    }

    @Override
    public void sendMsg(MethodContext context) {
        log.info("DingTalkSendDeleteTodoMsgHandlerTemplate.sendMsg,context={}",context);

        DeleteTodoResult result = new DeleteTodoResult();
        result.setCode(200);
        result.setMessage("发送成功");

        context.setResult(TemplateResult.newSuccess(result));
    }
}
