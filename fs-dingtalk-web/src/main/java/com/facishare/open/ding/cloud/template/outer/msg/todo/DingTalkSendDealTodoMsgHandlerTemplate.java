package com.facishare.open.ding.cloud.template.outer.msg.todo;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.result.AppAuthResult;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.result.HttpResponseMessage;
import com.facishare.open.ding.api.service.AppAuthService;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.api.vo.DingTaskVo;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.cloud.utils.OkHttp3MonitorUtils4Cloud;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.result.DealTodoResult;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 钉钉发送dealTodo消息模板
 * <AUTHOR>
 * @date 20241008
 */
@Slf4j
@Component
public class DingTalkSendDealTodoMsgHandlerTemplate extends DingTalkSendTodoMsgHandlerTemplate {
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private DingCorpMappingService corpMappingService;
    @Autowired
    private AppAuthService appAuthService;
    @Autowired
    private DingManager dingManager;

    @Override
    public void buildMsg(MethodContext context) {
        log.info("DingTalkSendDealTodoMsgHandlerTemplate.buildMsg,context={}",context);
        DealTodoArg dealTodoArg = context.getData();

        DealTodoResult result = new DealTodoResult();
        result.setCode(200);
        result.setMessage("发送成功");

        //代办类型
        if(!isCrmBizType(dealTodoArg.getBizType())){
//            log.info("CloudExternalTodoServiceImpl.dealTodo,todo not support bizType.dealTodoArg={}.", dealTodoArg);
//            return result;
            log.info("DingTalkSendDealTodoMsgHandlerTemplate.buildMsg,todo not support bizType, dealTodoArg={}",
                    dealTodoArg);
            context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
            return;
        }

        //判断灰度企业
        if(!ConfigCenter.OA_GRAY_TENANTS.contains(dealTodoArg.getEa())) {
            //return result;
            context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
            return;
        } else {
            if(ConfigCenter.WORK_ORDER_BIZ_TYPES.contains(dealTodoArg.getBizType())) {
                if(!ConfigCenter.OA_WORK_ORDER_TENANTS.contains(dealTodoArg.getEa())) {
                    //return result;
                    context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
                    return;
                }
            }
        }

        //查询企业绑定关系
        Result<DingEnterpriseResult> mappingEnterprise = objectMappingService.queryEnterpriseByEi(dealTodoArg.getEi());
        if (Objects.isNull(mappingEnterprise) || Objects.isNull(mappingEnterprise.getData())) {
//            log.warn("CloudExternalTodoServiceImpl.dealTodo,ea not bind.dealTodoArg={}.", dealTodoArg);
//            return result;
            log.info("DingTalkSendDealTodoMsgHandlerTemplate.buildMsg,queryEnterpriseByEi,ea not bind.dealTodoArg={}",
                    dealTodoArg);
            context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
            return;
        }
        Result<List<DingCorpMappingVo>> mappingResult = corpMappingService.queryByEi(dealTodoArg.getEi());
        String dingCorpId = mappingResult.getData().get(0).getDingCorpId();
        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(dingCorpId, ConfigCenter.APP_CRM_ID, null);
        // 需要判断是否购买了纷享钉钉CRM。其他应用的不发送
        if (ObjectUtils.isEmpty(appAuthResult.getData())) {
//            log.info("CloudExternalTodoServiceImpl.dealTodo,enterprise not support send todo :{}", dingCorpId);
//            return result;
            log.info("DingTalkSendDealTodoMsgHandlerTemplate.buildMsg,conditionAppAuth,enterprise not support send todo={}",
                    dingCorpId);
            context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
            return;
        }

        //获取token
        String accessToken = dingManager.getAccessToken(dingCorpId, ConfigCenter.CRM_SUITE_ID).getData();
        Map<String, String> dingTalkToken = new HashMap<>();
        dingTalkToken.put("x-acs-dingtalk-access-token", accessToken);

        List<DingTaskVo> dingTaskVos = objectMappingService.getDingTaskVo(dealTodoArg.getEi(), dealTodoArg.getSourceId()).getData();
        if (CollectionUtils.isEmpty(dingTaskVos)) {
//            log.info("CloudExternalTodoServiceImpl.dealTodo,dingTalkToken is null,dealTodoArg={}.", dealTodoArg);
//            return result;
            log.info("DingTalkSendDealTodoMsgHandlerTemplate.buildMsg,getDingTaskVo,dingTalkToken is null,dealTodoArg={}",
                    dealTodoArg);
            context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
            return;
        }

        //标志
        Boolean flag = true;

        //获取人员详细信息
        Result<List<DingMappingEmployeeResult>> employeeInfosResult = objectMappingService.batchQueryMapping(dealTodoArg.getEi(), dealTodoArg.getOperators());
        if(CollectionUtils.isEmpty(employeeInfosResult.getData())) {
//            log.info("CloudExternalTodoServiceImpl.dealTodo,user not bind,dealTodoArg={}.", dealTodoArg);
//            return result;
            log.info("DingTalkSendDealTodoMsgHandlerTemplate.buildMsg,batchQueryMapping,user not bind,dealTodoArg={}",
                    dealTodoArg);
            context.setResult(TemplateResult.newInstance(result.getCode(),result.getMessage(),result));
            return;
        }
        List<DingMappingEmployeeResult> employeeInfos = employeeInfosResult.getData();
        String operatorUnionId = employeeInfos.get(0).getDingUnionId();
        Map<String, Object> dingTalk = Maps.newHashMap();
        List<Map<String, Object>> executorStatusList = new LinkedList<>();
        List<Integer> userIds = new LinkedList<>();
        for (int i = 0; i < employeeInfos.size(); i++) {
            Map<String, Object> executorStatus = Maps.newHashMap();
            executorStatus.put("id", employeeInfos.get(i).getDingUnionId());
            executorStatus.put("isDone", flag);
            executorStatusList.add(executorStatus);
            userIds.add(employeeInfos.get(i).getEmployeeId());
        }

        dingTalk.put("executorStatusList", executorStatusList);
        //更换处理接口
        Gson gson = new Gson();
        String dealTalkUrl = DINGTALK_TODO + operatorUnionId + "/tasks/" + dingTaskVos.get(0).getTaskId() + "/executorStatus?operatorId=" + operatorUnionId;
        HttpResponseMessage messageResult = OkHttp3MonitorUtils4Cloud.sendOkHttp3Put(dealTalkUrl, dingTalkToken, gson.toJson(dingTalk));
        log.info("DingTalkSendDealTodoMsgHandlerTemplate.buildMsg,dealTalkUrl={},dingTalk={},messageResult={}", dealTalkUrl, dingTalk, messageResult);
        JSONObject jsonObject = JSONObject.parseObject(messageResult.getContent());
        if (messageResult.getStatusCode() == 200) {
            String dealResult = String.valueOf(jsonObject.get("result"));
            if ("true".equals(dealResult)) {
                //状态设为0
                int sum = 0;
                for (DingTaskVo dingTaskVo : dingTaskVos) {
                    if (userIds.contains(dingTaskVo.getEmployeeId())) {
                        Result<Integer> countResult = objectMappingService.updateStatus(0, dealTodoArg.getEi(), dealTodoArg.getSourceId(), dingTaskVo.getEmployeeId());
                        //更换接口后，就算全部人完成了代办，仍然显示为进行中的代办，需要查询数据库，如果状态全部为0的话，证明代办完成了，调用完成接口
                        if(countResult.isSuccess()) {
                            sum += countResult.getData();
                        }
                    }
                }
                List<DingTaskVo> dingTaskVos1 = objectMappingService.getDingTaskVo(dealTodoArg.getEi(), dealTodoArg.getSourceId()).getData();
                if (CollectionUtils.isEmpty(dingTaskVos1)) {
                    //调用完成的接口
                    this.updateTodoStatus(operatorUnionId, dingTaskVos.get(0).getTaskId(), dingTalkToken);
                }
                log.info("DingTalkSendDealTodoMsgHandlerTemplate.buildMsg,ea={},userIds={},sum={}", dealTodoArg.getEa(), userIds, sum);
            }
        }
        //return result;

        context.setResult(TemplateResult.newSuccess());
        context.setData(result);
    }

    @Override
    public void sendMsg(MethodContext context) {
        log.info("DingTalkSendDealTodoMsgHandlerTemplate.sendMsg,context={}",context);

        DealTodoResult result = new DealTodoResult();
        result.setCode(200);
        result.setMessage("发送成功");

        context.setResult(TemplateResult.newSuccess(result));
    }
}
