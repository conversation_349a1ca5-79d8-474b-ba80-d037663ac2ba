package com.facishare.open.ding.cloud.service.impl;

import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.service.cloud.CloudContactsService;
import com.facishare.open.ding.api.service.cloud.CloudEmpService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("cloudContactsServiceImpl")
public class CloudContactsServiceImpl implements CloudContactsService {
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private CloudEmpService cloudEmpService;

    @Override
    public Result<Integer> addUser(Integer ei, String outUserId, String suiteId, DingCorpMappingVo corpMappingVo) {
        //判断是否已经绑定了
        Result<DingMappingEmployeeResult> userResult = objectMappingService.queryEmpByDingUserId(ei, outUserId);
        if (ObjectUtils.isNotEmpty(userResult.getData())) {
            return new Result<>();
        }
        //查询钉钉人员数据，需要返回result

        Result<EmployeeDingVo> empVoResult = dingManager.getUserDetailByUserId(corpMappingVo.getDingCorpId(), outUserId, suiteId);
        if (!empVoResult.isSuccess()) {
            return Result.newError(empVoResult.getErrorCode(), empVoResult.getErrorMessage());
        }
        EmployeeDingVo empVo = empVoResult.getData();
        //创建人员
        //登陆会比较耗时，不设置权限
        return cloudEmpService.cloudCreateEmp(empVo, ei, corpMappingVo.getAppCode(), corpMappingVo.getDingCorpId());
    }
}
