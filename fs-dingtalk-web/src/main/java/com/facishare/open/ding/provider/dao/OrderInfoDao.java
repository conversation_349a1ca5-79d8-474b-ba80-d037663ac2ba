package com.facishare.open.ding.provider.dao;



import com.facishare.open.ding.api.result.OrderInfoResult;
import com.facishare.open.ding.provider.arg.OrderInfoEntity;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/7 10:50
 * @Version 1.0
 */
public interface OrderInfoDao extends ICrudMapper<OrderInfoEntity> {

    @Select("select count(*) from order_info where corp_id=#{dingCorpid}")
    Integer selectCount(@Param("dingCorpid") String dingCorpid);

    @Select("select count(*) from order_info where order_id=#{orderId}")
    Integer countByOrderId(@Param("orderId") Long orderId);

    @Select("select * from order_info where order_id=#{orderId}")
    OrderInfoResult resultByOrderId(@Param("orderId") Long orderId);


    @Select("select * from order_info where corp_id=#{dingCorpId}")
    List<OrderInfoResult> resultByCorpId(@Param("dingCorpId") String dingCorpId);

    @Select("select * from order_info where paid_time between #{startTime} and #{endTime} ")
    List<OrderInfoResult> queryOrderByBetweenTime(@Param("startTime") Date startTime,@Param("endTime") Date endTime);

    @Delete("delete from order_info where order_id=#{orderId} and ea=#{ea}")
    Integer removeOrder(@Param("orderId") Long orderId,@Param("ea") String ea);

    @Select("<script>SELECT * from  order_info WHERE corp_id = #{dingCorpId} " +
            "<if test=\"suiteId!=null and suiteId!=''\"> " +
            "AND suite_id = #{suiteId} " +
            "</if>" +
            "order by paid_time desc limit 1" +
            "</script>")
    OrderInfoResult queryLastOrderByCorpId(@Param("dingCorpId") String dingCorpId,@Param("suiteId") String suiteId);

   }
