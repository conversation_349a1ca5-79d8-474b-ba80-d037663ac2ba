package com.facishare.open.ding.provider.dao;

import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.provider.arg.DingCorpMappingEntity;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/7 16:42
 * @Version 1.0
 */
public interface DingCorpMappingDao extends ICrudMapper<DingCorpMappingEntity> {

    @Insert("<script>" +
            "INSERT IGNORE INTO ding_corp_mapping" +
            "(ei,ea,enterprise_name,ding_corp_id,ding_main_corp_id,is_init,bind_type,status,app_code,extend) values " +
            "(#{corpInfo.ei},#{corpInfo.ea},#{corpInfo.enterpriseName},#{corpInfo.dingCorpId},#{corpInfo.dingMainCorpId},#{corpInfo.isInit},#{corpInfo.bindType}," +
            "#{corpInfo.status},#{corpInfo.appCode},#{corpInfo.extend})" +
            "</script>")
    Integer insertCorpMapping(@Param("corpInfo") DingCorpMappingVo corpInfo);

    @Update("update ding_corp_mapping set repeat_index=repeat_index+1 where ei=#{ei}")
    Integer updateCorpMapping(@Param("ei") Integer ei);

    @Update("update ding_corp_mapping set is_init=1 where ei=#{ei}")
    Integer updateInitStatus(@Param("ei") Integer ei);

    @Select("select * from ding_corp_mapping where ei=#{ei}")
    List<DingCorpMappingVo> queryByEi(@Param("ei") Integer ei);

    @Select("select * from ding_corp_mapping where ea=#{ea}")
    List<DingCorpMappingVo> queryByEa(@Param("ea") String ea);

    @Select("select * from ding_corp_mapping where connector=#{connector} and ei=#{ei} and app_code=#{appCode} limit 1")
    DingCorpMappingVo queryByConnector(@Param("connector") Integer connector,@Param("ei") Integer ei,@Param("appCode") Long appCode);

    @Select("select * from ding_corp_mapping where ea=#{ea} and app_code=#{appCode}")
    DingCorpMappingVo queryByEaAndAppCode(@Param("ea") String ea, @Param("appCode") Long appCode);

    @Update("update ding_corp_mapping set extend=#{extend} where ea=#{ea} and app_code=#{appCode}")
    Integer updateExtend(@Param("ea") String ea, @Param("extend") String extend, @Param("appCode") Long appCode);
}
