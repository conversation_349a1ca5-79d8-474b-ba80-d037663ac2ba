package com.facishare.open.ding.provider.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.service.EnterpriseService;
import com.facishare.open.ding.provider.constants.Constant;
import com.facishare.open.ding.provider.entity.DingEnterprise;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.api.vo.ConnectionVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.common.utils.RSAUtils;
import com.facishare.open.ding.api.utils.HttpRequestUtils;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.GetAllDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.GetAllDepartmentDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.fxiaoke.cloud.DataPersistor;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>类的详细说明</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @dateTime 2019/8/19 10:55
 */
@Slf4j
@Service("enterpriseService")
public class EnterpriseServiceImpl implements EnterpriseService {

    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;

    @Autowired
    private DepartmentProviderService departmentProviderService;

    @ReloadableProperty("callback.url")
    public String CALL_BACK_URL;

    @Override
    public Result<DingEnterpriseResult> queryEnterpriseByEa(String ea) {
        return dingEnterpriseManager.queryEnterpriseByEa(ea);
    }

    @Override
    public Result<DingEnterpriseResult> queryEnterpriseByEi(Integer ei) {
        return dingEnterpriseManager.queryEnterpriseByEi(ei);
    }

    @Override
    public Result<DingEnterpriseResult> queryEnterpriseByCorpId(String corpId) {
        return dingEnterpriseManager.queryEnterpriseByDingCorpId(corpId);
    }

    @Override
    public Result<Integer> startConnect(ConnectionVo vo) {
        if (StringUtils.isEmpty(vo.getAppKey())
                || StringUtils.isEmpty(vo.getAppSecret())) {
            log.warn("startConnect 请确认连接参数.");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        Gson gson = new Gson();
        //验证appKey和appSecret
        String accessToken = DingRequestUtil.getToken(vo.getClientIp(), vo.getAppKey(), vo.getAppSecret());
        if (StringUtils.isEmpty(accessToken)) {
            log.warn("startConnect appKey或appSecret错误.");
            return Result.newError(ResultCode.DING_CONNECT_PARAM_ERROR);
        }

        //保存企业绑定
        DingEnterprise dingEnterprise = new DingEnterprise();
        dingEnterprise.setEa(vo.getEa());
        dingEnterprise.setEi(vo.getEi());
        dingEnterprise.setEnterpriseName(vo.getEnterpriseName());
        dingEnterprise.setDingCorpId(vo.getDingCorpId());
        dingEnterprise.setAgentId(vo.getAgentId());
        dingEnterprise.setAppKey(vo.getAppKey());
        dingEnterprise.setAppSecret(vo.getAppSecret());
        dingEnterprise.setRedirectAppId(vo.getRedirectAppId());
        dingEnterprise.setRedirectAppSecret(vo.getRedirectAppSecret());
        dingEnterprise.setClientIp(vo.getClientIp());
        dingEnterprise.setCreateBy(vo.getEmployeeId());
        dingEnterprise.setUpdateBy(vo.getEmployeeId());
        dingEnterprise.setToken("NyB0KLZ0oEwvh8aRqrz1r67llBFg4MH2fy7rElNYcG2A9z0qurpLAvpmZ4NB8pSJ3wj04El0iM1Uw5+E8uym1IasRaLGPI8ijnoP76Ry2Uyj4tVMyiKblbHTf3T4StbimeArN8ZXhqZZjrsXjAspHOMLVAtKBpeIw2nxPwtdup8=");
        dingEnterprise.setIsCallback(Constant.IS_CALLBACK);
        dingEnterprise.setDevModel(vo.getDevModel());
        dingEnterprise.setAlertStatus(vo.getAlertStatus());
        Result<Integer> saveResult = dingEnterpriseManager.saveEnterprise(dingEnterprise);
        if (Objects.isNull(saveResult) || Objects.isNull(saveResult.getData()) || saveResult.getData() <= 0) {
            log.warn("saveEnterprise failed, saveResult={}.", saveResult);
            return Result.newError(ResultCode.SAVE_ENTERPRISE_ERROR);
        }

        //注册回调接口
        String registUrl = DingRequestUtil.appendCallBackUrl(vo.getClientIp());
        Map<String, Object> registArg = new HashMap<>();
        registArg.put("ei", vo.getEi());
        registArg.put("callBackUrl", CALL_BACK_URL + vo.getEi());
        registArg.put("appKey", vo.getAppKey());
        registArg.put("appSecret", vo.getAppSecret());
        Object registResult = DingRequestUtil.proxyRequest(registUrl, gson.toJson(registArg));
        log.info("registResult:{}", registResult);
        if (Objects.isNull(registResult)) {
            log.warn("startConnect corpId或clientIp错误.");
            //删除绑定关系
            dingEnterpriseManager.deleteEnterprise(dingEnterprise);
            return Result.newError(ResultCode.DING_CORPID_ERROR);
        }
        Result Result = gson.fromJson(registResult.toString(), Result.class);
        //注册回调如果已经存在
        if(Result.getErrorCode()==ResultCode.DING_CALL_BACK_URL_EXIST.getErrorCode()){
            //兼容性设置 如果回调接口已经被注册，那就不再使用回调接口
            //查询是否存在回调接口
            String getCallBackUrl = "https://oapi.dingtalk.com/call_back/get_call_back?access_token="+accessToken;
            String clientUrl = DingRequestUtil.appendUrl(vo.getClientIp());
            Map<String, Object> messageArg = new HashMap<>();
            messageArg.put("type", "GET");
            messageArg.put("url", getCallBackUrl);
            Object callBackResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));
            JSONObject jsonObject = JSONObject.parseObject(callBackResult.toString());
            log.warn("regist call back failed, response={}.", jsonObject);
            //设置回调地址的tag为false
            dingEnterprise.setIsCallback(Constant.IS_NOT_CALLBACK);
            dingEnterpriseManager.updateEnterprise(dingEnterprise);
            return Result.newError(ResultCode.DING_CALL_BACK_URL_EXIST);
        }
        if (Result.getErrorCode() != HttpRequestUtils.DING_SUCCESS) {
            log.warn("regist call back failed, response={}.", Result);
            return Result.newError(ResultCode.DING_REGIST_FAILED);
        }

        // 添加钉钉开通企业数蜂眼埋点
        statisEnterpriseBindAccount(vo);
        return Result.newSuccess(1);
    }

    /**
     * 蜂眼 企业开通已经活跃数统计
     *
     * @param vo
     */
    public void statisEnterpriseBindAccount(ConnectionVo vo) {
        // 蜂眼 企业开通数统计
        try {
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("ei", vo.getEi());
            dataMap.put("ea", vo.getEa());
            dataMap.put("enterpriseName", vo.getEnterpriseName());
            dataMap.put("trace", "BindEnterprise");
            dataMap.put("traceName", "绑定企业");
            DataPersistor.asyncLog("fs-dingtalk-provider", dataMap);
        } catch (Exception e) {
            log.error("trace bindEnterprise datapersist , get exception, ", e);
        }
    }

    public void decrypt(ConnectionVo vo) {
        RSAUtils rsaUtils = new RSAUtils();
        try {
            rsaUtils.loadPrivateKey();
            byte[] cipherDataPri = rsaUtils.strToBase64(vo.getAppSecret());
            String newPassword = new String(rsaUtils.decrypt(rsaUtils.getPrivateKey(), cipherDataPri));
            vo.setAppSecret(newPassword);
        } catch (Exception e) {
            log.error("decrypt error,ei={}", vo.getEmployeeId());
        }
    }

    @Override
    public Result<Integer> updateConnection(ConnectionVo vo) {
        if (Objects.isNull(vo)) {
            log.warn("startConnect param is null,connectionVo=[{}].", vo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        //验证appKey和appSecret
        String accessToken = DingRequestUtil.getToken(vo.getClientIp(), vo.getAppKey(), vo.getAppSecret());
        if (StringUtils.isEmpty(accessToken)) {
            log.warn("startConnect appKey或appSecret错误.");
            return Result.newError(ResultCode.DING_CONNECT_PARAM_ERROR);
        }

        //更新企业绑定
        DingEnterprise dingEnterprise = new DingEnterprise();
        dingEnterprise.setDingCorpId(vo.getDingCorpId());
        dingEnterprise.setAgentId(vo.getAgentId());
        dingEnterprise.setAppKey(vo.getAppKey());
        dingEnterprise.setAppSecret(vo.getAppSecret());
        dingEnterprise.setEa(vo.getEa());
        dingEnterprise.setUpdateBy(vo.getEmployeeId());
        dingEnterprise.setClientIp(vo.getClientIp());
        dingEnterprise.setRedirectAppId(vo.getRedirectAppId());
        dingEnterprise.setRedirectAppSecret(vo.getRedirectAppSecret());
        dingEnterprise.setAlertStatus(vo.getAlertStatus());
        return dingEnterpriseManager.updateEnterprise(dingEnterprise);
    }

    /**
     * 查询企业所有一级部门
     *
     * @param ei
     * @return
     */
    @Override
    public Result<List<Map<String, Object>>> queryDeptLevelOne(Integer ei) {
        GetAllDepartmentDtoArg arg = new GetAllDepartmentDtoArg();
        arg.setEnterpriseId(ei);

        GetAllDepartmentDtoResult result = departmentProviderService.getAllDepartmentDto(arg);
        List<DepartmentDto> depts = result.getDepartments();
        List<Map<String, Object>> deptsList = new ArrayList<>();
        for (DepartmentDto dept : depts) {
            if (dept.getAncestors().size() == 1) {
                Map<String, Object> deptOne = new HashMap<>();
                deptOne.put("name", dept.getName());
                deptOne.put("id", dept.getDepartmentId());
                deptsList.add(deptOne);
            }
        }
        return Result.newSuccess(deptsList);
    }

    @Override
    public Result<Integer> saveOperation(ConnectionVo vo) {
        //保存修改状态的信息
        DingEnterprise enterprise=new DingEnterprise();
        enterprise.setEa(vo.getEa());
        enterprise.setEi(vo.getEi());
        enterprise.setAlertStatus(vo.getAlertStatus());
        Result<Integer> integerResult = dingEnterpriseManager.updateEnterprise(enterprise);
        return integerResult;
    }

}
