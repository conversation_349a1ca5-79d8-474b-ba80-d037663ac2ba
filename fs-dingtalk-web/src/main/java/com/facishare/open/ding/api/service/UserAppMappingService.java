package com.facishare.open.ding.api.service;


import com.facishare.open.ding.api.result.UserAppResult;
import com.facishare.open.ding.api.vo.UserMappingVo;
import com.facishare.open.ding.common.result.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/10 15:40
 * @Version 1.0
 */
public interface UserAppMappingService {

    Result<List<UserAppResult>> queryByDingEmpId(String dingCorpId, Long appId,String dingEmpId);

    Result<List<UserAppResult>> conditionQueryEmp(String dingCorpId, String dingEmpId);

    Result<Integer> insertDingEmp(UserMappingVo userMappingVo);

    Result<List<UserAppResult>> queryMappingByAppId(String dingCorpId,Long appId);

    Result<Integer> removeEmp(String dingCorpId,Long appId,String dingEmpId);

    Result<Integer> fixEmp(Integer empId);
}
