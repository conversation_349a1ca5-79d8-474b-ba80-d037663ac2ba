package service;

import com.facishare.open.ding.api.model.AppParams;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.cloud.CloudContactsService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.common.result.Result;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;

public class CloudContactsServiceTest extends BaseAbstractTest {
    @Autowired
    private CloudContactsService cloudContactsService;
    @Autowired
    private DingCorpMappingService corpMappingService;

    @Test
    public void addUser() {
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get("70480");
        Result<List<DingCorpMappingVo>> listResult = corpMappingService.queryCorpMappingByCorpId("dingfeca3fa3352c7d4ca39a90f97fcb1e09");
        Optional<DingCorpMappingVo> corpMappingVo = listResult.getData().stream()
                .filter(mappingVo -> mappingVo.getAppCode() == Long.parseLong(appParams.getAppId()))
                .findFirst();
        if (corpMappingVo.isPresent()) {
            Result<Integer> result = cloudContactsService.addUser(82379, "016867252917-811826296", "16740006", corpMappingVo.get());
            System.out.println(result);
        }
    }

}
