package service;

import base.BaseAbstractTest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.open.ding.api.enums.BindStatus;
import com.facishare.open.ding.api.enums.BindType;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.result.HttpResponseMessage;
import com.facishare.open.ding.api.service.AppAuthService;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.DingObjSyncService;
import com.facishare.open.ding.api.vo.*;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.crm.CrmRestManager;
import com.facishare.open.ding.provider.crm.CrmUrlUtils;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.facishare.open.ding.provider.model.InnerSearchQueryInfo;
import com.facishare.open.ding.provider.utils.OkHttp3MonitorUtils;
import com.facishare.open.feishu.syncapi.config.GlobalValue;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/2/24 17:41
 * @Version 1.0
 */
@Slf4j
public class CrmRestManagerTest extends BaseAbstractTest {
    @Autowired
    private CrmRestManager crmRestManager;
    @Autowired
    private DingCorpMappingService corpMappingService;

    @Autowired
    private DingCorpMappingService dingCorpMappingService;
    @Autowired
    private AppAuthService appAuthService;
    @Autowired
    private DingMappingEmployeeManager dingMappingEmployeeManager;
    @Autowired
    private DingObjSyncService syncService;


    @Test
    public void testData(){
        DingObjSyncVo dingObjSyncVo=new DingObjSyncVo();
        dingObjSyncVo.setCrmApiName("LeadsObj");
        dingObjSyncVo.setDingApiName("crm_customer_personal");
        dingObjSyncVo.setCrmObjId("");
        dingObjSyncVo.setDingCorpId("1");
        dingObjSyncVo.setTenantId(1);
        dingObjSyncVo.setDingObjId("12111");
        Integer integer = syncService.insertObjSync(dingObjSyncVo);
    }

    @Test
    public void testCorpMapping(){
        /**
         * DingCorpMappingVo(id=null, ei=82379, ea=ddqybhzyl, enterpriseName=深圳市追梦科技, dingCorpId=dinge852faff822b4cdcacaaa37764f94726,
         * dingMainCorpId=null, bindType=0, status=null, appCode=79788, repeatIndex=null, isInit=1, connector=null, category=null)
         */

        DingCorpMappingVo corpMappingVo=new DingCorpMappingVo();
        corpMappingVo.setAppCode(79788L);
        corpMappingVo.setEi(82379);
        corpMappingVo.setDingCorpId("dingfeca3fa3352c7d4ca39a90f97fcb1e09");
        corpMappingVo.setStatus(0);
        corpMappingVo.setIsInit(1);
        corpMappingVo.setEnterpriseName("深圳市追梦科技");
        corpMappingVo.setBindType(BindStatus.ORDER_BIND.getType());
        corpMappingVo.setExtend(GlobalValue.enterprise_extend);
        Result<Integer> result = dingCorpMappingService.insertCorpMapping(corpMappingVo);
        log.info("result");
    }

    @Test
    public void testManager(){
        List<Integer> empIds=Lists.newArrayList();
        empIds.add(1004);
        empIds.add(1005);
        empIds.add(1006);
        empIds.add(1007);
        empIds.add(1008);
        Result<List<DingMappingEmployeeResult>> listResult = dingMappingEmployeeManager.batchGetEmpIds(82379, null);
        Result<Integer> result = dingMappingEmployeeManager.batchUpdateEmpStatus(2, 82568, Lists.newArrayList(1003, 1004));
    }


    @Test
    public void testAppAuth(){
        AppAuthVo appAuthVo=new AppAuthVo();
        appAuthVo.setAgentId(1123L);
        appAuthVo.setAppId(12112L);
        appAuthVo.setAuthInfo("12121211");
        appAuthVo.setDingCorpId("1212112");
        appAuthVo.setEi(12121);
        Result<Integer> result = appAuthService.insertAuth(appAuthVo);
        log.info("result :{}",result);
    }

    @Test
    public void  createDept(){
        DeptVo vo=new DeptVo();
        vo.setEi(84475);
        vo.setName("部门测试001");
        vo.setCrmParentId(1000);
        vo.setCrmDeptOwner(1000);
        Result<Integer> dept = crmRestManager.createDept(vo);

    }

    @Test
    public void  modifyDept(){
        DeptVo vo=new DeptVo();
        vo.setEi(78583);
        vo.setName("手机测试232");
        vo.setCrmDeptId(1015);
        vo.setCrmParentId(1005);
        vo.setCrmDeptOwner(1000);
        Result<Void> voidResult = crmRestManager.modifyDept(vo);
    }

    @Test
    public void  stopDept(){
        DeptVo vo=new DeptVo();
        vo.setEi(78583);
        vo.setName("手机测试12");
        vo.setCrmParentId(1005);
        vo.setCrmDeptOwner(1000);
        vo.setCrmDeptId(1015);
        Result<Void> voidResult = crmRestManager.stopDept(vo);
    }

    @Test
    public void testCreateEmp(){
        CreateCrmEmployeeVo createCrmEmployeeVo=new CreateCrmEmployeeVo();
        createCrmEmployeeVo.setName("柯南颖");
        createCrmEmployeeVo.setMobile("13432858633");
        createCrmEmployeeVo.setCrmMainDeptId("999998");
        createCrmEmployeeVo.setEi(84475);
        Result<Integer> emp = crmRestManager.createEmp(createCrmEmployeeVo);
        log.info("emp:{}",emp);
    }

    @Test
    public void testModifyEmp(){
        CreateCrmEmployeeVo createCrmEmployeeVo=new CreateCrmEmployeeVo();
        createCrmEmployeeVo.setName("王振懿Justice");
        createCrmEmployeeVo.setMobile("13163352321");

        createCrmEmployeeVo.setCrmMainDeptId("1000");
        createCrmEmployeeVo.setEi(83449);
        createCrmEmployeeVo.setCrmEmpId(1000);
        Result<Integer> emp = crmRestManager.modifyEmp(createCrmEmployeeVo);

    }


    @Test
    public void testStopEmp(){

        Result<Integer> emp = crmRestManager.stopEmp(1048,76340);

    }

    @Test
    public void testQueryEmp(){
        Map<String,String> hearsMap= Maps.newHashMap();
        hearsMap.put("x-fs-ei","81961");
        hearsMap.put("x-fs-userinfo","-10000");

        InnerSearchQueryInfo innerSearchQueryInfo = new InnerSearchQueryInfo();
        InnerSearchQueryInfo.Filter orderIdFilter = new InnerSearchQueryInfo.Filter();
        orderIdFilter.setOperator("EQ");
        orderIdFilter.setField_name("tel");
        orderIdFilter.setField_values(Lists.newArrayList("13432858633"));

        List<InnerSearchQueryInfo.Filter> filters = Lists.newArrayList(orderIdFilter);

        innerSearchQueryInfo.setFilters(filters);
        innerSearchQueryInfo.setOffset(0);
        innerSearchQueryInfo.setLimit(100);
        InnerSearchQueryInfo.Order order=new InnerSearchQueryInfo.Order();
        order.setFieldName("last_modified_time");
        order.setAsc(false);
        List<InnerSearchQueryInfo.Order> orders = Lists.newArrayList(order);
        innerSearchQueryInfo.setOrders(orders);

        Map<String, Object> queryMap = Maps.newHashMap();
        Gson gson=new Gson();
        queryMap.put("object_describe_api_name", "ContactObj");
        queryMap.put("search_query_info", gson.toJson(innerSearchQueryInfo));
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(CrmUrlUtils.queryList("/ContactObj"), hearsMap, JSONObject.toJSONString(queryMap));

        Map<String, Object> objectMap = Maps.newHashMap();

        Result<Map<String, Object>> phone = crmRestManager.queryByField(81961, "tel", "13432858633");

    }

    @Test
    public void testQueryDept(){

        Result<String> crmDeptMapResult = crmRestManager.queryCrmObj(84883,"PersonnelObj", "_id", "1000");
        if(crmDeptMapResult.isSuccess()
                && StringUtils.isNotEmpty(crmDeptMapResult.getData())) {
            JSONArray dataList = (JSONArray) JSONPath.read(crmDeptMapResult.getData(), "$.data.dataList");
            if(dataList.size()!=0
                    && ObjectUtils.isNotEmpty(JSONPath.read(crmDeptMapResult.getData(), "$.data.dataList[0].status"))
                    && StringUtils.equalsIgnoreCase("0", JSONPath.read(crmDeptMapResult.getData(), "$.data.dataList[0].status").toString())){
                System.out.println("w");
            }
        }
        System.out.println("b");
    }

    @Test
    public void testQuery(){
        Result<List<DingCorpMappingVo>> result = corpMappingService.queryCorpMappingByCorpId("dingfeca3fa3352c7d4ca39a90f97fcb1e09");
        log.info("result:{}",result);
    }

    @Test
    public void updateEnterpriseExtend(){
        Result<Void> result = corpMappingService.updateEnterpriseExtend("ddqybhzyl", "isFirstLand", true);
        log.info("result:{}",result);
    }

    @Test
    public void queryCorpMappingByCorpId(){
        Result<List<DingCorpMappingVo>> result = dingCorpMappingService.queryCorpMappingByCorpId("dingfeca3fa3352c7d4ca39a90f97fcb1e09");
        System.out.println(result);
    }


}
