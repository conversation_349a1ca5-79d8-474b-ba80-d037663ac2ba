package controller;

import base.BaseAbstractTest;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.ding.api.arg.DingAuthResult;
import com.facishare.open.ding.api.result.DingAppResult;
import com.facishare.open.ding.api.result.OrderInfoResult;
import com.facishare.open.ding.api.service.cloud.CloudOrderService;
import com.facishare.open.ding.api.vo.BindEmpVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.web.constants.ConfigCenter;
import com.facishare.open.ding.web.controller.ObjectMappingController;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

@Slf4j
public class ObjectMappingControllerTest extends BaseAbstractTest {
    @Autowired
    private ObjectMappingController objectMappingController;
    @Autowired
    private CloudOrderService cloudOrderService;

    @Test
    public void testQueryEmpBind(){
        Result<Map<String,Object>> result = objectMappingController.queryEmployeeBind(new BindEmpVo());

//        log.info("result = " + result);
    }

    @Test
    public void queryOrder(){
        OrderInfoResult lastOrder = cloudOrderService.queryLastOrder("dingf4d4d1af9a278874f2c783f7214b6d69",null);
        System.out.println(lastOrder);
    }
    
    @Test
    public void testJson(){
        DingAuthResult dingAuthResult=new DingAuthResult();
        dingAuthResult.setEi(1);
        dingAuthResult.setFsEmpId(100);
        String s = JSONObject.toJSONString(dingAuthResult);
        String message="{\"ei\":1,\"fsEmpId2\":100}";
        DingAuthResult dingAppResult = JSONObject.parseObject(message, new TypeReference<DingAuthResult>() {
        });
        log.info("ding");
    }

    @Test
    public void testUrl(){
        String url="https://www.ceshi112.com/XV/Home/Index#crm/remind/approvalbyme";
        String replace = url.replace("&", "&amp").replace("<", "&lt").replace(">", "&gt").replace("\"", "&quot").replace("\'", "&#x27");
        System.out.println(replace);

    }

    @Test
    public void testAuthUrl(){
        String data="{\"dingmarketing\":[\"https://www.ceshi112.com/XV/UI/Home?noheader=1&noqx=1&source=dingTalk#/app/marketing/index/=/promotion\",\"https://www.ceshi112.com\"]}";
        Map<String, List<String>> corpMap=JSONObject.parseObject(data,new TypeReference<Map>(){});
        log.info("data");
    }

}
