package manager;

import base.BaseAbstractTest;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.entity.DingEnterprise;
import com.facishare.open.ding.provider.manager.DingDeptMananger;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.github.mybatis.pagination.Page;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/7/16 11:05
 */
@Slf4j
public class DingMappingEmployeeManagerTest extends BaseAbstractTest{
    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;
    @Autowired
    private DingMappingEmployeeManager dingMappingEmployeeManager;
    @Autowired
    private DingDeptMananger dingDeptMananger;

    @Test
    public void testSaveEnterprise(){
        DingEnterprise dingEnterprise = new DingEnterprise();
        dingEnterprise.setEa("1");
        dingEnterprise.setEi(1);
        dingEnterprise.setAgentId("asd");
        dingEnterprise.setAppKey("1234");
        dingEnterprise.setDingCorpId("1234QAERFQWE");
        dingEnterpriseManager.saveEnterprise(dingEnterprise);
    }


    @Test
    public void conditionQuery(){
//        Integer ei=78762;
        Result<List<DingMappingEmployeeResult>> listResult = dingMappingEmployeeManager.conditionQueryEmployee(76235, null, 1, 20, "", null);
        System.out.println(listResult.getData());
    }

    @Test
    public void conditionEmployeeCount(){
        Integer count = dingMappingEmployeeManager.conditionEmployeeCount(76235, null, "", null);
        System.out.println(count);
    }


    @Test
    public void testDingMapping(){

        //注册回调接口
        String registUrl = DingRequestUtil.appendCallBackUrl("http://47.113.195.161:8089/");
        Map<String, Object> registArg = new HashMap<>();
        registArg.put("ei", 76246);
        registArg.put("callBackUrl", "https://www.ceshi112.com/dingtalk/business/callback?ei=76246");
        registArg.put("appKey", "dingskqye5ljk4pogfbf");
        registArg.put("appSecret", "D4F0V5a8-HRUvXck3LDtP_AGw7UDQP_D_0k2l8kmttFOURFIYyrJkUcBGvvt_OwL");
        Gson gson=new Gson();
        Object registResult = DingRequestUtil.proxyRequest(registUrl, gson.toJson(registArg));

    }

    @Test
    public void testUpdateModel(){
        DingMappingEmployeeResult result=new DingMappingEmployeeResult();
        result.setEi(76246);
        result.setDingUnionId("DUZk2F8cdNoaetz7FPc2RQiEiE");
        result.setDingEmployeeId("092159206936640993");
        result.setDingDeptId(376323342L);
        Integer integer = dingMappingEmployeeManager.updateModelEmp(result);

    }

}
