package com.facishare.open.huawei.kit.web.service;

import com.facishare.open.feishu.syncapi.entity.AppInfoEntity;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.huawei.kit.web.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;

public class ToolsServiceTest extends BaseTest {
    @Resource
    private ToolsService toolsService;
    @Test
    public void deleteCustomer() {
        Result<Void> result = toolsService.refreshOrderAndCustomer("huaiweitestzy004", "MOCKPERIODYEARNEW");
        System.out.println(result);
    }
}
