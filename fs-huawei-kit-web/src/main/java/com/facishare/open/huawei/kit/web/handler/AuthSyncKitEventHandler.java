package com.facishare.open.huawei.kit.web.handler;

import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.huawei.kit.web.consts.HuaweiProduceConstant;
import com.facishare.open.huawei.kit.web.info.AuthSyncInfo;
import com.facishare.open.huawei.kit.web.result.Result2;
import com.facishare.open.huawei.kit.web.service.EnterpriseBindService;
import com.facishare.open.huawei.kit.web.service.HuaweiContactsService;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 人员同步同步事件处理逻辑
 * <AUTHOR>
 * @date 20241107
 */
@Slf4j
@Component
public class AuthSyncKitEventHandler extends HuaweiEventHandler {
    @Resource
    private EnterpriseBindService enterpriseBindService;
    @Resource
    private HuaweiContactsService huaweiContactsService;
    @Resource
    private RedisDataSource redisDataSource;


    @Override
    public String getSupportEventType() {
        return HuaweiProduceConstant.AUTH_SYNC;
    }

    @Override
    public void handle(KitVerifyTemplateData kitVerifyTemplateData) {
        //同步人员
        log.info("AuthSyncKitEventHandler.handle,kitVerifyTemplateData={}",kitVerifyTemplateData);
        ObjectMapper authSyncObjectMapper = new ObjectMapper();
        AuthSyncInfo authSyncInfo = authSyncObjectMapper.convertValue(kitVerifyTemplateData.getIsvProduceReq(), AuthSyncInfo.class);

        com.facishare.open.feishu.syncapi.result.Result<List<EnterpriseBindEntity>> authSyncEnterpriseBindList = enterpriseBindService.getEnterpriseBindList(kitVerifyTemplateData.getIsvProduceReq().get("tenantId").toString());
        if(CollectionUtils.isEmpty(authSyncEnterpriseBindList.getData())) {
            //先存起来
            setTemRedis(authSyncInfo.getTenantId(), getSupportEventType(), authSyncInfo);
            return;
        }

        huaweiContactsService.syncEmployee(authSyncInfo);
    }

    @Override
    public TemplateResult result(KitVerifyTemplateData kitVerifyTemplateData) {
        return TemplateResult.newSuccess(Result2.newSuccess());
    }

    private void setTemRedis(String outTenantId, String type, Object object) {
        log.info("HuaweiOuterKitTemplate.setTempRedis,outTenantId={},type={},object={}",outTenantId, type, object);
        Gson gson = new Gson();
        String tem = redisDataSource.getRedisClient().get("huawei-kit2-sync-" + outTenantId);
        Map<String, Object> temSyncMap;
        if(StringUtils.isEmpty(tem)) {
            temSyncMap = new HashMap<>();
        } else {
            temSyncMap = gson.fromJson(tem, new TypeToken<Map<String, Object>>() {
            });
        }
        temSyncMap.put(type, object);
        redisDataSource.getRedisClient().set("huawei-kit2-sync-" + outTenantId, gson.toJson(temSyncMap));
        redisDataSource.getRedisClient().expire("huawei-kit2-sync-" + outTenantId, 60 * 10L);
    }
}
