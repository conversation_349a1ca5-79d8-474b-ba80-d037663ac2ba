package com.facishare.open.huawei.kit.web.service;

import com.facishare.open.feishu.syncapi.arg.CreateCustomerAndUpdateMappingArg;
import com.facishare.open.feishu.syncapi.arg.CreateOrderArg;
import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.huawei.kit.web.info.HuaweiOrderInfo;
import com.facishare.open.huawei.kit.web.model.HuaweiOrderDataModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.huawei.kit.web.result.QueryInstanceResult;

import java.util.List;

public interface HuaweiOrderService {
    /**
     * 查询企业绑定关系
     */
    Result<List<EnterpriseBindEntity>> getEnterpriseBindList(String outEa);


    Result<CorpInfoEntity> getCorpInfo(String instanceId);

    /**
     * 查询实例信息
     */
    QueryInstanceResult queryInstance(String outEa);

    Result<HuaweiOrderInfo> saveOrder(HuaweiOrderDataModel huaweiOrderDataModel);


    Result<String> genFsEa(String enterpriseName);

    Result<Void> createCustomerAndUpdateMapping(CreateCustomerAndUpdateMappingArg arg);

    Result<Void> createOrder(CreateOrderArg arg);

    Result<Boolean> isEnterpriseBind(String ea);

    Result<EnterpriseBindEntity> getEnterpriseBind(String ea);

    Result<Void> updateEnterpriseAndAdminMapping(String ea, String adminUserId);

    Result<Void> saveOrderAndAddCrmOrder(HuaweiOrderDataModel huaweiOrderDataModel);
}
