package com.facishare.open.huawei.kit.web.controller.outer;

import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.huawei.kit.web.handler.HuaweiEventHandlerManager;
import com.facishare.open.huawei.kit.web.result.Result;
import com.facishare.open.huawei.kit.web.service.ServiceAuthService;
import com.facishare.open.huawei.kit.web.service.ToolsService;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(value="/huaweikit2/tools")
public class HuaweiToolsController {
    @Resource
    private HuaweiEventHandlerManager huaweiEventHandlerManager;

    @Resource
    private ServiceAuthService serviceAuthService;
    @Resource
    private ToolsService toolsService;


    @PostMapping(value = "reSyncEvent")
    @ResponseBody
    public Result reSyncEvent(@RequestBody KitVerifyTemplateData kitVerifyTemplateData) {
        TemplateResult templateResult = huaweiEventHandlerManager.handle(kitVerifyTemplateData.getType(), kitVerifyTemplateData);
        return (Result) templateResult.getData();
    }

    @RequestMapping(value = "/updateData",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.feishu.syncapi.result.Result<Integer> updateData(@RequestBody String data) {
        return serviceAuthService.updateData(new String(Base64.decodeBase64(data.getBytes())));
    }

    @RequestMapping(value = "/refreshOrderAndCustomer",method =RequestMethod.GET)
    @ResponseBody
    public com.facishare.open.feishu.syncapi.result.Result<Void> refreshOrderAndCustomer(@RequestParam("instanceId") String instanceId, @RequestParam("orderId") String orderId) {
        return toolsService.refreshOrderAndCustomer(instanceId, orderId);
    }
}