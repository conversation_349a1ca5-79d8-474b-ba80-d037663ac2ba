package com.facishare.open.huawei.kit.web.handler;

import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.huawei.kit.web.consts.HuaweiProduceConstant;
import com.facishare.open.huawei.kit.web.result.Result2;
import com.facishare.open.huawei.kit.web.service.CorpService;
import com.facishare.open.huawei.kit.web.service.EnterpriseBindService;
import com.facishare.open.huawei.kit.web.service.HuaweiOrderService;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import com.facishare.open.huawei.kit.web.template.outer.event.order.HuaweiOpenEnterpriseHandlerTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 租户同步同步事件处理逻辑
 * <AUTHOR>
 * @date 20241107
 */
@Slf4j
@Component
public class TenantSyncKitEventHandler extends HuaweiEventHandler {

    @Resource
    private HuaweiOrderService huaweiOrderService;
    @Resource
    private CorpService corpService;


    @Override
    public String getSupportEventType() {
        return HuaweiProduceConstant.TENANT_SYNC;
    }

    @Override
    public void handle(KitVerifyTemplateData kitVerifyTemplateData) {
        Result<CorpInfoEntity> infoEntityResult = huaweiOrderService.getCorpInfo(kitVerifyTemplateData.getIsvProduceReq().get("instanceId").toString());
        if(ObjectUtils.isEmpty(infoEntityResult.getData())) {
            CorpInfoEntity corpInfoEntity = CorpInfoEntity
                    .builder()
                    .displayId(kitVerifyTemplateData.getIsvProduceReq().get("instanceId").toString())
                    .tenantKey(kitVerifyTemplateData.getIsvProduceReq().get("tenantId").toString())
                    .tenantName(kitVerifyTemplateData.getIsvProduceReq().get("name").toString())
                    .tenantTag(0)
                    .cancellation(0)
                    .domainName(kitVerifyTemplateData.getIsvProduceReq().get("domainName").toString())
                    .build();
            Result<Void> result = corpService.insertCorpInfo(corpInfoEntity);
            log.info("TenantSyncKitEventHandler.handle,corpInfoEntity={},result={}",corpInfoEntity,result);
        } else {
            CorpInfoEntity corpInfoEntity = infoEntityResult.getData();
            corpInfoEntity.setTenantName(kitVerifyTemplateData.getIsvProduceReq().get("name").toString());
            corpInfoEntity.setDomainName(kitVerifyTemplateData.getIsvProduceReq().get("domainName").toString());
            corpInfoEntity.setCancellation(0);
            Result<Void> result = corpService.insertCorpInfo(corpInfoEntity);
            log.info("TenantSyncKitEventHandler.handle,corpInfoEntity={},result={}",corpInfoEntity,result);
        }
    }

    @Override
    public TemplateResult result(KitVerifyTemplateData kitVerifyTemplateData) {
        return TemplateResult.newSuccess(Result2.newSuccess());
    }
}
