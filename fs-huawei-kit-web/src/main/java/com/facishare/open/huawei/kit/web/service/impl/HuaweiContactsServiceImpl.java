package com.facishare.open.huawei.kit.web.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.huawei.kit.web.manager.DepartmentBindManager;
import com.facishare.open.huawei.kit.web.manager.EmployeeBindManager;
import com.facishare.open.huawei.kit.web.manager.EnterpriseBindManager;
import com.facishare.open.feishu.syncapi.entity.DepartmentBindEntity;
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.enums.BindStatusEnum;
import com.facishare.open.feishu.syncapi.enums.BindTypeEnum;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.huawei.kit.web.info.AllOrgSyncInfo;
import com.facishare.open.huawei.kit.web.info.AuthSyncInfo;
import com.facishare.open.huawei.kit.web.info.SingleOrgSyncInfo;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.huawei.kit.web.service.HuaweiContactsService;
import com.facishare.open.order.contacts.proxy.api.arg.FsDeptArg;
import com.facishare.open.order.contacts.proxy.api.arg.FsEmpArg;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.api.enums.FsEmployeeRoleCodeEnum;
import com.facishare.open.order.contacts.proxy.api.service.FsContactsServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsDepartmentServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("huaweiContactsService")
public class HuaweiContactsServiceImpl implements HuaweiContactsService {
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Resource
    private EmployeeBindManager employeeBindManager;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private DepartmentBindManager departmentBindManager;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Resource
    private FsDepartmentServiceProxy fsDepartmentServiceProxy;
    @Resource
    private FsContactsServiceProxy fsContactsServiceProxy;

    @Override
    public Result<Void> syncEmployee(AuthSyncInfo authSyncInfo) {
        //根据类型处理
        if(authSyncInfo.getFlag() == 1 || authSyncInfo.getFlag() == 2) {
            createOrUpdateEmployee(authSyncInfo);
        } else if(authSyncInfo.getFlag() == 0 || authSyncInfo.getFlag() == 3) {
            stopEmployee(authSyncInfo);
        }
        return Result.newSuccess();
    }

    private Result<Void> createOrUpdateEmployee(AuthSyncInfo authSyncInfo) {
        String outEa = authSyncInfo.getTenantId();
        //查询企业绑定关系
        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(outEa);
        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        String fsEa = enterpriseBindList.get(0).getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        for(AuthSyncInfo.User user : authSyncInfo.getUserList()) {
            String outUserId = user.getUserName();
            //查询人员绑定关系
            EmployeeBindEntity employeeBindManagerEntity = employeeBindManager.getEntity(outEa, outUserId, fsEa);
            if(ObjectUtils.isEmpty(employeeBindManagerEntity)) {
                //新增
                //员工不存在，新增员工并更新员工绑定表
                String sex = "M";

                List<String> fsDepIdList = getFsDepIdList(outEa, Lists.newArrayList(user.getOrgCode()));
                List<String> mainDepIdList = CollectionUtils.isEmpty(fsDepIdList) ?
                        Lists.newArrayList(GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"") : fsDepIdList;

                FsEmpArg arg = FsEmpArg.builder()
                        .ei(ei + "")
                        .name(user.getName())
                        .fullName(user.getName())
                        .sex(sex)
                        .phone(user.getMobile())
                        .mainDepartment(mainDepIdList)
                        .status("0")
                        .isActive(true)
                        .build();
                com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result = fsEmployeeServiceProxy.create(arg,
                        Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                        FsEmployeeRoleCodeEnum.SALES.getRoleCode());
                if (result.isSuccess()) {
                    EmployeeBindEntity addEmployeeBindEntity = EmployeeBindEntity.builder()
                            .channel(ChannelEnum.huawei)
                            .fsEa(fsEa)
                            .fsUserId(result.getData().getId())
                            .outEa(outEa)
                            .outUserId(user.getUserName())
                            .bindStatus(BindStatusEnum.normal)
                            .bindType(BindTypeEnum.auto)
                            .build();
                    int count = employeeBindManager.insert(addEmployeeBindEntity);
                    LogUtils.info("HuaweiContactsServiceImpl.addUser,insert employee mapping,count={}", count);
                } else {
                    //如果手机同号则自动匹配。
                if (result.getCode() == 46) {
                    //通过手机号搜索员工
                    com.facishare.open.order.contacts.proxy.api.result.Result<EmployeeDto> mobileResult = fsEmployeeServiceProxy.getEmployeesDtoByEnterpriseAndMobile(ei, user.getMobile());
                    if (mobileResult.isSuccess()) {
                        //修改员工
                        FsEmpArg fsEmpArg = FsEmpArg.builder()
                        .ei(ei + "")
                        .id(mobileResult.getData().getDataId())
                        .status("0")
                        .build();
                        com.facishare.open.order.contacts.proxy.api.result.Result<Void> updateResult = fsEmployeeServiceProxy.update(fsEmpArg, null, null);
                        LogUtils.info("HuaweiContactsServiceImpl.addUser,updateResult={}", updateResult);
                        if(updateResult.isSuccess()) {
                            EmployeeBindEntity addEmployeeBindEntity = EmployeeBindEntity.builder()
                           .channel(ChannelEnum.huawei)
                           .fsEa(fsEa)
                           .fsUserId(mobileResult.getData().getDataId())
                           .outEa(outEa)
                           .outUserId(user.getUserName())
                           .bindStatus(BindStatusEnum.normal)
                           .bindType(BindTypeEnum.auto)
                           .build();
                           int count = employeeBindManager.insert(addEmployeeBindEntity);
                           LogUtils.info("HuaweiContactsServiceImpl.addUser,insert employee mapping2,count={}", count);
                           return Result.newSuccess();
                        }
                    }
                }
                    return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
                }
            } else {
                //更新
                com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.toggle(ei + "",
                        employeeBindManagerEntity.getFsUserId(),
                        true,
                        Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                        FsEmployeeRoleCodeEnum.SALES.getRoleCode());
                LogUtils.info("HuaweiContactsServiceImpl.addUserList,result={}", result);
                employeeBindManager.batchUpdateBindStatus(fsEa, Lists.newArrayList(employeeBindManagerEntity.getFsUserId()), BindStatusEnum.normal,outEa);
            }
        }
        return Result.newSuccess();
    }

    private List<String> getFsDepIdList(String outEa, List<String> outDepIdList) {
        List<String> fsDepList = new ArrayList<>();
        for (String depId : outDepIdList) {
            //飞书根部门对应纷享根部门
            if (StringUtils.equalsIgnoreCase(depId, "0")) {
                fsDepList.add(GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "");
                continue;
            }
            DepartmentBindEntity entityByOutEa = departmentBindManager.getEntityByOutEa(outEa, depId);
            if (entityByOutEa == null) {
                continue;
            }
            fsDepList.add(entityByOutEa.getFsDepId());
        }
        LogUtils.info("HuaweiContactsServiceImpl.getFsDepIdList,fsDepList={}", fsDepList);

        return fsDepList;
    }

    private Result<Void> stopEmployee(AuthSyncInfo authSyncInfo) {
        String outEa = authSyncInfo.getTenantId();
        //查询企业绑定关系
        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(outEa);
        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        String fsEa = enterpriseBindList.get(0).getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        for(AuthSyncInfo.User user : authSyncInfo.getUserList()) {
            String outUserId = user.getUserName();
            //查询人员绑定关系
            EmployeeBindEntity employeeBindManagerEntity = employeeBindManager.getEntity(outEa, outUserId, fsEa);
            if (ObjectUtils.isNotEmpty(employeeBindManagerEntity)) {
                com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.toggle(ei + "",
                        employeeBindManagerEntity.getFsUserId(),
                        false,
                        Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                        FsEmployeeRoleCodeEnum.SALES.getRoleCode());
                LogUtils.info("HuaweiContactsServiceImpl.addUserList,result={}", result);
                employeeBindManager.batchUpdateBindStatus(fsEa, Lists.newArrayList(employeeBindManagerEntity.getFsUserId()), BindStatusEnum.stop, outEa);
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> syncDepartment(SingleOrgSyncInfo singleOrgSyncInfo) {
        if(singleOrgSyncInfo.getFlag() == 1 || singleOrgSyncInfo.getFlag() == 2) {
            AllOrgSyncInfo.OrgInfo orgInfo = new AllOrgSyncInfo.OrgInfo();
            orgInfo.setOrgCode(singleOrgSyncInfo.getOrgCode());
            orgInfo.setOrgName(singleOrgSyncInfo.getOrgName());
            orgInfo.setParentCode(singleOrgSyncInfo.getParentCode());
            syncDepartment2Crm(singleOrgSyncInfo.getTenantId(), Lists.newArrayList(orgInfo));
        } else if(singleOrgSyncInfo.getFlag() == 0) {
            //查询企业绑定关系
            List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(singleOrgSyncInfo.getTenantId());
            if(CollectionUtils.isEmpty(enterpriseBindList)) {
                return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
            }
            String fsEa = enterpriseBindList.get(0).getFsEa();
            int ei = eieaConverter.enterpriseAccountToId(fsEa);

            DepartmentBindEntity departmentBindEntity = departmentBindManager.getEntityByOutEa(singleOrgSyncInfo.getTenantId(), singleOrgSyncInfo.getOrgCode());
            if(ObjectUtils.isNotEmpty(departmentBindEntity)) {
                com.facishare.open.order.contacts.proxy.api.result.Result<List<String>> batchStopFsDep = fsContactsServiceProxy.batchStopFsDep(ei, fsEa, departmentBindEntity.getFsDepId());
                //2.批量更新部门绑定状态为停用
                if(batchStopFsDep.isSuccess()) {
                    departmentBindManager.batchUpdateBindStatus(fsEa, batchStopFsDep.getData(), BindStatusEnum.stop, singleOrgSyncInfo.getTenantId());
                }
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> syncAllDepartment(AllOrgSyncInfo allOrgSyncInfo) {
        return syncDepartment2Crm(allOrgSyncInfo.getTenantId(), allOrgSyncInfo.getOrgInfoList());
    }

    private Result<Void> syncDepartment2Crm(String outEa, List<AllOrgSyncInfo.OrgInfo> orgInfoList) {
        //查询企业绑定关系
        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(outEa);
        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        String fsEa = enterpriseBindList.get(0).getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        for(AllOrgSyncInfo.OrgInfo orgInfo : orgInfoList) {
            //查询部门绑定关系
            DepartmentBindEntity departmentBindEntity = departmentBindManager.getEntityByOutEa(outEa, orgInfo.getOrgCode());
            String fsDepartmentId = GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "";
            if(StringUtils.isNotEmpty(orgInfo.getParentCode())) {
                DepartmentBindEntity parentDepartmentBindEntity = departmentBindManager.getEntityByOutEa(outEa, orgInfo.getParentCode());
                if(ObjectUtils.isNotEmpty(parentDepartmentBindEntity)) {
                    fsDepartmentId = parentDepartmentBindEntity.getFsDepId();
                }
            }
            if(ObjectUtils.isEmpty(departmentBindEntity)) {
                //新增
                FsDeptArg arg = FsDeptArg.builder()
                        .ei(ei + "")
                        .name(orgInfo.getOrgName())
                        .code(orgInfo.getOrgCode())
                        .status("0")
                        .parentId(Lists.newArrayList(fsDepartmentId))
                        .build();
                LogUtils.info("HuaweiContactsServiceImpl.initDepList,createArg={}", arg);
                //3.创建纷享部门
                com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> createDepResult = fsDepartmentServiceProxy.create(arg);
                LogUtils.info("HuaweiContactsServiceImpl.initDepList,createDepResult={}", createDepResult);
                if (createDepResult.isSuccess() == false) {
                    LogUtils.info("HuaweiContactsServiceImpl.initDepList,create dep failed,break,createArg={}", arg);
                    return Result.newSuccess();
                }
                DepartmentBindEntity newDepartmentBindEntity = DepartmentBindEntity.builder()
                        .channel(ChannelEnum.huawei)
                        .fsEa(fsEa)
                        .fsDepId(createDepResult.getData().getId())
                        .outEa(outEa)
                        .outDepId(orgInfo.getOrgCode())
                        .bindType(BindTypeEnum.auto)
                        .bindStatus(BindStatusEnum.normal)
                        .depCode(orgInfo.getOrgCode())
                        .build();
                //4.插入部门绑定表数据
                int count = departmentBindManager.insert(newDepartmentBindEntity);
                LogUtils.info("HuaweiContactsServiceImpl.initDepList,insert department mapping,count={}", count);
            } else {
                //更新
                com.facishare.open.order.contacts.proxy.api.result.Result<List<String>> batchResumeFsDep = fsContactsServiceProxy.batchResumeFsDep(ei, fsEa, Lists.newArrayList(departmentBindEntity.getFsDepId()));

                //2.批量更新部门绑定状态为正常
                if(batchResumeFsDep.isSuccess()) {
                    departmentBindManager.batchUpdateBindStatus(fsEa, batchResumeFsDep.getData(), BindStatusEnum.normal,outEa);
                }
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> createEmployee(String tenantId, AuthSyncInfo.User user) {
        String outEa = tenantId;
        //查询企业绑定关系
        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(outEa);
        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        String fsEa = enterpriseBindList.get(0).getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        String outUserId = user.getUserName();
        //查询人员绑定关系
        EmployeeBindEntity employeeBindManagerEntity = employeeBindManager.getEntity(outEa, outUserId, fsEa);
        if(ObjectUtils.isEmpty(employeeBindManagerEntity)) {
            //新增
            //员工不存在，新增员工并更新员工绑定表
            String sex = "M";

            List<String> fsDepIdList = getFsDepIdList(outEa, Lists.newArrayList(user.getOrgCode()));
            List<String> mainDepIdList = CollectionUtils.isEmpty(fsDepIdList) ?
                    Lists.newArrayList(GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "") : fsDepIdList;

            FsEmpArg arg = FsEmpArg.builder()
                    .ei(ei + "")
                    .name(user.getName())
                    .fullName(user.getName())
                    .sex(sex)
                    .phone(user.getMobile())
                    .mainDepartment(mainDepIdList)
                    .status("0")
                    .isActive(true)
                    .build();
            com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result = fsEmployeeServiceProxy.create(arg,
                    Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                    FsEmployeeRoleCodeEnum.SALES.getRoleCode());
            LogUtils.info("HuaweiContactsServiceImpl.addUser,result={}", result);
            if (result.isSuccess()) {
                EmployeeBindEntity addEmployeeBindEntity = EmployeeBindEntity.builder()
                        .channel(ChannelEnum.huawei)
                        .fsEa(fsEa)
                        .fsUserId(result.getData().getId())
                        .outEa(outEa)
                        .outUserId(user.getUserName())
                        .bindStatus(BindStatusEnum.normal)
                        .bindType(BindTypeEnum.auto)
                        .build();
                int count = employeeBindManager.insert(addEmployeeBindEntity);
                LogUtils.info("HuaweiContactsServiceImpl.addUser,insert employee mapping,count={}", count);
            } else {
                //如果手机同号则自动匹配。
                if (result.getCode() == 46) {
                    //通过手机号搜索员工
                    com.facishare.open.order.contacts.proxy.api.result.Result<EmployeeDto> mobileResult = fsEmployeeServiceProxy.getEmployeesDtoByEnterpriseAndMobile(ei, user.getMobile());
                    if (mobileResult.isSuccess()) {
                        //修改员工
                        FsEmpArg fsEmpArg = FsEmpArg.builder()
                        .ei(ei + "")
                        .id(mobileResult.getData().getDataId())
                        .status("0")
                        .build();
                        com.facishare.open.order.contacts.proxy.api.result.Result<Void> updateResult = fsEmployeeServiceProxy.update(fsEmpArg, null, null);
                        LogUtils.info("HuaweiContactsServiceImpl.addUser,updateResult={}", updateResult);
                        if(updateResult.isSuccess()) {
                            EmployeeBindEntity addEmployeeBindEntity = EmployeeBindEntity.builder()
                           .channel(ChannelEnum.huawei)
                           .fsEa(fsEa)
                           .fsUserId(mobileResult.getData().getDataId())
                           .outEa(outEa)
                           .outUserId(user.getUserName())
                           .bindStatus(BindStatusEnum.normal)
                           .bindType(BindTypeEnum.auto)
                           .build();
                           int count = employeeBindManager.insert(addEmployeeBindEntity);
                           LogUtils.info("HuaweiContactsServiceImpl.addUser,insert employee mapping2,count={}", count);
                           return Result.newSuccess();
                        }
                    }
                }
                return Result.newError(ResultCodeEnum.getCodeByOutCode(String.valueOf(result.getCode()), ResultCodeEnum.CRM_USER_ACCOUNT_CREATE_ERROR.getCode()), result.getMsg());
            }
        }
        return Result.newSuccess();
    }
}
