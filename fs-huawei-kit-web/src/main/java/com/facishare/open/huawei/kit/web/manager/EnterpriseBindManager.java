package com.facishare.open.huawei.kit.web.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.facishare.open.huawei.kit.web.mapper.EnterpriseBindMapper;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.config.GlobalValue;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.enums.BindStatusEnum;
import com.facishare.open.feishu.syncapi.enums.BindTypeEnum;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * tb_enterprise_bind表管理器类
 * <AUTHOR>
 * @date 20220722
 */
@Component
public class EnterpriseBindManager {
    @Resource
    private EnterpriseBindMapper enterpriseBindMapper;

    public int insert(EnterpriseBindEntity entity) {
        int count = enterpriseBindMapper.insert(entity);
        LogUtils.info("EnterpriseBindManager.insert,count={}",count);
        return count;
    }

    public int insert(ChannelEnum channel,
                      String fsEa,
                      String outEa,
                      String domain,
                      BindTypeEnum bindType,
                      BindStatusEnum bindStatus) {
        EnterpriseBindEntity entity = EnterpriseBindEntity.builder()
                .channel(channel)
                .fsEa(fsEa)
                .outEa(outEa)
                .domain(domain)
                .bindType(bindType)
                .bindStatus(bindStatus)
                .extend(GlobalValue.enterprise_extend)
                .build();
        return insert(entity);
    }

    public int updateById(EnterpriseBindEntity entity) {
        int count = enterpriseBindMapper.updateById(entity);
        LogUtils.info("EnterpriseBindManager.updateById,count={}",count);
        return count;
    }

    public int updateBindStatus(String fsEa,
                                String outEa,
                                BindStatusEnum bindStatus) {
        EnterpriseBindEntity entity = EnterpriseBindEntity.builder()
                .bindStatus(bindStatus)
                .build();

        LambdaUpdateWrapper<EnterpriseBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getFsEa,fsEa);
        if(StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(EnterpriseBindEntity::getOutEa,outEa);
        }
        int count = enterpriseBindMapper.update(entity, wrapper);
        LogUtils.info("EnterpriseBindManager.updateBindStatus,count={}",count);
        return count;
    }

    public List<EnterpriseBindEntity> getEnterpriseBindList(String outEa) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getOutEa, outEa);
        wrapper.eq(EnterpriseBindEntity::getDomain, ConfigCenter.crm_domain);

        return enterpriseBindMapper.selectList(wrapper);
    }

    public List<EnterpriseBindEntity> getAllEnterpriseBindList(String outEa) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getOutEa, outEa);

        return enterpriseBindMapper.selectList(wrapper);
    }

    public EnterpriseBindEntity getEntity(String fsEa) {
        return getEntity(ChannelEnum.feishu,fsEa,null);
    }

    public EnterpriseBindEntity getEntity(String fsEa, String outEa) {
        return getEntity(ChannelEnum.feishu,fsEa,outEa,null,null);
    }

    public EnterpriseBindEntity getEntity(String fsEa,BindStatusEnum bindStatus) {
        return getEntity(ChannelEnum.feishu,fsEa,bindStatus);
    }

    public EnterpriseBindEntity getEntity(String fsEa,BindStatusEnum bindStatus,BindTypeEnum bindType) {
        return getEntity(ChannelEnum.feishu,fsEa,bindStatus,bindType);
    }

    public EnterpriseBindEntity getEntityByOutEa(String outEa,BindStatusEnum bindStatus,BindTypeEnum bindType) {
        return getEntity(ChannelEnum.feishu,null,outEa,bindStatus,bindType);
    }

    public EnterpriseBindEntity getEntity(String fsEa, String outEa, BindStatusEnum bindStatus) {
        return getEntity(ChannelEnum.feishu,fsEa,outEa,bindStatus,null);
    }

    public EnterpriseBindEntity getEntity(ChannelEnum channel,String fsEa,BindStatusEnum bindStatus) {
        return getEntity(channel,fsEa,null,bindStatus,null);
    }

    public EnterpriseBindEntity getEntity(ChannelEnum channel,String fsEa,BindStatusEnum bindStatus,BindTypeEnum bindType) {
        return getEntity(channel,fsEa,null,bindStatus,bindType);
    }

    public EnterpriseBindEntity getEntity(ChannelEnum channel,String fsEa,String outEa,BindStatusEnum bindStatus,BindTypeEnum bindType) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getChannel,channel);
        if(StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(EnterpriseBindEntity::getFsEa,fsEa);
        }
        if(StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(EnterpriseBindEntity::getOutEa, outEa);
        }
        wrapper.eq(EnterpriseBindEntity::getDomain, ConfigCenter.crm_domain);
        if(bindStatus!=null) {
            wrapper.eq(EnterpriseBindEntity::getBindStatus,bindStatus);
        }

        if(bindType!=null) {
            wrapper.eq(EnterpriseBindEntity::getBindType,bindType);
        }

        return enterpriseBindMapper.selectOne(wrapper);
    }

    public List<EnterpriseBindEntity> getEntityList(String fsEa,BindStatusEnum bindStatus) {
        return getEntityList(ChannelEnum.feishu, fsEa, bindStatus);
    }

    public List<EnterpriseBindEntity> getEntityList(ChannelEnum channel,String fsEa,BindStatusEnum bindStatus) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getChannel,channel);
        wrapper.eq(EnterpriseBindEntity::getFsEa,fsEa);
        wrapper.eq(EnterpriseBindEntity::getDomain, ConfigCenter.crm_domain);
        if(bindStatus!=null) {
            wrapper.eq(EnterpriseBindEntity::getBindStatus,bindStatus);
        }

        return enterpriseBindMapper.selectList(wrapper);
    }

    public EnterpriseBindEntity getEntity(EnterpriseBindEntity entity) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getChannel, entity.getChannel());
        wrapper.eq(EnterpriseBindEntity::getDomain, ConfigCenter.crm_domain);
        if(StringUtils.isNotEmpty(entity.getFsEa())) {
            wrapper.eq(EnterpriseBindEntity::getFsEa, entity.getFsEa());
        }
        if(StringUtils.isNotEmpty(entity.getOutEa())) {
            wrapper.eq(EnterpriseBindEntity::getOutEa, entity.getOutEa());
        }
        if(StringUtils.isNotEmpty(entity.getAppKey())) {
            wrapper.eq(EnterpriseBindEntity::getAppKey, entity.getAppKey());
        }
        if(StringUtils.isNotEmpty(entity.getAccessKey())) {
            wrapper.eq(EnterpriseBindEntity::getAccessKey, entity.getAccessKey());
        }
        if(StringUtils.isNotEmpty(entity.getAccessSecret())) {
            wrapper.eq(EnterpriseBindEntity::getAccessSecret, entity.getAccessSecret());
        }
        if(entity.getBindType() != null) {
            wrapper.eq(EnterpriseBindEntity::getBindType, entity.getBindType());
        }
        if(entity.getBindStatus() != null) {
            wrapper.eq(EnterpriseBindEntity::getBindStatus, entity.getBindStatus());
        }

        return enterpriseBindMapper.selectOne(wrapper);
    }

    /**
     * 删除已绑定的企业数据
     *
     * @param fsEa
     * @return
     */
    public int deleteByFsEa(String fsEa, String outEa) {
        LambdaUpdateWrapper<EnterpriseBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getFsEa, fsEa);
        wrapper.eq(EnterpriseBindEntity::getOutEa, outEa);
        wrapper.eq(EnterpriseBindEntity::getDomain, ConfigCenter.crm_domain);
        int count = enterpriseBindMapper.delete(wrapper);
        LogUtils.info("EnterpriseBindManager.deleteByFsEa,count={}", count);
        return count;
    }

    /**
     * 只删除当前域名下的企业绑定关系
     * @param fsEa
     * @param outEa
     * @param domain
     * @return
     */
    public int deleteByFsEa(String fsEa, String outEa, String domain) {
        LambdaUpdateWrapper<EnterpriseBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getFsEa, fsEa);
        wrapper.eq(EnterpriseBindEntity::getOutEa, outEa);
        wrapper.eq(EnterpriseBindEntity::getDomain, domain);
        int count = enterpriseBindMapper.delete(wrapper);
        LogUtils.info("EnterpriseBindManager.deleteByFsEa,domain={},count={}", domain, count);
        return count;
    }

    /**
     * 更新企业绑定扩展字段
     * @param fsEa
     * @param extend
     * @return
     */
    public int updateExtend(String fsEa, String outEa, String extend) {
        EnterpriseBindEntity entity = EnterpriseBindEntity.builder()
                .extend(extend)
                .build();

        LambdaUpdateWrapper<EnterpriseBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getFsEa,fsEa);
        if(StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(EnterpriseBindEntity::getOutEa,outEa);
        }
        wrapper.eq(EnterpriseBindEntity::getDomain, ConfigCenter.crm_domain);
        int count = enterpriseBindMapper.update(entity, wrapper);
        LogUtils.info("EnterpriseBindManager.updateExtend,count={}",count);
        return count;
    }

    public List<EnterpriseBindEntity> getEnterpriseBindListByBindType(BindTypeEnum bindType) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getBindType,bindType);
        wrapper.eq(EnterpriseBindEntity::getDomain, ConfigCenter.crm_domain);

        return enterpriseBindMapper.selectList(wrapper);
    }

    public List<EnterpriseBindEntity> getEnterpriseBindListByBindStatus(BindStatusEnum bindStatus) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getBindStatus, bindStatus);
        wrapper.eq(EnterpriseBindEntity::getDomain, ConfigCenter.crm_domain);

        return enterpriseBindMapper.selectList(wrapper);
    }

    public Integer update(EnterpriseBindEntity entity) {
        int count = enterpriseBindMapper.updateById(entity);
        LogUtils.info("EnterpriseBindManager.update,count={}",count);
        return count;
    }

    public EnterpriseBindEntity getEnterpriseBind(String outEa, String fsEa, String domain) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getOutEa, outEa);
        wrapper.eq(EnterpriseBindEntity::getFsEa, fsEa);
        if(StringUtils.isNotEmpty(domain)) {
            wrapper.eq(EnterpriseBindEntity::getDomain, domain);
        }

        return enterpriseBindMapper.selectOne(wrapper);
    }

    public List<EnterpriseBindEntity> getEntityListByFsEaNotChannel(String fsEa,BindStatusEnum bindStatus) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getFsEa,fsEa);
        wrapper.eq(EnterpriseBindEntity::getDomain, ConfigCenter.crm_domain);
        if(bindStatus!=null) {
            wrapper.eq(EnterpriseBindEntity::getBindStatus,bindStatus);
        }

        return enterpriseBindMapper.selectList(wrapper);
    }
}
