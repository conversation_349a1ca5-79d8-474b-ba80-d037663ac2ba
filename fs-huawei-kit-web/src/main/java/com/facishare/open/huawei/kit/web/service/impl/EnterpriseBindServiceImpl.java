package com.facishare.open.huawei.kit.web.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.syncapi.entity.*;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.huawei.kit.web.service.EmployeeBindService;
import com.facishare.open.huawei.kit.web.service.EnterpriseBindService;
import com.facishare.open.huawei.kit.web.manager.EnterpriseBindManager;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.facishare.uc.api.service.EnterpriseEditionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service("enterpriseBindService")
public class EnterpriseBindServiceImpl implements EnterpriseBindService {
    @Resource
    private EnterpriseEditionService enterpriseEditionService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private I18NStringManager i18NStringManager;

    @Override
    public Result<List<EnterpriseBindEntity>> getEnterpriseBindList(String outEa) {
        return Result.newSuccess(enterpriseBindManager.getEnterpriseBindList(outEa));
    }

    @Override
    public Result<EnterpriseBindEntity> getEntity(String fsEa) {
        return Result.newSuccess(enterpriseBindManager.getEntity(fsEa));
    }
}