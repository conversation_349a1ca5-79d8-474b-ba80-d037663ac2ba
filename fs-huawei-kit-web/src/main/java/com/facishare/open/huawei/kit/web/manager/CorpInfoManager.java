package com.facishare.open.huawei.kit.web.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.huawei.kit.web.mapper.CorpInfoMapper;
import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Component
public class CorpInfoManager {
    @Resource
    private CorpInfoMapper corpInfoMapper;

    public CorpInfoEntity getEntityByTenantKey(String tenantKey) {
        LambdaQueryWrapper<CorpInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CorpInfoEntity::getTenantKey,tenantKey);

        CorpInfoEntity entity = corpInfoMapper.selectOne(wrapper);
        return entity;
    }

    public CorpInfoEntity getEntityByDisplayId(String displayId) {
        LambdaQueryWrapper<CorpInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CorpInfoEntity::getDisplayId,displayId);

        CorpInfoEntity entity = corpInfoMapper.selectOne(wrapper);
        return entity;
    }

    public Integer updateCorpInfo(CorpInfoEntity entity) {
        LambdaQueryWrapper<CorpInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CorpInfoEntity::getTenantKey,entity.getTenantKey());

        CorpInfoEntity corpInfoEntity = corpInfoMapper.selectOne(wrapper);
        LogUtils.info("CorpInfoManager.updateCorpInfo,corpInfoEntity={}",corpInfoEntity);

        int count = 0;
        if(corpInfoEntity!=null) {
            corpInfoEntity.setTenantName(entity.getTenantName());
            corpInfoEntity.setUpdateTime(new Date());
            count = corpInfoMapper.updateById(corpInfoEntity);
            LogUtils.info("CorpInfoManager.updateCorpInfo,update,count={}",count);
        } else {
            count = corpInfoMapper.insert(entity);
            LogUtils.info("CorpInfoManager.updateCorpInfo,insert,count={}",count);
        }
        return count;
    }

    public List<CorpInfoEntity> getAllEntity() {
        LambdaQueryWrapper<CorpInfoEntity> wrapper = new LambdaQueryWrapper<>();
        List<CorpInfoEntity> corpInfoEntities = corpInfoMapper.selectList(wrapper);
        LogUtils.info("CorpInfoManager.getAllEntity,size={}",corpInfoEntities.size());
        return corpInfoEntities;
    }
}
