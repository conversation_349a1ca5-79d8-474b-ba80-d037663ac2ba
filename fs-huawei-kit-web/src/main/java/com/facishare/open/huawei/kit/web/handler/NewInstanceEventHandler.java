package com.facishare.open.huawei.kit.web.handler;

import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.entity.OrderInfoEntity;
import com.facishare.open.huawei.kit.web.consts.HuaweiProduceConstant;
import com.facishare.open.huawei.kit.web.manager.OrderInfoManager;
import com.facishare.open.huawei.kit.web.result.CreateInstanceResult;
import com.facishare.open.huawei.kit.web.result.Result2;
import com.facishare.open.huawei.kit.web.service.EnterpriseBindService;
import com.facishare.open.huawei.kit.web.service.HuaweiOrderService;
import com.facishare.open.huawei.kit.web.template.outer.event.order.HuaweiOpenEnterpriseHandlerTemplate;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 新实例事件处理逻辑
 * <AUTHOR>
 * @date 20241107
 */
@Component
@Slf4j
public class NewInstanceEventHandler extends HuaweiEventHandler {

    @Resource
    private HuaweiOpenEnterpriseHandlerTemplate huaweiOpenEnterpriseHandlerTemplate;
    @Resource
    private EnterpriseBindService enterpriseBindService;
    @Resource
    private OrderInfoManager orderInfoManager;

    @Override
    public String getSupportEventType() {
        return HuaweiProduceConstant.NEW_INSTANCE;
    }

    @Override
    public void handle(KitVerifyTemplateData kitVerifyTemplateData) {
        log.info("TenantSyncKitEventHandler.handle,kitVerifyTemplateData={}",kitVerifyTemplateData);
//        com.facishare.open.feishu.syncapi.result.Result<List<EnterpriseBindEntity>> enterpriseBindList = enterpriseBindService.getEnterpriseBindList(kitVerifyTemplateData.getIsvProduceReq().get("tenantId").toString());
//        if(CollectionUtils.isNotEmpty(enterpriseBindList.getData())) {
//            return;
//        }

        //如果orderId重复，就不再处理
        OrderInfoEntity entity = orderInfoManager.getEntity(kitVerifyTemplateData.getIsvProduceReq().get("orderId").toString());
        log.info("TenantSyncKitEventHandler.handle,entity={}",entity);
        if(ObjectUtils.isNotEmpty(entity)) {
            return;
        }

        TemplateResult tenantSyncTemplateResult = huaweiOpenEnterpriseHandlerTemplate.execute(kitVerifyTemplateData);
        log.info("TenantSyncKitEventHandler.handle,tenantSyncTemplateResult={}",tenantSyncTemplateResult);
    }

    @Override
    public TemplateResult result(KitVerifyTemplateData kitVerifyTemplateData) {
        //创建实例
        //纷享无需创建实例，直接返回成功
        String businessId = String.valueOf(kitVerifyTemplateData.getIsvProduceReq().get("businessId"));
        return TemplateResult.newSuccess(CreateInstanceResult.newSuccess(businessId));
    }
}
