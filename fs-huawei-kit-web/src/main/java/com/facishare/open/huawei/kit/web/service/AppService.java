package com.facishare.open.huawei.kit.web.service;

import com.facishare.open.feishu.syncapi.entity.AppInfoEntity;
import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.result.Result;

/**
 * 应用服务
 * <AUTHOR>
 * @date 20220717
 */
public interface AppService {
    Result<Integer> updateAppInfo(AppInfoEntity entity);

    Result<AppInfoEntity> getAppInfo(String outEa);

    Result<AppInfoEntity> getAppInfo(String outEa, String appId);


}