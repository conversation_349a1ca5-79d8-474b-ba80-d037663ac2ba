package com.facishare.open.huawei.kit.web.handler;

import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.huawei.kit.web.consts.HuaweiProduceConstant;
import com.facishare.open.huawei.kit.web.model.HuaweiOrderDataModel;
import com.facishare.open.huawei.kit.web.result.Result;
import com.facishare.open.huawei.kit.web.service.EnterpriseBindService;
import com.facishare.open.huawei.kit.web.service.HuaweiOrderService;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import com.facishare.open.huawei.kit.web.template.outer.event.order.HuaweiSaveOrderHandlerTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 升级实例事件处理逻辑
 * <AUTHOR>
 * @date 20241107
 */
@Slf4j
@Component
public class UpgradeInstanceEventHandler extends HuaweiEventHandler {

    @Resource
    private HuaweiOrderService huaweiOrderService;
    @Resource
    private HuaweiSaveOrderHandlerTemplate huaweiSaveOrderHandlerTemplate;
    @Resource
    private EnterpriseBindService enterpriseBindService;


    @Override
    public String getSupportEventType() {
        return HuaweiProduceConstant.UPGRADE_INSTANCE;
    }

    @Override
    public void handle(KitVerifyTemplateData kitVerifyTemplateData) {
        log.info("UpgradeInstanceEventHandler.handle,kitVerifyTemplateData={}",kitVerifyTemplateData);
        //没有企业绑定关系时不做处理，通过instanceId查询corpInfo
        String upgradeInstanceInstanceId = String.valueOf(kitVerifyTemplateData.getIsvProduceReq().get("instanceId"));
        com.facishare.open.feishu.syncapi.result.Result<CorpInfoEntity> upgradeInstanceCorpInfoResult = huaweiOrderService.getCorpInfo(upgradeInstanceInstanceId);
        if(ObjectUtils.isEmpty(upgradeInstanceCorpInfoResult.getData())) {
            return;
        }
        com.facishare.open.feishu.syncapi.result.Result<List<EnterpriseBindEntity>> upgradeInstanceEnterpriseBindList = enterpriseBindService.getEnterpriseBindList(upgradeInstanceCorpInfoResult.getData().getTenantKey());
        if(CollectionUtils.isEmpty(upgradeInstanceEnterpriseBindList.getData())) {
            return;
        }
        //更新订单逻辑
        String upgradeOrderId = String.valueOf(kitVerifyTemplateData.getIsvProduceReq().get("orderId"));
        com.facishare.open.feishu.syncapi.result.Result<CorpInfoEntity> upgradeInfoEntityResult = huaweiOrderService.getCorpInfo(upgradeInstanceInstanceId);
        HuaweiOrderDataModel upgradeHuaweiOrderDataModel = new HuaweiOrderDataModel();
        upgradeHuaweiOrderDataModel.setInstanceId(upgradeInstanceInstanceId);
        upgradeHuaweiOrderDataModel.setTenantId(upgradeInfoEntityResult.getData().getTenantKey());
        upgradeHuaweiOrderDataModel.setOrderId(upgradeOrderId);

        TemplateResult upgradeTemplateResult = huaweiSaveOrderHandlerTemplate.execute(upgradeHuaweiOrderDataModel);
        log.info("UpgradeInstanceEventHandler.handle,upgradeTemplateResult={}",upgradeTemplateResult);

    }

    @Override
    public TemplateResult result(KitVerifyTemplateData kitVerifyTemplateData) {
        //升级实例
        return TemplateResult.newSuccess(Result.newSuccess());
    }
}
