package com.facishare.open.huawei.kit.web.service;

import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.result.Result;

/**
 * 企业服务
 * <AUTHOR>
 * @date 20220718
 */
public interface CorpService {

    /**
     * 获取企业绑定信息
     * @param tenantKey
     * @return
     */
    Result<CorpInfoEntity> getCorpEntity(String tenantKey);

    Result<Void> insertCorpInfo(CorpInfoEntity corpInfoEntity);
}
