package com.facishare.open.huawei.kit.web.handler;

import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.AppInfoEntity;
import com.facishare.open.huawei.kit.web.consts.HuaweiProduceConstant;
import com.facishare.open.huawei.kit.web.result.Result2;
import com.facishare.open.huawei.kit.web.service.AppService;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import com.facishare.open.huawei.kit.web.utils.RSAEncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 应用同步同步事件处理逻辑
 * <AUTHOR>
 * @date 20241107
 */
@Slf4j
@Component
public class ApplicationSyncKitEventHandler extends HuaweiEventHandler {

    @Resource
    private AppService appService;


    @Override
    public String getSupportEventType() {
        return HuaweiProduceConstant.APPLICATION_SYNC;
    }

    @Override
    public void handle(KitVerifyTemplateData kitVerifyTemplateData) {
        log.info("ApplicationSyncKitEventHandler.handle,kitVerifyTemplateData={}",kitVerifyTemplateData);
        String clientSecret = "";
        try {
            clientSecret = RSAEncryptionUtil.decrypt(kitVerifyTemplateData.getIsvProduceReq().get("clientSecret").toString(), ConfigCenter.PRIVATE_KEY_CLIENT);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        //应用同步，保存应用id
        AppInfoEntity entity = AppInfoEntity
                .builder()
                .tenantKey(kitVerifyTemplateData.getIsvProduceReq().get("tenantId").toString())
                .appId(kitVerifyTemplateData.getIsvProduceReq().get("appId").toString())
                .applicants("manager")
                .clientId(kitVerifyTemplateData.getIsvProduceReq().get("clientId").toString())
                .clientSecret(clientSecret)
                .build();
        appService.updateAppInfo(entity);
    }

    @Override
    public TemplateResult result(KitVerifyTemplateData kitVerifyTemplateData) {
        return TemplateResult.newSuccess(Result2.newSuccess());
    }
}
