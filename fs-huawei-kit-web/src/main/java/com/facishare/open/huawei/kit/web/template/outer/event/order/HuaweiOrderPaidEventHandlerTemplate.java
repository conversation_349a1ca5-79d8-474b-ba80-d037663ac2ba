package com.facishare.open.huawei.kit.web.template.outer.event.order;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.order.OrderPaidEventHandlerTemplate;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.huawei.kit.web.service.HuaweiOrderService;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class HuaweiOrderPaidEventHandlerTemplate extends OrderPaidEventHandlerTemplate {
    @Resource
    private HuaweiOpenEnterpriseHandlerTemplate huaweiAddOrderEventHandlerTemplate;
    @Resource
    private HuaweiSaveOrderHandlerTemplate feishuUpdateOrderEventHandlerTemplate;
    @Resource
    private HuaweiOrderService huaweiOrderService;


    @Override
    public void openEnterpriseOrSaveOrder(MethodContext context) {
        log.info("HuaweiOrderPaidEventHandlerTemplate.openEnterpriseOrSaveOrder,context={}",context);
        KitVerifyTemplateData kitVerifyTemplateData = context.getData();
        String tenantId = kitVerifyTemplateData.getIsvProduceReq().get("tenantId").toString();

        List<EnterpriseBindEntity> enterpriseBindList = huaweiOrderService.getEnterpriseBindList(tenantId).getData();
        log.info("HuaweiOrderPaidEventHandlerTemplate.openEnterpriseOrSaveOrder,enterpriseBindList={}",enterpriseBindList);
        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            //如果没有绑定关系，触发开通企业的逻辑
            context.setResult(TemplateResult.newSuccess(OPEN_ENTERPRISE));
        } else {
            //如果有企业绑定关系，触发保存订单的逻辑
            context.setResult(TemplateResult.newSuccess(SAVE_ORDER));
        }
        log.info("HuaweiOrderPaidEventHandlerTemplate.openEnterpriseOrSaveOrder,context.2={}",context);
    }

    @Override
    public void openEnterprise(MethodContext context) {
        log.info("HuaweiOrderPaidEventHandlerTemplate.openEnterprise,context={}",context);
        TemplateResult result = huaweiAddOrderEventHandlerTemplate.execute(context.getData());
        log.info("HuaweiOrderPaidEventHandlerTemplate.openEnterprise,result={}",result);
    }

    @Override
    public void saveOrder(MethodContext context) {
        log.info("HuaweiOrderPaidEventHandlerTemplate.saveOrder,context={}",context);
        TemplateResult result = feishuUpdateOrderEventHandlerTemplate.execute(context.getData());
        log.info("HuaweiOrderPaidEventHandlerTemplate.saveOrder,result={}",result);
    }
}
