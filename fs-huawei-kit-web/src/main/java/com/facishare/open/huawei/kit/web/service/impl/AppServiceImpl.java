package com.facishare.open.huawei.kit.web.service.impl;

import com.facishare.open.huawei.kit.web.manager.AppInfoManager;
import com.facishare.open.feishu.syncapi.entity.AppInfoEntity;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.huawei.kit.web.service.AppService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("appService")
public class AppServiceImpl implements AppService {
    @Resource
    private AppInfoManager appInfoManager;

    @Override
    public Result<Integer> updateAppInfo(AppInfoEntity entity) {
        return new Result<>(appInfoManager.updateAppInfo(entity));
    }

    @Override
    public Result<AppInfoEntity> getAppInfo(String outEa) {
        return new Result<>(appInfoManager.getEntity(outEa));
    }

    @Override
    public Result<AppInfoEntity> getAppInfo(String outEa, String appId) {
        return new Result<>(appInfoManager.getEntity(outEa, appId));
    }
}
