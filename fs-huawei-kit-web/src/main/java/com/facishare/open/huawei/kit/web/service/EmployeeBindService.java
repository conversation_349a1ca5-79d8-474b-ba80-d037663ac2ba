package com.facishare.open.huawei.kit.web.service;

import com.facishare.open.feishu.syncapi.arg.QueryEmployeeBindArg;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.feishu.syncapi.info.EmployeeBindInfo;
import com.facishare.open.feishu.syncapi.result.Result;

import java.util.List;

public interface EmployeeBindService {

    Result<EmployeeBindInfo> queryEmployeeBind(String channel, QueryEmployeeBindArg arg);

    Result<List<EmployeeBindInfo>> queryEmployeeBindListByOutData(ChannelEnum channel, String outEa, String outUserId);
}
