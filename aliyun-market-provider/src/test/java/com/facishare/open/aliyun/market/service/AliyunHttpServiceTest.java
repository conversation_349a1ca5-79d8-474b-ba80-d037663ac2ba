package com.facishare.open.aliyun.market.service;

import com.aliyun.market20151101.models.DescribeOrderResponseBody;
import com.facishare.open.aliyun.market.BaseTest;
import com.facishare.open.feishu.syncapi.result.Result;
import org.junit.Test;

import javax.annotation.Resource;

public class AliyunHttpServiceTest extends BaseTest {
    @Resource
    private AliyunHttpService aliyunHttpService;

    @Test
    public void getOrderInfo() throws Exception {
        Result<DescribeOrderResponseBody> result = aliyunHttpService.getOrderInfo("217974047440914");
        System.out.println(result);
    }
}
