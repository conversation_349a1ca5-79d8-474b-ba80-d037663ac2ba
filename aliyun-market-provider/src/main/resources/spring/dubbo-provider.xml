<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <description>dubbo提供者接口</description>

<!--    <dubbo:service interface="com.facishare.open.order.contacts.proxy.api.service.FsEventService"-->
<!--                   ref="fsEventService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.AliyunLoginService"
                   ref="aliyunLoginService"
                   protocol="dubbo"
                   timeout="300000"
                   version="1.0"
                   retries="3"/>
</beans>
