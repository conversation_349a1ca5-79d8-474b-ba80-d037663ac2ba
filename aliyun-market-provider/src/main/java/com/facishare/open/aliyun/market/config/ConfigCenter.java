package com.facishare.open.aliyun.market.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.syncapi.model.config.VersionModel;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;

import java.util.Set;

public class ConfigCenter {
    //阿里云服务商ACCESS_KEY_ID
    public static String ACCESS_KEY_ID;
    //阿里云服务商ACCESS_KEY_SECRET
    public static String ACCESS_KEY_SECRET;
    public static JSONObject aliyunProductCode = null;
    //CRM域名URL
    public static String CRM_DOMAIN;
    //免登录重定向uri
    public static String REDIRECT_URI;
    //阿里云SDK组件代理
    public static String ALIYUN_SDK_HTTP_PROXY;

    public static Set<String> CRM_PRODUCT_NUM = Sets.newHashSet();

    static {
        ConfigFactory.getInstance().getConfig("fs-aliyun-market-config", config -> {
            ACCESS_KEY_ID = config.get("ACCESS_KEY_ID", ACCESS_KEY_ID);
            ACCESS_KEY_SECRET = config.get("ACCESS_KEY_SECRET", ACCESS_KEY_SECRET);
            String aliyunProductCodeInfo = config.get("ALIYUN_PRODUCT_CODE");
            aliyunProductCode = JSON.parseObject(aliyunProductCodeInfo);
            LogUtils.info("OrderPaidEventHandler.init,aliyunProductCode={}", aliyunProductCode);

            CRM_DOMAIN = config.get("crm_domain");
            REDIRECT_URI = config.get("redirect_uri");

            CRM_PRODUCT_NUM =  ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("CRM_PRODUCT_NUM", "")));

            ALIYUN_SDK_HTTP_PROXY = config.get("ALIYUN_SDK_HTTP_PROXY");
        });
    }

    public static JSONArray getVersionArray(String appId, String pricePlanId) {
        JSONObject editionObj = aliyunProductCode.getJSONObject(appId);
        JSONArray editionArray = editionObj.getJSONArray(pricePlanId);
        LogUtils.info("ConfigCenter.getVersionArray,editionArray={}",editionArray);

        return editionArray;
    }

    public static VersionModel getFirstVersionProductId(String appId, String pricePlanId) {
        JSONArray editionArray = getVersionArray(appId, pricePlanId);
        VersionModel model = JSONObject.parseObject(editionArray.getJSONObject(0).toJSONString(),VersionModel.class);
        return model;
    }
}
