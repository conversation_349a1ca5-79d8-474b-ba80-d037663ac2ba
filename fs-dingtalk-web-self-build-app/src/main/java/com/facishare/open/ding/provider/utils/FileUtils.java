package com.facishare.open.ding.provider.utils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2020/11/24 15:39
 * @Version 1.0
 */
public class FileUtils {
      public static void writeText(String content){
            try{
                File file =new File("D:\\opt\\dinglog.txt");
                if(!file.exists()){
                    file.createNewFile();
                }
                //使用true，即进行append file
                FileWriter fileWritter = new FileWriter(file.getName(),true);
                fileWritter.write(content);
                fileWritter.close();
                System.out.println("finish");
            }catch(IOException e){
                e.printStackTrace();
            }
        }

    }
