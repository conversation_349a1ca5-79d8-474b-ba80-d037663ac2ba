package com.facishare.open.ding.api.service;

import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.vo.ConnectionVo;
import com.facishare.open.ding.common.result.Result;

import java.util.List;
import java.util.Map;

/**
 * <p>类的详细说明</p>
 *
 * @version 1.0
 * @dateTime 2018-07-12 10:50
 * @<NAME_EMAIL>
 */
public interface EnterpriseService {

    /**
     * 根据EA查询企业绑定信息
     * @param ea
     * @return
     */
    Result<DingEnterpriseResult> queryEnterpriseByEa(String ea);

    Result<DingEnterpriseResult> queryEnterpriseByEaEmpty(String ea);

    /**
     * 根据EI查询企业绑定信息
     * @param ei
     * @return
     */
    Result<DingEnterpriseResult> queryEnterpriseByEi(Integer ei);

    /**
     * 根据appKey查询企业绑定信息
     * @param appKey
     * @return
     */
    Result<DingEnterpriseResult> queryEnterpriseByAppKey(String appKey);


    /**
     * 根据corpId查询企业绑定信息
     * @param corpId
     * @return
     */
    Result<DingEnterpriseResult> queryEnterpriseByCorpId(String corpId);

    /**
     * 连接cloud - 开始连接
     * @param vo
     * @return
     */
    Result<Integer> startConnect(ConnectionVo vo);

    /**
     * 连接cloud - 变更设置
     * @param vo
     * @return
     */
    Result<Integer> updateConnection(ConnectionVo vo);

    /**
     * 查询企业所有一级部门
     * @param ei
     * @return
     */
    Result<List<Map<String,Object>>> queryDeptLevelOne(Integer ei);

    /**
     * 修改crm提醒状态
     */
    Result<Integer> saveOperation(ConnectionVo vo);

    /**
     * 账号自动绑定
     */
    Result<Integer> saveAutoBind(String ea,Integer ei, Integer autoBind);

    /**
     * 刷库
     */
    Result<Void> updateAllDingEnterprise();
}
