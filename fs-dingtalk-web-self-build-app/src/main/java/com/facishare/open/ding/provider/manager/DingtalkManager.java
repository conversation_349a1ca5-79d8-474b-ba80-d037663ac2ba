package com.facishare.open.ding.provider.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.vo.DingTaskVo;
import com.facishare.open.ding.common.model.bizlog.DingAPICallNumer;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.config.ConfigCenter;
import com.facishare.open.ding.provider.dao.DingMappingEmployeeDao;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.dingding.DingUrl;
import com.facishare.open.ding.provider.handler.DingRetryHandlerFromProvider;
import com.facishare.open.ding.provider.handler.QPSLimitHandlerFromProvider;
import com.facishare.open.ding.provider.model.HttpResponseMessage;
import com.facishare.open.ding.provider.utils.XorUtils;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.fxiaoke.message.extrnal.platform.model.result.CreateTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DealTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DeleteTodoResult;
import com.fxiaoke.ps.ProtostuffUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DingtalkManager {

    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;

    @Autowired
    private DingTodoManager dingTodoManager;

    @Autowired
    private DingMappingEmployeeDao dingMappingEmployeeDao;

    @ReloadableProperty("mid.authorize.url")
    private String MID_URL;

    //客户端
    private static final String DING_SINGLE_URL = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?response_type=code&scope=snsapi_auth&state=STATE";

    private static final String DING_PC_SLIDE_URL = "dingtalk://dingtalkclient/page/link?url=%s&pc_slide=true";

    @Autowired
    private QPSLimitHandlerFromProvider qpsLimitHandlerFromProvider;

    private LoadingCache<String, String> cache = Caffeine.newBuilder().maximumSize(10000).expireAfterWrite(100, TimeUnit.MINUTES).refreshAfterWrite(90, TimeUnit.MINUTES).build(new CacheLoader<String, String>() {
        @Nullable
        @Override
        public String load(@NonNull String key) throws Exception {
            Result<DingEnterpriseResult> dingEnterpriseResultResult = dingEnterpriseManager.queryEnterpriseByEi(Integer.valueOf(key));
            DingEnterpriseResult result = dingEnterpriseResultResult.getData();
            return DingRequestUtil.getToken(result.getClientIp(), result.getAppKey(), result.getAppSecret());
        }
    });

    public CreateTodoResult createTodo(CreateTodoArg arg, Result<DingEnterpriseResult> mappingEnterprise) {
        Gson gson = new Gson();
        CreateTodoResult result = new CreateTodoResult();
        log.info("Enter the gray-scale enterprise agency process");

//        Result<DingEnterpriseResult> mappingEnterprise = dingEnterpriseManager.queryEnterpriseInfoByEi(arg.getEi());
//        if (Objects.isNull(mappingEnterprise) || Objects.isNull(mappingEnterprise.getData())) {
//            log.warn("ei={}.", arg.getEi());
//            return result;
//        }
//        log.info("externalOaTodoServiceImpl.createTodo.mappingEnterprise = {}", mappingEnterprise);
//        if(!(mappingEnterprise.getData().getAlertStatus().equals(AlertStatusEnum.TODO_STATUS.getStatus()) || mappingEnterprise.getData().getAlertStatus().equals(AlertStatusEnum.TODO_AND_REMIND_STATUS.getStatus()))){
//            //已经关闭就不再发送crm待办消息
//            log.info("externalOaTodoServiceImpl.createTodo,crm alert status was closed arg:{}",arg);
//            result.setMessage("crm alert was closed");
//            return result;
//        }

        //去掉crm与钉钉无绑定的人员的账号
        List<Integer> receiverIds = new LinkedList<>();
        for (int i = 0; i < arg.getReceiverIds().size(); i++) {
            int receiverCount = dingMappingEmployeeDao.findByEmployeeId(arg.getEi(), arg.getReceiverIds().get(i));
            if (receiverCount >= 1) {
                receiverIds.add(arg.getReceiverIds().get(i));
            }
        }
        if (CollectionUtils.isEmpty(receiverIds)) {
            log.info("the receivers is not binded.");
            return result;
        }
        log.info("externalOaTodoServiceImpl.createTodo.receiverIds = {}", receiverIds);

        //accessToken获取
//        Boolean isQPSLimit = qpsLimitHandler.isQPSLimitByEa(arg.getEa());
//        if(isQPSLimit) {
//            //限流且重试多次失败
//            log.info("externalOaTodoServiceImpl.createTodo.qps limit.arg={}", arg);
//            return result;
//        }
        String accessToken = cache.get(String.valueOf(arg.getEi()));
        Map<String, String> dingTalkToken = Maps.newHashMap();
        dingTalkToken.put("x-acs-dingtalk-access-token", accessToken);
        log.info("externalOaTodoServiceImpl.createTodo.accessToken = {}", XorUtils.EncodeByXor(accessToken, ConfigCenter.XOR_SECRET_KEY));

        Map<String, Object> dingTalk = Maps.newHashMap();
        dingTalk.put("sourceId", arg.getSourceId());
        //标志
        Boolean flag = false;

        List<DingMappingEmployeeResult> employeeLists = dingMappingEmployeeDao.batchGetDingEmployeesByFsIds(arg.getEi(), receiverIds);
        if (CollectionUtils.isEmpty(employeeLists)) {
            log.warn("userUnionIds is empty, arg={}.", arg);
            result.setMessage("userUnionIds is empty.");
            return result;
        }
        List<String> userUnionIds = employeeLists.stream().map(DingMappingEmployeeResult::getDingUnionId).collect(Collectors.toList());
        int operatorId = employeeLists.get(0).getEmployeeId();
        String operatorUnionId = employeeLists.get(0).getDingUnionId();
        log.info("externalOaTodoServiceImpl.createTodo.ea={},userUnionIds={}", arg.getEa(), userUnionIds);
        //临时解决
        Map<String, String> sysUserIdMap = new Gson().fromJson(ConfigCenter.SYS_TODO_USER_IDS, new TypeToken<Map<String, String>>(){}.getType());
        if(sysUserIdMap.containsKey(arg.getEa())) {
            operatorId = -10000;
            operatorUnionId = sysUserIdMap.get(arg.getEa());
            log.info("externalOaTodoServiceImpl.createTodo.ea={},operatorUnionId={}.", arg.getEa(), operatorUnionId);
        }
        //查看代办详情
        String dealTalkUrl = DingUrl.DINGTALK_TODO + operatorUnionId + "/tasks/sources/" + arg.getSourceId();
        Boolean isQPSLimit3 = qpsLimitHandlerFromProvider.isQPSLimitByEa(arg.getEa());
        if(isQPSLimit3) {
            //限流且重试多次失败
            log.info("externalOaTodoServiceImpl.createTodo.qps limit.arg={}", arg);
            return result;
        }
        HttpResponseMessage messageResult = null;
        JSONObject jsonObject = null;
        if(mappingEnterprise.getData().getClientIp().equalsIgnoreCase(ConfigCenter.DING_CLIENT_IP)) {
            Map<String, Object> messageArg = new HashMap<>();
            messageArg.put("url", dealTalkUrl);//钉钉发送消息的url
            messageArg.put("type", "GET");
            messageArg.put("header", gson.toJson(dingTalkToken));
            messageArg.put("token", mappingEnterprise.getData().getToken());
            String clientUrl = DingRequestUtil.appendProxyHttpUrl(mappingEnterprise.getData().getClientIp());
            messageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(clientUrl, new HashMap<>(), gson.toJson(messageArg));
            JSONObject messageJsonObject = JSONObject.parseObject(messageResult.getContent());
            if((Integer) messageJsonObject.get("errorCode") != 200) {
                log.warn("messageHttpResult is error, arg={}.", arg);
                result.setMessage("messageResult is error.");
                return result;
            }
            jsonObject = JSONObject.parseObject(messageJsonObject.get("data").toString());
        } else {
            messageResult = DingRetryHandlerFromProvider.sendOkHttp3Get(dealTalkUrl, dingTalkToken, new HashMap<>());
            log.info("externalOaTodoServiceImpl.createTodo.createTalkUrl={},messageResult={}.", dealTalkUrl, messageResult);
            if(messageResult.getStatusCode() != 200) {
                log.warn("messageResult is error, arg={}.", arg);
                result.setMessage("messageResult is error.");
                return result;
            }
            jsonObject = JSONObject.parseObject(messageResult.getContent());
        }
        String talkId = String.valueOf(jsonObject.get("id"));
        if(StringUtils.isEmpty(talkId) || "null".equals(talkId)) {
            //找不到代办，新建代办
            //优先级
            int priority = 20;
            //优化卡片消息
            StringBuilder markdown = new StringBuilder();
            StringBuilder subject = new StringBuilder();
            StringBuilder description = new StringBuilder();
            if(StringUtils.isNotEmpty(arg.getTitle())) {
                subject.append(arg.getTitle()).append("-");
            }
            description.append(arg.getContent()).append("-");
            if (CollectionUtils.isNotEmpty(arg.getForm())) {
                for (int i = 0; i < arg.getForm().size(); i++) {
                    markdown.append("【").append(arg.getForm().get(i).getKey()).append(": ").append(arg.getForm().get(i).getValue()).append("】");
                    if (i == 1) {
                        subject.append(markdown);
                    }
                }
            }
            description.append(markdown);
            dingTalk.put("subject", subject.toString());
            dingTalk.put("description", description.toString());
            //跳转
            String objectApiName = arg.getExtraDataMap().get("objectApiName");
            String objectId = arg.getExtraDataMap().get("objectId");
            String instanceId = arg.getExtraDataMap().get("workflowInstanceId");
            String directUri = MID_URL + "?ei=" + arg.getEi() + "&apiname=" + objectApiName
                    + "&id=" + objectId + "&instanceId=" + instanceId + "&taskId=" + arg.getGenerateUrlType()  + "&bizType=" + arg.getBizType();
            StringBuilder stringBuilder = new StringBuilder();
            String directAppId = mappingEnterprise.getData().getRedirectAppId();
            stringBuilder.append(DING_SINGLE_URL).append("&appid=").append(directAppId).append("&redirect_uri=").append(URLEncoder.encode(directUri));
            log.info("finalUrl = {}", stringBuilder.toString());
            Map<String, String> detailUrl = Maps.newHashMap();
            detailUrl.put("appUrl", stringBuilder.toString());
            String pcUrl = String.format(DING_PC_SLIDE_URL, URLEncoder.encode(stringBuilder.toString()));
            detailUrl.put("pcUrl", pcUrl);
            dingTalk.put("creatorId", operatorUnionId);
            dingTalk.put("detailUrl", detailUrl);
            //设置代办任务仅出现在处理人当中，创建者的钉钉就不会出现不需要处理的代办
            boolean isOnlyShowExecutor = true;
            dingTalk.put("isOnlyShowExecutor", isOnlyShowExecutor);
            dingTalk.put("priority", priority);
            dingTalk.put("executorIds", userUnionIds);
            dingTalk.put("participantIds", userUnionIds);
            //设置截止时间，钉钉代办可以显示飘数，约定截止时间为当天的23:59:59，过期也可以显示飘数，不影响页面的跳转
            try {
                String endTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd 23:59:59");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date datetime = sdf.parse(endTime);//将你的日期转换为时间戳
                long time = datetime.getTime();
                dingTalk.put("dueTime", time);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            //调用钉钉代办第三方接口
            String dingTalkUrl = DingUrl.DINGTALK_TODO + operatorUnionId + "/tasks?operatorId=" + operatorUnionId;
            Boolean isQPSLimit1 = qpsLimitHandlerFromProvider.isQPSLimitByEa(arg.getEa());
            if(isQPSLimit1) {
                //限流且重试多次失败
                log.info("externalOaTodoServiceImpl.createTodo.qps limit.arg1={}", arg);
                return result;
            }

            try {
                for (int crmUserId : receiverIds) {//按照Userid分开上报是为了能快速识别哪些userid用了最多的API
                    DingAPICallNumer dumpLog = DingAPICallNumer.builder().tenantId(String.valueOf(arg.getEi()))
                            .bizName(arg.getBizType()).title(arg.getTitle()).senderId(arg.getSenderId()).appid(arg.getAppId()).crmUserId(crmUserId)
                            .objectApiName(arg.getExtraDataMap().get("objectApiName")).objectId(arg.getExtraDataMap().get("objectId"))
                            .functionName("createTodo")
                            .build();
                    BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
                }
            } catch (Exception ex) {}

            HttpResponseMessage CreateMessageResult = null;
            JSONObject createJsonObject = null;
            Integer createCode = null;
            String createContent = null;
            if(mappingEnterprise.getData().getClientIp().equalsIgnoreCase(ConfigCenter.DING_CLIENT_IP)) {
                Map<String, Object> messageArg = new HashMap<>();
                messageArg.put("url", dingTalkUrl);//钉钉发送消息的url
                messageArg.put("type", "POST");
                messageArg.put("header", gson.toJson(dingTalkToken));
                messageArg.put("token", mappingEnterprise.getData().getToken());
                messageArg.put("data", dingTalk);
                String clientUrl = DingRequestUtil.appendProxyHttpUrl(mappingEnterprise.getData().getClientIp());
                CreateMessageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(clientUrl, new HashMap<>(),gson.toJson(messageArg));
                JSONObject createMsgJsonObject = JSONObject.parseObject(CreateMessageResult.getContent());
                createCode = (Integer) createMsgJsonObject.get("errorCode");
                createContent = createMsgJsonObject.get("data").toString();
            } else {
                CreateMessageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(dingTalkUrl, dingTalkToken, gson.toJson(dingTalk));
                createCode = CreateMessageResult.getStatusCode();
                createContent = CreateMessageResult.getContent();
            }
            log.info("externalOaTodoServiceImpl.createTodo.create.ea={},dingTalk={},updateToDo={},CreateMessageResult={}.", arg.getEa(), dingTalk, dingTalkUrl, CreateMessageResult);
            if (createCode == 200) {
                createJsonObject = JSONObject.parseObject(createContent);
                log.warn("ExternalOaTodoServiceImpl.createTodo: 新增代办成功，ea={}，messageArg={}，userId={}.", arg.getEa(), dingTalk, receiverIds);
                for (int i = 0; i < receiverIds.size(); i++) {
                    DingTaskVo dingTaskVo = new DingTaskVo();
                    dingTaskVo.setEi(arg.getEi());
                    dingTaskVo.setEmployeeId(receiverIds.get(i));
                    dingTaskVo.setSourceId(arg.getSourceId());
                    dingTaskVo.setCreatorEmployeeId(operatorId);
                    dingTaskVo.setTaskId(String.valueOf(createJsonObject.get("id")));
                    int update = dingTodoManager.insertSource(dingTaskVo);
                    if (update >= 1) {
                        log.info("ExternalOaTodoServiceImpl.createTodo: 保存关系映射成功， dingTaskVo={}.", dingTaskVo);
                    }
                }
            } else {
                log.warn("ExternalOaTodoServiceImpl.createTodo: 新增代办失败，ea={}，messageArg={}，userId={}.", arg.getEa(), dingTalk, receiverIds);
            }
        } else {
            //代办已经存在，更新代办

            String executorIdJson = JSONArray.toJSONString(jsonObject.get("executorIds"));
            // 将json字符串转换为list集合
            List<String> executorIds = CollectionUtils.isNotEmpty(JSONArray.parseArray(executorIdJson, String.class)) ? JSONArray.parseArray(executorIdJson, String.class) : new LinkedList<>();
            executorIds.addAll(userUnionIds);
            String participantIdJson = JSONArray.toJSONString(jsonObject.get("participantIds"));
            // 将json字符串转换为list集合
            List<String> participantIds = CollectionUtils.isNotEmpty(JSONArray.parseArray(participantIdJson, String.class)) ? JSONArray.parseArray(participantIdJson, String.class) : new LinkedList<>();
            participantIds.addAll(userUnionIds);
            dingTalk.put("done", flag);
            dingTalk.put("executorIds", executorIds);
            dingTalk.put("participantIds", participantIds);
            String updateToDo = DingUrl.DINGTALK_TODO + operatorUnionId + "/tasks/" + talkId + "?operatorId=" + operatorUnionId;
            Boolean isQPSLimit2 = qpsLimitHandlerFromProvider.isQPSLimitByEa(arg.getEa());
            if(isQPSLimit2) {
                //限流且重试多次失败
                log.info("externalOaTodoServiceImpl.createTodo.qps limit.arg2={}", arg);
                return result;
            }

            try {
                for (int crmUserId : receiverIds) {//按照Userid分开上报是为了能快速识别哪些userid用了最多的API
                    DingAPICallNumer dumpLog = DingAPICallNumer.builder().tenantId(String.valueOf(arg.getEi()))
                            .bizName(arg.getBizType()).title(arg.getTitle()).senderId(arg.getSenderId()).appid(arg.getAppId()).crmUserId(crmUserId)
                            .objectApiName(arg.getExtraDataMap().get("objectApiName")).objectId(arg.getExtraDataMap().get("objectId"))
                            .functionName("updateTodo")
                            .build();
                    BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
                }
            } catch (Exception ex) {}

            HttpResponseMessage updateToDoMessageResult = null;
            Integer updateCode = null;
            if(mappingEnterprise.getData().getClientIp().equalsIgnoreCase(ConfigCenter.DING_CLIENT_IP)) {
                Map<String, Object> messageArg = new HashMap<>();
                messageArg.put("url", updateToDo);//钉钉发送消息的url
                messageArg.put("type", "PUT");
                messageArg.put("header", gson.toJson(dingTalkToken));
                messageArg.put("token", mappingEnterprise.getData().getToken());
                messageArg.put("data", dingTalk);
                String clientUrl = DingRequestUtil.appendProxyHttpUrl(mappingEnterprise.getData().getClientIp());
                updateToDoMessageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(clientUrl, new HashMap<>(),gson.toJson(messageArg));
                JSONObject updateMsgJsonObject = JSONObject.parseObject(updateToDoMessageResult.getContent());
                updateCode = (Integer) updateMsgJsonObject.get("errorCode");
            } else {
                updateToDoMessageResult = DingRetryHandlerFromProvider.sendOkHttp3Put(updateToDo, dingTalkToken, gson.toJson(dingTalk));
                updateCode = updateToDoMessageResult.getStatusCode();
            }
            log.info("externalOaTodoServiceImpl.createTodo.update.ea={},dingTalk={},updateToDo={},updateToDoMessageResult={}.", arg.getEa(), dingTalk, updateToDo, updateToDoMessageResult);
            if (updateCode == 200) {
                log.warn("externalOaTodoServiceImpl.createTodo: 更新代办成功，creatorEmployeeId={}更新代办成功.", operatorId);
                for (int i = 0; i < receiverIds.size(); i++) {
                    DingTaskVo dingTaskVo = new DingTaskVo();
                    dingTaskVo.setEi(arg.getEi());
                    dingTaskVo.setEmployeeId(receiverIds.get(i));
                    dingTaskVo.setSourceId(arg.getSourceId());
                    dingTaskVo.setCreatorEmployeeId(operatorId);
                    dingTaskVo.setTaskId(talkId);
                    int update = dingTodoManager.insertSource(dingTaskVo);
                    if (update >= 1) {
                        log.info("ExternalOaTodoServiceImpl.createTodo: 保存关系映射成功， dingTaskVo={}.", dingTaskVo);
                    }
                }
            } else {
                log.warn("ExternalOaTodoServiceImpl.createTodo: 新增代办失败，messageArg={}，userId={}.", dingTalk, receiverIds);
            }
        }
        return result;
    }

    public DealTodoResult dealTodo(DealTodoArg var1, Result<DingEnterpriseResult> mappingEnterprise) {
        //处理代办
        log.info("Enter the dealTodo resource");
        DealTodoResult result = new DealTodoResult();
        result.setCode(200);
//        Result<DingEnterpriseResult> mappingEnterprise = dingEnterpriseManager.queryEnterpriseInfoByEi(var1.getEi());
//        if (Objects.isNull(mappingEnterprise) || Objects.isNull(mappingEnterprise.getData())) {
//            log.warn("ei={}.", var1.getEi());
//            return result;
//        }
//        log.info("ExternalOaTodoServiceImpl.dealTodo: dealToDO messageArg:{}", var1);
//        if(!(mappingEnterprise.getData().getAlertStatus().equals(AlertStatusEnum.TODO_STATUS.getStatus()) || mappingEnterprise.getData().getAlertStatus().equals(AlertStatusEnum.TODO_AND_REMIND_STATUS.getStatus()))){
//            //根据source判断这个待办是否还没有处理
//            List<DingTaskVo> dingTaskVo = dingTodoManager.getDingTaskVo(var1.getEi(), var1.getSourceId());
//            if(CollectionUtils.isEmpty(dingTaskVo)) {
//                //已经关闭就不再发送crm待办消息
//                log.info("externalOaTodoServiceImpl.createTodo,crm alert status was closed arg:{}",var1);
//                result.setMessage("crm alert was closed");
//                return result;
//            }
//        }

        //去掉crm与钉钉无绑定的人员的账号
        List<Integer> operators = new LinkedList<>();
        for(int i = 0; i < var1.getOperators().size(); i ++) {
            int receiverCount = dingMappingEmployeeDao.findByEmployeeId(var1.getEi(), var1.getOperators().get(i));
            if(receiverCount >= 1) {
                operators.add(var1.getOperators().get(i));
            }
        }
        if(CollectionUtils.isEmpty(operators)) {
            log.info("the receivers is not binded.");
            return result;
        }
        log.info("externalOaTodoServiceImpl.dealTodo.ea={},receiverIds={}", var1.getEa(), operators);
//        Boolean isQPSLimit2 = qpsLimitHandler.isQPSLimitByEa(var1.getEa());
//        if(isQPSLimit2) {
//            //限流且重试多次失败
//            log.info("externalOaTodoServiceImpl.dealTodo.qps limit.var1={}", var1);
//            return result;
//        }
        String accessToken = cache.get(String.valueOf(var1.getEi()));
        Map<String, String> dingTalkToken = new HashMap<>();
        dingTalkToken.put("x-acs-dingtalk-access-token", accessToken);
        List<DingTaskVo> dingTaskVos = dingTodoManager.getDingTaskVo(var1.getEi(), var1.getSourceId());
        if(CollectionUtils.isEmpty(dingTaskVos)) {
//            log.info("externalOaTodoServiceImpl.dealTodo,ea={},thread sleep.", var1.getEa());
//            //为避免先消费了处理接口，再消费新增接口，需要让线程睡1秒再执行一次查询动作
//            try {
//                Thread.sleep(1000L);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            dingTaskVos = dingTodoManager.getDingTaskVo(var1.getEi(), var1.getSourceId());
//            if(CollectionUtils.isEmpty(dingTaskVos)) {
//                log.info("the dingTaskVos1 is null.");
//                return result;
//            }
            //顺序消费
            log.info("the dingTaskVos1 is null.");
            return result;
        }
        Map<String, Object> dingTalk = Maps.newHashMap();
        List<Map<String, Object>> executorStatusList = new LinkedList<>();
        Boolean flag = Boolean.TRUE;
        List<DingMappingEmployeeResult> employeeLists = dingMappingEmployeeDao.batchGetDingEmployeesByFsIds(var1.getEi(), operators);
        if(CollectionUtils.isEmpty(employeeLists)) {
            log.info("the employeeLists is null.");
            return result;
        }
        List<String> userUnionIds = employeeLists.stream().map(DingMappingEmployeeResult::getDingUnionId).collect(Collectors.toList());
        String operatorUnionId = employeeLists.get(0).getDingUnionId();
        for(String userUnionId : userUnionIds) {
            Map<String, Object> executorStatus = Maps.newHashMap();
            executorStatus.put("id", userUnionId);
            executorStatus.put("isDone", flag);
            executorStatusList.add(executorStatus);
        }
        dingTalk.put("executorStatusList", executorStatusList);
        //更换处理接口
        Gson gson = new Gson();
        String dealTalkUrl = DingUrl.DINGTALK_TODO + operatorUnionId + "/tasks/" + dingTaskVos.get(0).getTaskId() + "/executorStatus?operatorId=" + operatorUnionId;
        Boolean isQPSLimit = qpsLimitHandlerFromProvider.isQPSLimitByEa(var1.getEa());
        if(isQPSLimit) {
            //限流且重试多次失败
            log.info("externalOaTodoServiceImpl.dealTodo.qps limit.var1={}", var1);
            return result;
        }

        try {
            for (int crmUserId : operators) {//按照Userid分开上报是为了能快速识别哪些userid用了最多的API
                DingAPICallNumer dumpLog = DingAPICallNumer.builder().tenantId(String.valueOf(var1.getEi()))
                        .bizName(var1.getBizType()).title("").senderId(0).appid(var1.getAppId()).crmUserId(crmUserId)
                        .objectApiName(var1.getExtraDataMap().get("objectApiName")).objectId(var1.getExtraDataMap().get("objectId"))
                        .functionName("dealTodo")
                        .build();
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            }
        } catch (Exception ex) {}

        HttpResponseMessage messageResult = null;
        JSONObject jsonObject = null;
        Integer code = null;
        String content = null;
        if(mappingEnterprise.getData().getClientIp().equalsIgnoreCase(ConfigCenter.DING_CLIENT_IP)) {
            Map<String, Object> messageArg = new HashMap<>();
            messageArg.put("url", dealTalkUrl);//钉钉发送消息的url
            messageArg.put("type", "PUT");
            messageArg.put("header", gson.toJson(dingTalkToken));
            messageArg.put("token", mappingEnterprise.getData().getToken());
            messageArg.put("data", dingTalk);
            String clientUrl = DingRequestUtil.appendProxyHttpUrl(mappingEnterprise.getData().getClientIp());
            messageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(clientUrl, new HashMap<>(),gson.toJson(messageArg));
            JSONObject msgJsonObject = JSONObject.parseObject(messageResult.getContent());
            code = (Integer) msgJsonObject.get("errorCode");
            content = msgJsonObject.get("data").toString();
        } else {
            messageResult = DingRetryHandlerFromProvider.sendOkHttp3Put(dealTalkUrl, dingTalkToken, gson.toJson(dingTalk));
            code = messageResult.getStatusCode();
            content = messageResult.getContent();
        }
        log.info("externalOaTodoServiceImpl.dealTodo,dealTalkUrl={},dingTalkToken={},dingTalk={},messageResult={}", dealTalkUrl, dingTalkToken,dingTalk,messageResult);
        jsonObject = JSONObject.parseObject(content);
        if (code == 200) {
            String dealResult = String.valueOf(jsonObject.get("result"));
            if("true".equals(dealResult)) {
                //状态设为0
                int sum = 0;
                for(DingTaskVo dingTaskVo : dingTaskVos) {
                    if(operators.contains(dingTaskVo.getEmployeeId())) {
                        int count = dingTodoManager.updateStatus(0, var1.getEi(), var1.getSourceId(), dingTaskVo.getEmployeeId());
                        //更换接口后，就算全部人完成了代办，仍然显示为进行中的代办，需要查询数据库，如果状态全部为0的话，证明代办完成了，调用完成接口
                        sum += count;
                    }
                }
                List<DingTaskVo> dingTaskVos1 = dingTodoManager.getDingTaskVo(var1.getEi(), var1.getSourceId());
                if(CollectionUtils.isEmpty(dingTaskVos1)) {
                    log.info("externalOaTodoServiceImpl.dealTodo,ea={},thread sleep1.", var1.getEa());
                    //为避免此时有新增待办，调用钉钉接口成功，但是还没有入库导致完成的问题，让当前线程睡会
                    try {
                        Thread.sleep(1000L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    dingTaskVos1 = dingTodoManager.getDingTaskVo(var1.getEi(), var1.getSourceId());
                    if(CollectionUtils.isEmpty(dingTaskVos1)) {
                        log.info("externalOaTodoServiceImpl.dealTodo,ea={},Todo is complete.", var1.getEa());
                        //调用完成的接口
                        Boolean isQPSLimit1 = qpsLimitHandlerFromProvider.isQPSLimitByEa(var1.getEa());
                        if(isQPSLimit1) {
                            //限流且重试多次失败
                            log.info("externalOaTodoServiceImpl.dealTodo.qps limit.var2={}", var1);
                            return result;
                        }
                        this.updateTodoStatus(mappingEnterprise.getData(), operatorUnionId, dingTaskVos.get(0).getTaskId(), dingTalkToken);
                    }
                }
                log.info("externalOaTodoServiceImpl.dealTodo,ea={},operators={},sum={}", var1.getEa(), operators, sum);
            }
        }
        return result;
    }

    public void updateTodoStatus(DingEnterpriseResult dingEnterpriseResult, String operator, String taskId, Map<String, String> dingTalkToken) {
        Gson gson = new Gson();
        String updateToDo = DingUrl.DINGTALK_TODO + operator + "/tasks/" + taskId + "?operatorId=" + operator;
        Map<String, Object> dingTalk = Maps.newHashMap();
        boolean flag = true;
        dingTalk.put("done", flag);
        HttpResponseMessage updateToDoMessageResult = null;
        JSONObject jsonObject = null;
        Integer code = null;
        String content = null;
        if(dingEnterpriseResult.getClientIp().equalsIgnoreCase(ConfigCenter.DING_CLIENT_IP)) {
            Map<String, Object> messageArg = new HashMap<>();
            messageArg.put("url", updateToDo);//钉钉发送消息的url
            messageArg.put("type", "PUT");
            messageArg.put("header", gson.toJson(dingTalkToken));
            messageArg.put("token", dingEnterpriseResult.getToken());
            messageArg.put("data", dingTalk);
            String clientUrl = DingRequestUtil.appendProxyHttpUrl(dingEnterpriseResult.getClientIp());
            updateToDoMessageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(clientUrl, new HashMap<>(),gson.toJson(messageArg));
            JSONObject msgJsonObject = JSONObject.parseObject(updateToDoMessageResult.getContent());
            code = (Integer) msgJsonObject.get("errorCode");
            content = msgJsonObject.get("data").toString();
        } else {
            updateToDoMessageResult = DingRetryHandlerFromProvider.sendOkHttp3Put(updateToDo, dingTalkToken, gson.toJson(dingTalk));
            code = updateToDoMessageResult.getStatusCode();
            content = updateToDoMessageResult.getContent();
        }
        jsonObject = JSONObject.parseObject(content);
        if (code == 200) {
            String dealResult = String.valueOf(jsonObject.get("result"));
            if("true".equals(dealResult)) {
                log.info("externalOaTodoServiceImpl.updateTodoStatus,operator={},taskId={}", operator, taskId);
            }
        }
    }

    public DeleteTodoResult deleteTodo(DeleteTodoArg var1, Result<DingEnterpriseResult> mappingEnterprise) {
        //删除代办
        log.info("ExternalOaTodoServiceImpl.deleteTodo: deleteToDO messageArg:{}", var1);
        DeleteTodoResult result = new DeleteTodoResult();
        result.setCode(200);
//        Result<DingEnterpriseResult> mappingEnterprise = dingEnterpriseManager.queryEnterpriseInfoByEi(var1.getEi());
//        if (Objects.isNull(mappingEnterprise) || Objects.isNull(mappingEnterprise.getData())) {
//            log.warn("ei={}.", var1.getEi());
//            return result;
//        }
//        if(!(mappingEnterprise.getData().getAlertStatus().equals(AlertStatusEnum.TODO_STATUS.getStatus()) || mappingEnterprise.getData().getAlertStatus().equals(AlertStatusEnum.TODO_AND_REMIND_STATUS.getStatus()))){
//            List<DingTaskVo> dingTaskVo = dingTodoManager.getDingTaskVo(var1.getEi(), var1.getSourceId());
//            if(CollectionUtils.isEmpty(dingTaskVo)) {
//                //已经关闭就不再发送crm待办消息
//                log.info("externalOaTodoServiceImpl.createTodo,crm alert status was closed arg:{}",var1);
//                result.setMessage("crm alert was closed");
//                return result;
//            }
//        }

        //去掉crm与钉钉无绑定的人员的账号
        List<Integer> deleteEmployeeIds = new LinkedList<>();
        for(int i = 0; i < var1.getDeleteEmployeeIds().size(); i ++) {
            int receiverCount = dingMappingEmployeeDao.findByEmployeeId(var1.getEi(), var1.getDeleteEmployeeIds().get(i));
            if(receiverCount >= 1) {
                deleteEmployeeIds.add(var1.getDeleteEmployeeIds().get(i));
            }
        }
        if(CollectionUtils.isEmpty(deleteEmployeeIds)) {
            log.info("the receivers is not binded.");
            return result;
        }
        log.info("externalOaTodoServiceImpl.deleteTodo.ea={},receiverIds={}.", var1.getEa(), deleteEmployeeIds);
//        Boolean isQPSLimit = qpsLimitHandler.isQPSLimitByEa(var1.getEa());
//        if(isQPSLimit) {
//            //限流且重试多次失败
//            log.info("externalOaTodoServiceImpl.deleteTodo.qps limit.var1={}", var1);
//            return result;
//        }
        String accessToken = cache.get(String.valueOf(var1.getEi()));
        Map<String, String> dingTalkToken = new HashMap<>();
        dingTalkToken.put("x-acs-dingtalk-access-token", accessToken);
        //走更新
        List<DingTaskVo> dingTaskVo = dingTodoManager.getDingTaskVo(var1.getEi(), var1.getSourceId());
        if(CollectionUtils.isEmpty(dingTaskVo)){
            log.info("externalOaTodoServiceImpl.deleteTodo.receiverIds is null");
            return result;
        }
        List<DingMappingEmployeeResult> employeeLists = dingMappingEmployeeDao.batchGetDingEmployeesByFsIds(var1.getEi(), deleteEmployeeIds);
        if(CollectionUtils.isEmpty(employeeLists)) {
            log.info("externalOaTodoServiceImpl.deleteTodo.employeeLists is null.");
            return result;
        }
        List<String> userUnionIds = employeeLists.stream().map(DingMappingEmployeeResult::getDingUnionId).collect(Collectors.toList());
        int operatorId = employeeLists.get(0).getEmployeeId();
        String operatorUnionId = employeeLists.get(0).getDingUnionId();
        log.info("externalOaTodoServiceImpl.deleteTodo.ea={},userUnionIds={}.", var1.getEa(), userUnionIds);

        Map<String, Object> dingTalk = Maps.newHashMap();
        String dealTalkUrl = DingUrl.DINGTALK_TODO + operatorUnionId + "/tasks/sources/" + var1.getSourceId();
        //调用第三方接口
        Gson gson = new Gson();
        Boolean isQPSLimit3 = qpsLimitHandlerFromProvider.isQPSLimitByEa(var1.getEa());
        if(isQPSLimit3) {
            //限流且重试多次失败
            log.info("externalOaTodoServiceImpl.deleteTodo.qps limit.var1={}", var1);
            return result;
        }

        try {
            for (int crmUserId : deleteEmployeeIds) {//按照Userid分开上报是为了能快速识别哪些userid用了最多的API
                DingAPICallNumer dumpLog = DingAPICallNumer.builder().tenantId(String.valueOf(var1.getEi()))
                        .bizName(var1.getBizType()).title("").senderId(0).appid(var1.getAppId()).crmUserId(crmUserId)
                        .objectApiName(var1.getExtraDataMap().get("objectApiName")).objectId(var1.getExtraDataMap().get("objectId"))
                        .functionName("deleteTodo")
                        .build();
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            }
        } catch (Exception ex) {}

        HttpResponseMessage messageResult = null;
        JSONObject jsonObject = null;
        Integer code = null;
        String content = null;
        if(mappingEnterprise.getData().getClientIp().equalsIgnoreCase(ConfigCenter.DING_CLIENT_IP)) {
            Map<String, Object> messageArg = new HashMap<>();
            messageArg.put("url", dealTalkUrl);//钉钉发送消息的url
            messageArg.put("type", "GET");
            messageArg.put("header", gson.toJson(dingTalkToken));
            messageArg.put("token", mappingEnterprise.getData().getToken());
            String clientUrl = DingRequestUtil.appendProxyHttpUrl(mappingEnterprise.getData().getClientIp());
            messageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(clientUrl, new HashMap<>(),gson.toJson(messageArg));
            JSONObject msgJsonObject = JSONObject.parseObject(messageResult.getContent());
            code = (Integer) msgJsonObject.get("errorCode");
            content = msgJsonObject.get("data").toString();
        } else {
            messageResult = DingRetryHandlerFromProvider.sendOkHttp3Get(dealTalkUrl, dingTalkToken, new HashMap<>());
            code = messageResult.getStatusCode();
            content = messageResult.getContent();
        }
        log.info("externalOaTodoServiceImpl.deleteTodo.ea={},dealTalkUrl={},messageResult={}.", var1.getEa(), dealTalkUrl, messageResult);
        jsonObject = JSONObject.parseObject(content);
        if (code == 200) {
            String talkId = String.valueOf(jsonObject.get("id"));
            String executorIds = JSONArray.toJSONString(jsonObject.get("executorIds"));

            // 将json字符串转换为list集合
            List<String> cardVos = new LinkedList<>(JSONArray.parseArray(executorIds, String.class));
            for (int i = 0; i < cardVos.size(); i++) {
                cardVos.removeAll(userUnionIds);
            }
            String participantIds = JSONArray.toJSONString(jsonObject.get("participantIds"));
            // 将json字符串转换为list集合
            List<String> cardVos1 = new LinkedList<>(JSONArray.parseArray(participantIds, String.class));
            for (int i = 0; i < cardVos1.size(); i++) {
                cardVos1.removeAll(userUnionIds);
            }
            Boolean flag = false;
            dingTalk.put("done", flag);
            dingTalk.put("executorIds",cardVos);
            dingTalk.put("participantIds",cardVos1);
            String updateToDo = DingUrl.DINGTALK_TODO + operatorUnionId + "/tasks/" + talkId + "?operatorId=" + operatorUnionId;
            Boolean isQPSLimit1 = qpsLimitHandlerFromProvider.isQPSLimitByEa(var1.getEa());
            if(isQPSLimit1) {
                //限流且重试多次失败
                log.info("externalOaTodoServiceImpl.deleteTodo.qps limit.var2={}", var1);
                return result;
            }
            HttpResponseMessage updateToDoMessageResult = null;
            Integer updateCode = null;
            if(mappingEnterprise.getData().getClientIp().equalsIgnoreCase(ConfigCenter.DING_CLIENT_IP)) {
                Map<String, Object> messageArg = new HashMap<>();
                messageArg.put("url", updateToDo);//钉钉发送消息的url
                messageArg.put("type", "PUT");
                messageArg.put("header", gson.toJson(dingTalkToken));
                messageArg.put("token", mappingEnterprise.getData().getToken());
                messageArg.put("data", dingTalk);
                String clientUrl = DingRequestUtil.appendProxyHttpUrl(mappingEnterprise.getData().getClientIp());
                updateToDoMessageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(clientUrl, new HashMap<>(),gson.toJson(messageArg));
                JSONObject msgJsonObject = JSONObject.parseObject(updateToDoMessageResult.getContent());
                updateCode = (Integer) msgJsonObject.get("errorCode");
            } else {
                updateToDoMessageResult = DingRetryHandlerFromProvider.sendOkHttp3Put(updateToDo, dingTalkToken, gson.toJson(dingTalk));
                updateCode = updateToDoMessageResult.getStatusCode();
            }
            log.info("externalOaTodoServiceImpl.deleteTodo.ea={},dingTalk={},updateToDo={},updateToDoMessageResult={}.", var1.getEa(), dingTalk, updateToDo, updateToDoMessageResult);
            if (updateCode == 200) {
                int count = dingTodoManager.updateExecutor(deleteEmployeeIds, var1.getEi(), var1.getSourceId());
                log.warn("externalOaTodoServiceImpl.deleteTodo: 更新代办成功，deleteEmployeeId={}，count={}，更新代办成功.", operatorId, count);
                //增加逻辑，客户删除了代办任务，把状态设置为完成
                List<DingTaskVo> dingTaskVo1 = dingTodoManager.getDingTaskVo(var1.getEi(), var1.getSourceId());
                if(CollectionUtils.isEmpty(dingTaskVo1)) {
                    //调用完成的接口
                    Boolean isQPSLimit2 = qpsLimitHandlerFromProvider.isQPSLimitByEa(var1.getEa());
                    if(isQPSLimit2) {
                        //限流且重试多次失败
                        log.info("externalOaTodoServiceImpl.deleteTodo.qps limit.var3={}", var1);
                        return result;
                    }
                    this.updateTodoStatus(mappingEnterprise.getData(), operatorUnionId, talkId, dingTalkToken);
                }
            } else {
                log.warn("ExternalOaTodoServiceImpl.deleteTodo: 更新代办失败，messageArg={}，userId={}.", dingTalk, deleteEmployeeIds);
            }
        }
        return result;
    }
}
