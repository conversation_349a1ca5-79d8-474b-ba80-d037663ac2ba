package com.facishare.open.ding.provider.utils;



import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import java.util.List;
import java.util.Set;


/**
 * Created by fengyh on 2020/11/24.
 */
@Slf4j
public class ReorderDeptUtils {

    public  static   Dept getParentDepartment(List<Dept> depts, Long departmentId) {
        for(Dept dept : depts) {
            if(dept.getId().equals(departmentId)) {
                return dept;
            }
        }
        return null;
    }

    //获取目录树第一级的节点
    public static List<Dept> getRootChildDeptList(List<Dept> dingDeptList) {
        //父部们为null或者父部们不在dingDeptList中
        List<Dept> result = Lists.newArrayList();
        for(Dept dept: dingDeptList) {
            if(dept.getParentid() == null) {
                result.add(dept);
            } else {
                Dept parentDept = getParentDepartment(dingDeptList, dept.getParentid());
                if(null == parentDept) { //父节点不在列表中，那么当前接单属于第一层级别的节点。
                    result.add(dept);
                }
            }
        }
        return result;
    }

    //获取父节点的id为parentId的所有节点
    public static  List<Dept> getAllChildDeptList(List<Dept> dingDeptList, Long parentId) {
        List<Dept> result = Lists.newArrayList();
        for(Dept dept: dingDeptList) {
            if(dept.getParentid().equals(parentId)) {
                result.add(dept);
            }
        }
        return result;
    }

    public  static  List<Dept> reorderDingdingDepartments(List<Dept> dingDeptList) {
        List<Dept> newList = Lists.newArrayList();
        int parentIndex = 0; //newlist中待查找子节点的父节点index

        List<Dept> dingRootChildDeptList = getRootChildDeptList(dingDeptList);
        newList.addAll(dingRootChildDeptList);

        while (!dingDeptList.isEmpty()) {
           while(parentIndex < newList.size()) {
               Dept curParentDept = newList.get(parentIndex);
               List<Dept> curLevelChildList = getAllChildDeptList(dingDeptList, curParentDept.getId());
               newList.addAll(curLevelChildList);
               parentIndex++;
           }
            dingDeptList.removeAll(newList);
        }


        removeDupName(newList);
        return  newList;
    }

    public static void removeDupName(List<Dept> deptList) {
        Set<String> deptNameSet = Sets.newHashSet();
        int postfix = 0;
        for(Dept dept: deptList) {
            if(deptNameSet.contains(dept.getName())) {
                dept.setName(dept.getName() + "__"+postfix++);
            };
            deptNameSet.add(dept.getName());
        }
    }


    public static void main(String args[]) {

        List<Dept> dingDeptList =  DingRequestUtil.queryDeptList("https://www.ceshi112.com/dingtalk/",
                "ding5tbqg7zsssifrucv", "PpYTRUqaFdrYOuR32AOpYwHtgoxHzdYL2_g_mWu2cNNXOZ8sRCuaqDMFaTRmAWgG",
                "NyB0KLZ0oEwvh8aRqrz1r67llBFg4MH2fy7rElNYcG2A9z0qurpLAvpmZ4NB8pSJ3wj04El0iM1Uw5+E8uym1IasRaLGPI8ijnoP76Ry2Uyj4tVMyiKblbHTf3T4StbimeArN8ZXhqZZjrsXjAspHOMLVAtKBpeIw2nxPwtdup8=", "1");

        List<Dept> dingDeptList2 = Lists.newArrayList();
        Dept d1 = new Dept();
        Dept d2 = new Dept();
        Dept d3 = new Dept();
        Dept d4 = new Dept();
        Dept d5 = new Dept();
        Dept d6 = new Dept();
        Dept d7 = new Dept();
        Dept d8 = new Dept();
        Dept d9 = new Dept();
        d1.setId(1L);
        d2.setId(2L);
        d3.setId(3L);
        d4.setId(4L);
        d5.setId(5L);
        d6.setId(6L);
        d7.setId(7L);
        d8.setId(8L);
        d9.setId(9L);
        d1.setParentid(null);
        d2.setParentid(1L);
        d3.setParentid(1L);
        d4.setParentid(2L);
        d5.setParentid(2L);
        d6.setParentid(2L);
        d7.setParentid(3L);
        d8.setParentid(3L);
        d9.setParentid(6L);
/**
        dingDeptList.add(d8);
        dingDeptList.add(d4);
        dingDeptList.add(d5);
        dingDeptList.add(d1);
        dingDeptList.add(d2);
        dingDeptList.add(d6);
        dingDeptList.add(d3);
        dingDeptList.add(d7);
        dingDeptList.add(d9);
        **/
        List<Dept> result = reorderDingdingDepartments(dingDeptList);
        //System.out.println("ret: {}" + result);;
        log.info("get order result:{} ", new Gson().toJson(result));


    }
}
