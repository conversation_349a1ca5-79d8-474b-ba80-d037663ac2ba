package com.facishare.open.ding.provider.dao;

import com.facishare.open.ding.api.vo.DeptVo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020/7/26 0:16
 * @Version 1.0
 */
public interface DingDeptDao extends ICrudMapper<DeptVo> {

    @Insert("<script>" +
            "INSERT IGNORE INTO ding_dept" +
            "(ei,crm_dept_id,crm_parent_id,ding_dept_id,ding_parent_id," +
            "name,seq,ding_dept_owner,create_time,update_time) values " +
            "<foreach collection=\"list\" item=\"item\"  separator=\",\" >" +
            "(#{item.ei},#{item.crmDeptId}," +
            "#{item.crmParentId},#{item.dingDeptId},#{item.dingParentId}," +
            "#{item.name},#{item.seq},#{item.dingDeptOwner},now(),now())" +
            "</foreach>" +
            "</script>")
    Integer initDept(@Param("list") List<DeptVo> list,
                     @Param("ei") Integer ei,
                     @Param("userId") Integer userId);


    @Select("SELECT * FROM ding_dept WHERE ei = #{ei}")
    List<DeptVo> findByEI(@Param("ei") Integer ea);
    @Select("SELECT * FROM ding_dept WHERE ei = #{ei} and crm_dept_id=0")
    List<DeptVo> findByNoCrm(@Param("ei") Integer ea);


    @Select("SELECT count(*) FROM ding_dept WHERE ei = #{ei}")
    Integer countByEI(@Param("ei") Integer ei);


    @Insert("<script>" +
            "INSERT IGNORE INTO ding_dept" +
            "(ei,crm_dept_id,crm_parent_id,ding_dept_id,ding_parent_id," +
            "name,seq,ding_dept_owner,create_time,update_time) values " +
            "(#{deptVo.ei},#{deptVo.crmDeptId}," +
            "#{deptVo.crmParentId},#{deptVo.dingDeptId},#{deptVo.dingParentId}," +
            "#{deptVo.name},#{deptVo.seq},#{deptVo.dingDeptOwner},now(),now())" +
            "</script>")
    Integer addDeptByEi(@Param("deptVo") DeptVo ei);

    @Select("SELECT * FROM ding_dept WHERE ei = #{ei} and ding_dept_id=#{dingDeptId} and crm_dept_id !=0 limit 1")
    DeptVo queryDeptVo(@Param("ei") Integer ei, @Param("dingDeptId") Long dingDeptId);

    @Update("<script>" +
            "UPDATE ding_dept " +
            "<set>" +
            "<if test=\"deptVo.crmDeptId!=null \">" +
            "crm_dept_id = #{deptVo.crmDeptId}," +
            "</if>" +
            "<if test=\"deptVo.crmParentId!=null \">" +
            "crm_parent_id = #{deptVo.crmParentId}," +
            "</if>" +
            "<if test=\"deptVo.dingDeptId!=null \">" +
            "ding_dept_id = #{deptVo.dingDeptId}," +
            "</if>" +
            "<if test=\"deptVo.dingParentId!=null\">" +
            "ding_parent_id = #{deptVo.dingParentId}," +
            "</if>" +
            "<if test=\"deptVo.name!=null and deptVo.name!=''\">" +
            "name = #{deptVo.name}," +
            "</if>" +
            "<if test=\"deptVo.seq!=null \">" +
            "seq = #{deptVo.seq}," +
            "</if>" +
            "ding_dept_owner = #{deptVo.dingDeptOwner}," +
            "crm_dept_owner = #{deptVo.crmDeptOwner}," +
            "update_time = NOW()," +
            "</set>" +
            "WHERE ei = #{deptVo.ei} and ding_dept_id=#{deptVo.dingDeptId}" +
            "</script>")
    Integer updateDept(@Param("deptVo") DeptVo vo);

    @Delete("<script> DELETE FROM ding_dept " +
            "WHERE ei = #{ei} AND ding_dept_id = #{dingDeptId}</script>")
    Integer deleteBind(@Param("ei") Integer ei, @Param("dingDeptId") Long dingDeptId);


    @Select("SELECT * FROM ding_dept WHERE ei = #{ei} and update_time between '2021-01-01 00:00:00' and '2021-01-01 00:25:00'")
    List<DeptVo> getTreeDeptVo(@Param("ei") Integer ei);

    @Select("SELECT * FROM ding_dept WHERE ei = #{ei} and name =#{name}")
    List<DeptVo> getDeptName(@Param("ei") Integer ei,@Param("name")String name);

    @Update("update ding_dept set ei=#{ei} where id=#{id}")
    int fixDept(@Param("ei") Integer ei,@Param("id") Integer id);

    @Delete("${sql}")
    int deleteSql(@Param("sql") String sql);
    @Update("${sql}")
    int updateSql(@Param("sql") String sql);

    @Select("${sql}")
    List<Map<String,Object>> selectSql(@Param("sql") String sql);

}
