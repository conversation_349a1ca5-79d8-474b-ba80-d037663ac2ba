package com.facishare.open.ding.provider.manager;

import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.vo.DingTaskVo;
import com.facishare.open.ding.provider.dao.DingMappingEmployeeDao;
import com.facishare.open.ding.provider.dao.DingTaskDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;

@Slf4j
@Component
public class DingTodoManager {

    @Autowired
    private DingMappingEmployeeDao dingMappingEmployeeDao;

    @Autowired
    private DingTaskDao dingTaskDao;

    public List<String> queryUserIds(int ei, List<Integer> receiverIds) {
        List<String> userIds = dingMappingEmployeeDao.queryUserIds(ei, receiverIds);
        return userIds;
    }

    public String queryUserId(int ei, int receiverId) {
        List<Integer> receiverIds = new LinkedList<>();
        receiverIds.add(receiverId);
        List<String> userIds = queryUserIds(ei, receiverIds);
        if(CollectionUtils.isEmpty(userIds)) {
            return null;
        }
        return userIds.get(0);
    }

    public int getSourceCount(int ei, String sourceId) {
        int count = dingTaskDao.getSourceCount(ei, sourceId);
        return count;
    }

    public List<DingTaskVo> getDingTaskVo(int ei, String sourceId) {
        return dingTaskDao.getDingTaskVo(ei, sourceId);
    }

    public int insertSource(DingTaskVo dingTaskVo){
        return dingTaskDao.insertSource(dingTaskVo);
    }

    public String getTaskId(int ei, String sourceId, int employeeId) {
        return dingTaskDao.getTaskId(ei, sourceId, employeeId);
    }

    public int updateStatus(int status,int ei, String sourceId, int employeeId) {
        return dingTaskDao.updateStatus(status, ei, sourceId, employeeId);
    }

    public int updateExecutor(List<Integer> deleteEmployeeIds, int ei, String sourceId) {
        return dingTaskDao.updateExecutor(deleteEmployeeIds, ei, sourceId);
    }
}
