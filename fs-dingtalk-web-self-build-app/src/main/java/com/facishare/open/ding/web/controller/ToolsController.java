package com.facishare.open.ding.web.controller;

import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.ToolsService;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.web.manager.ExcelListener.BaseListener;
import com.facishare.open.ding.web.manager.FileManager;
import com.facishare.open.ding.web.manager.excel.ReadExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>钉钉云工具类接口</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2013-07-03 11:52
 */
@CrossOrigin
@Slf4j
@RestController
@RequestMapping("/tools")
public class ToolsController {
    @Resource
    private ToolsService toolsService;
    @Autowired
    private FileManager fileManager;

    @PostMapping(value = "/employee/bindEmpByExcel")
    public Result<Void> bindEmpByExcel(@RequestParam(value = "sheetName", required = false) String sheetName, MultipartFile file) {
        byte[] bytes = new byte[0];
        try {
            bytes = file.getBytes();
            InputStream inputStream = new ByteArrayInputStream(bytes);
            ReadExcel.Arg<Map<Integer, String>> arg = new ReadExcel.Arg<>();
            BaseListener<Map<Integer, String>> listen = new BaseListener<Map<Integer, String>>() {
            };
            arg.setExcelListener(listen);
            arg.setInputStream(inputStream);
            if(StringUtils.isNotEmpty(sheetName)) {
                arg.setSheetName(sheetName);
            }
            fileManager.readExcelBySheetName(arg);
            if (CollectionUtils.isEmpty(listen.getDataList())) {
                log.info("ToolsController.bindEmpByExcel,listen is emp.");
                return Result.newSuccess();
            }
            List<DingMappingEmployeeResult> employeeResults = listen.getDataList().stream().map(v -> {
                DingMappingEmployeeResult result = new DingMappingEmployeeResult();
                result.setEi(Integer.valueOf(v.get(0)));
                result.setEmployeePhone(v.get(1));
                result.setEmployeeName(v.get(2));
                result.setEmployeeId(Integer.valueOf(v.get(3)));
                result.setDingEmployeeId(v.get(4));
                result.setEmployeeStatus(1);
                result.setBindStatus(2);
                return result;
            }).collect(Collectors.toList());
            log.info("CloudToolsController.updateDeptBind,employeeResults={}.", employeeResults);
            toolsService.bindEmpByExcel(employeeResults);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.newSuccess();
    }
}
