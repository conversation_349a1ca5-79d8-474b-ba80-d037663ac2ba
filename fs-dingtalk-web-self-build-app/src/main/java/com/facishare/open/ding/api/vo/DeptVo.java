package com.facishare.open.ding.api.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020/7/24 19:35
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class DeptVo implements Serializable {
    private Long id;
    private Integer ei;
    private Integer crmDeptId;
    private Integer crmParentId;
    private Long dingDeptId;
    private Long dingParentId;
    private String name;
    private Integer seq;
    //钉钉的部门负责人
    private String dingDeptOwner;
    //crm的部门负责人
    private Integer crmDeptOwner;
    private Date createTime;
    private Date updateTime;

    public DeptVo (Integer ei,String name,Integer crmDeptOwner,Integer crmParentId){
        this.ei=ei;
        this.crmParentId=crmParentId;
        this.name=name;
        this.crmDeptOwner=crmDeptOwner;
    }

    public DeptVo (Integer ei,String name,Integer crmDeptOwner,Integer crmParentId,Integer crmDeptId){
        this.ei=ei;
        this.crmParentId=crmParentId;
        this.crmDeptId=crmDeptId;
        this.name=name;
        this.crmDeptOwner=crmDeptOwner;
    }


}
