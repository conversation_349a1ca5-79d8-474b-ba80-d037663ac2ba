package com.facishare.open.ding.provider.manager;

import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.provider.dao.DingDeptDao;
import com.facishare.open.ding.provider.entity.DingDeptEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.omg.CORBA.INTERNAL;
import org.omg.PortableInterceptor.INACTIVE;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/7/26 12:34
 * @Version 1.0
 */
@Slf4j
@Component
public class DingDeptMananger {
    @Autowired
    private DingDeptDao dingDeptDao;

    /**
     * 初始化部门映射
     *
     * @param list
     * @param ei
     * @param userId
     * @return
     */
    public Integer initDeptManager(List<DeptVo> list, Integer ei, Integer userId) {
        log.info("list:{},ei:{},userId",list,ei,userId);
        return dingDeptDao.initDept(list, ei, userId);
    }

    /**
     * 根据ei查询部门映射
     */
    public List<DeptVo> getDeptByEI(Integer ei) {
        return dingDeptDao.findByEI(ei);
    }
    /**
     * 查询没有创建成功的部门
     */
    public List<DeptVo> findByNoCrm(Integer ei) {
        return dingDeptDao.findByNoCrm(ei);
    }



    /**
     * 查询部门总数
     */
    /**
     * 根据ei查询部门映射
     */
    public Integer countByEi(Integer ei) {
        return dingDeptDao.countByEI(ei);
    }

    /**
     * 插入部门
     */
    /**
     * 添加部门
     */
    public Integer addDeptByEi(DeptVo vo) {
        return dingDeptDao.addDeptByEi(vo);
    }

    /**
     * 根据dingDeptID查询
     *
     * @param ei
     * @param deptId
     * @return
     */
    public DeptVo queryByDingId(Integer ei, Long deptId) {
        return dingDeptDao.queryDeptVo(ei, deptId);
}

    /**
     * 修改部门信息
     */
    public Result<Integer> updateDept(DeptVo vo) {
        if (ObjectUtils.isEmpty(vo)) {
            log.info("updateDept dingDeptId params error :{}", vo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        log.info(" updateDept dingDeptId params  :{}", vo);
        Integer count = dingDeptDao.updateDept(vo);
        return Result.newSuccess(count);
    }

    /**
     * 删除部门
     */
    public Result<Integer> deleteDept(Integer ei, Long dingDeptId) {
        if (dingDeptId == null) {
            log.info("deleteDept dingDeptId params error :{}", dingDeptId);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        Integer count = dingDeptDao.deleteBind(ei, dingDeptId);
        return Result.newSuccess(count);
    }

    /**
     * 按照名字查询部门信息
     */
    public List<DeptVo> getDeptName(Integer ei,String deptName){
      return   dingDeptDao.getDeptName(ei,deptName);
    }

    /**
     * 查询非法的根级部门信息的部门
     */
  public List<DeptVo> getTreeDept(Integer ei){
      return dingDeptDao.getTreeDeptVo(ei);
  }

    /**
     * 更新部门ei(处理脏数据）
     */
    public Integer fixDept(Integer ei,Integer ids){

        Integer count=0;
        count+= dingDeptDao.fixDept(ei,ids);
        return count;
    }

}
