package com.facishare.open.ding.provider.task;

import com.facishare.open.ding.provider.dao.DingEnterpriseDao;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.organization.adapter.api.model.biz.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.GetDepartmentDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>映射组织处理</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-05-15 19:31
 */
@Slf4j
@Component
public class OrgMappingManager {
    @Autowired
    private EmployeeService employeeAdapterService;

    @Autowired
    private DepartmentProviderService departmentProviderService;

    @Autowired
    private DingEnterpriseDao dingEnterpriseDao;

    private static final String SINGLE_ORG = "999999";

    private static final Integer SINGLE_TYPE = 0;

    /**
     * 查询员工的一级部门编号
     * @param ei
     * @param fsOwner
     * @return
     */
    public Result<String> getKcOrgByOwner(Integer ei,Integer fsOwner){
        //查询组织绑定关系，获取该纷享组织对应的cloud组织
//        List<OrgMapRelation> relations = dingEnterpriseDao.findOrgMappingByEI(ei);
//        String kcOrg = "";
//        Map<String,String> orgMaps = new HashMap<>();
//        for (OrgMapRelation org : relations){
//            orgMaps.put(org.getFsOrg(),org.getKcOrg());
//        }
//        if (orgMaps.size() == 1
//                && relations.get(0).getRegularType() == SINGLE_TYPE
//                && orgMaps.containsKey(SINGLE_ORG)){
//            kcOrg = orgMaps.get(SINGLE_ORG);
//            return Result.newSuccess(kcOrg);
//        }

        //根据纷享负责人查询其所在一级部门编号
        GetEmployeeDtoArg employeeDtoArg = new GetEmployeeDtoArg();
        employeeDtoArg.setEmployeeId(fsOwner);
        employeeDtoArg.setEnterpriseId(ei);
        GetEmployeeDtoResult empResult = employeeAdapterService.getEmployeeDto(employeeDtoArg);
        Integer mainDepartmentId = empResult.getEmployee().getMainDepartmentId();

        GetDepartmentDtoArg departmentDtoArg = new GetDepartmentDtoArg();
        departmentDtoArg.setEnterpriseId(ei);
        departmentDtoArg.setDepartmentId(mainDepartmentId);
        GetDepartmentDtoResult departmentResult = departmentProviderService.getDepartmentDto(departmentDtoArg);
        List<Integer> ancestors = departmentResult.getDepartment().getAncestors();
        Integer deptLevelOne = 0;
        if (CollectionUtils.isNotEmpty(ancestors)){
            if (ancestors.size() > 1){
                deptLevelOne = ancestors.get(1);
            }else if (ancestors.size() == 1){
                deptLevelOne = mainDepartmentId;
            }
        }else {
            log.warn("get org by owner failed,ei={},fsOwner={},empResult={},departmentResult={}.",ei,fsOwner,empResult,departmentResult);
            return Result.newError(ResultCode.GET_ORG_BY_OWNER_FAILED);
        }

//        if (orgMaps.containsKey(String.valueOf(deptLevelOne))){
//            kcOrg = orgMaps.get(String.valueOf(deptLevelOne));
//            return Result.newSuccess(kcOrg);
//        }else{
//            log.warn("getKcOrgByOwner no org mapping,ei={},fsOwner={},deptLevelOne={},relations={}.",ei,fsOwner,deptLevelOne,relations);
//            return Result.newError(ResultCode.GET_ORG_BY_OWNER_FAILED);
//        }
        return null;
    }

    /**
     * 查询cloud中所以绑定的组织
     * @param ei
     * @return
     */
    public Result<String> getKcOrg(Integer ei){
//        List<OrgMapRelation> relations = dingEnterpriseDao.findOrgMappingByEI(ei);
//        if (CollectionUtils.isEmpty(relations)){
//            log.warn("getKcOrg no org mapping,ei={},relations={}",ei,relations);
//            return Result.newError(ResultCode.NO_ORG_MAPPING);
//        }
//        List<String> kcOrgs = new ArrayList<>();
//        StringBuffer stringBuffer = new StringBuffer();
//        int i = 1;
//        for (OrgMapRelation org : relations){
//            kcOrgs.add(org.getKcOrg());
//            if (i == relations.size()) {
//                stringBuffer.append("'").append(org.getKcOrg()).append("'");
//            } else {
//                stringBuffer.append("'").append(org.getKcOrg()).append("'").append(",");
//            }
//            i++;
//        }
//        String fOrgNumber = stringBuffer.toString();
//        return Result.newSuccess(fOrgNumber);
        return null;
    }


}
