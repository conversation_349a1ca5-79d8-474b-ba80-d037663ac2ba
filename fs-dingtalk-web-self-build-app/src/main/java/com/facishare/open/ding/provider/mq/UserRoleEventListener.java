package com.facishare.open.ding.provider.mq;


import com.facishare.open.ding.api.result.CreateUserEventResult;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.EnterpriseService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.organization.api.event.OrganizationChangedListener;
import com.facishare.organization.api.event.organizationChangeEvent.EmployeeChangeEvent;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @Version 1.0
 */
@Component
@Slf4j
public class UserRoleEventListener extends OrganizationChangedListener {

    @Autowired
    private EnterpriseService  enterpriseService;
    @Autowired
    private ObjectMappingService objectMappingService;


    public UserRoleEventListener() {
        super("fs-dingtalk-provider");
    }

    @Override
    protected void onEmployeeChanged(EmployeeChangeEvent event) {
        log.info("onEmployeeChanged,event={}");
        //是否有绑定关系
        Result<DingEnterpriseResult> result = enterpriseService.queryEnterpriseByEaEmpty(event.getEnterpriseAccount());
        if(!result.isSuccess() || ObjectUtils.isEmpty(result.getData())){
            return;
        }
        if(ObjectUtils.isNotEmpty(event.getOldEmployeeDto())) {
            //不是新创建人员,更新员工信息时，更新名字和手机号码
            if(!event.getNewEmployeeDto().getName().equals(event.getOldEmployeeDto().getName()) || !event.getNewEmployeeDto().getMobile().equals(event.getOldEmployeeDto().getMobile())) {
                DingMappingEmployeeResult employeeResult = new DingMappingEmployeeResult();
                employeeResult.setEi(result.getData().getEi());
                employeeResult.setEmployeeId(event.getNewEmployeeDto().getEmployeeId());
                employeeResult.setEmployeeName(event.getNewEmployeeDto().getName());
                employeeResult.setEmployeePhone(event.getNewEmployeeDto().getMobile());
                Result<Integer> integerResult = objectMappingService.updateEmployeeAccountBindInfo(employeeResult);
                log.info("UserRoleEventListener.consumeMessage,integerResult:{}", integerResult);
            }
            return;
        }

        //账号自动绑定支持模式一且需要开启账号绑定
        if(result.getData().getDevModel() == 2 || result.getData().getAutBind() == 0) {
            return;
        }
        CreateUserEventResult createUserEventResult = new CreateUserEventResult();
        createUserEventResult.setEa(result.getData().getEa());
        createUserEventResult.setEi(result.getData().getEi());
        createUserEventResult.setUser(event.getNewEmployeeDto().getEmployeeId());

        log.info("UserRoleEventListener.consumeMessage,createUserEventResult:{}", createUserEventResult);
        objectMappingService.autoSaveEmployeeAccount(createUserEventResult);
    }
}
