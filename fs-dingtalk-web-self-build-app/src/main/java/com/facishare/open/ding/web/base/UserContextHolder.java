package com.facishare.open.ding.web.base;

import org.springframework.core.NamedThreadLocal;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/17 15:14 
 * <AUTHOR> yin<PERSON>@fxiaoke.com
 * @version 1.0 
 */
public class UserContextHolder {
    public static final ThreadLocal<UserVo> userVoHolder = new NamedThreadLocal<>("UserVoHolder");

    public static ThreadLocal<UserVo> get() {
        return userVoHolder;
    }
}
