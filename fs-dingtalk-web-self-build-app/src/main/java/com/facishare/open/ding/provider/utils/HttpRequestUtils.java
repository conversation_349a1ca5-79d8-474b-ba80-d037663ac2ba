package com.facishare.open.ding.provider.utils;

import com.facishare.open.ding.provider.model.HttpResponseMessage;
import com.facishare.open.ding.provider.model.result.CrmResponseResult;
import com.facishare.open.ding.provider.model.result.KisCloudResult;
import com.facishare.open.ding.provider.model.result.KisServerResult;
import com.facishare.open.ding.provider.model.result.kis.KisServerBaseData;
import com.google.gson.JsonSyntaxException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.net.ConnectException;
import java.net.SocketTimeoutException;

/**
 * Created by system on 2018/6/19.
 */
@Slf4j
public class HttpRequestUtils {

    public static int CONNECTION_FAILED = 1;

    public static int CONNECTION_TIMEOUT = 2;

    public static int REQUEST_FAILED = 3;

    public static int JSON_PARSE_FAILED = 4;

    public static int CRM_SUCCESS = 0;

    public static Integer DING_SUCCESS = 0;

    public static int KIS_SUCCESS = 200;

    public static int KIS_TOKEN_EXPIRED = 1803;

    /**
     * 构建HttpResponseMessage
     * @param response
     * @return
     * @throws Throwable
     */
    public static HttpResponseMessage buildHttpResponseMessage(Response response) throws Exception {
        HttpResponseMessage httpResponseMessage = new HttpResponseMessage();
        httpResponseMessage.setStatusCode(response.code());
        httpResponseMessage.setMessage(response.message());
        httpResponseMessage.setContent(response.body().string());
        return httpResponseMessage;
    }

    /**
     * 根据异常构建CrmResponseResult
     * @param throwable
     * @return
     */
    public static CrmResponseResult buildFailureCrmResponseResult(Throwable throwable) {
        CrmResponseResult result = new CrmResponseResult();

        Throwable rootCause = ExceptionUtils.getRootCause(throwable);
        if (rootCause != null && rootCause instanceof ConnectException) {
            result.setErrorCode(CONNECTION_FAILED);
            result.setErrorMessage("服务器访问超时");
        } else if (rootCause != null && rootCause instanceof SocketTimeoutException) {
            result.setErrorCode(CONNECTION_TIMEOUT);
            result.setErrorMessage("服务器访问超时");
        } else if(rootCause != null && rootCause instanceof JsonSyntaxException) {
            result.setErrorCode(JSON_PARSE_FAILED);
            result.setErrorMessage("json解析对象异常");
        } else {
            result.setErrorCode(REQUEST_FAILED);
            result.setErrorMessage("服务器业务异常");
        }

        return result;
    }

    /**
     * 根据异常构建KisCloudResult
     * @param throwable
     * @return
     */
    public static KisCloudResult buildFailureKisCloudResult(Throwable throwable) {
        KisCloudResult result = new KisCloudResult();

        Throwable rootCause = ExceptionUtils.getRootCause(throwable);
        if (rootCause != null && rootCause instanceof ConnectException) {
            result.setCode(CONNECTION_FAILED);
            result.setMessage("服务器访问超时");
        } else if (rootCause != null && rootCause instanceof SocketTimeoutException) {
            result.setCode(CONNECTION_TIMEOUT);
            result.setMessage("服务器访问超时");
        } else {
            result.setCode(REQUEST_FAILED);
            result.setMessage("服务器业务异常");
        }

        return result;
    }

    /**
     * 根据异常构建KisServerResult
     * @param throwable
     * @return
     */
    public static KisServerResult<KisServerBaseData> buildFailureKisServerResult(Throwable throwable) {
        KisServerResult<KisServerBaseData> result = new KisServerResult<>();

        Throwable rootCause = ExceptionUtils.getRootCause(throwable);
        if (rootCause != null && rootCause instanceof ConnectException) {
            result.setResultCode(CONNECTION_FAILED);
            result.setErrorMessage("服务器访问超时");
        } else if (rootCause != null && rootCause instanceof SocketTimeoutException) {
            result.setResultCode(CONNECTION_TIMEOUT);
            result.setErrorMessage("服务器访问超时");
        } else {
            result.setResultCode(REQUEST_FAILED);
            result.setErrorMessage("服务器业务异常");
        }

        return result;
    }

}
