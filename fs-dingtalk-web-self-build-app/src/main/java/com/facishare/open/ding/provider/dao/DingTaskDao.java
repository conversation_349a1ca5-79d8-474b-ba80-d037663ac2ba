package com.facishare.open.ding.provider.dao;

import com.facishare.open.ding.api.vo.DingTaskVo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface DingTaskDao extends ICrudMapper<DingTaskVo> {

    @Select("SELECT count(*) from ding_source_task where ei = #{ei} and source_id = #{sourceId} and status = '1'")
    Integer getSourceCount(@Param("ei") int ei, @Param("sourceId") String sourceId);

    @Select("SELECT * from ding_source_task where ei = #{ei} and source_id = #{sourceId} and status = '1'")
    List<DingTaskVo> getDingTaskVo(@Param("ei") int ei, @Param("sourceId") String sourceId);

    @Select("SELECT task_id from ding_source_task where ei = #{ei} and source_id = #{sourceId} and employee_id = #{employeeId} and status = '1'")
    String getTaskId(@Param("ei") int ei, @Param("sourceId") String sourceId, @Param("employeeId") int employeeId);

    @Update("UPDATE ding_source_task " +
            "SET status = #{status} " +
            "where ei = #{ei} and employee_id = #{employeeId} and source_id = #{sourceId} and status = '1'")
    Integer updateStatus(@Param("status") int status, @Param("ei") int ei, @Param("sourceId") String sourceId, @Param("employeeId") int employeeId);

    @Insert("INSERT ding_source_task " +
            "(ei, employee_id, source_id, task_id, creator_employee_id) " +
            "VALUES(#{dingTaskVo.ei}, #{dingTaskVo.employeeId}, #{dingTaskVo.sourceId}, " +
            "#{dingTaskVo.taskId}, #{dingTaskVo.creatorEmployeeId})")
    Integer insertSource(@Param("dingTaskVo") DingTaskVo dingTaskVo);

    @Delete("<script>" +
            "Delete from ding_source_task where ei = #{ei} and source_id = #{sourceId} and status = '1' and employee_id in " +
            "<foreach collection='deleteEmployeeIds' item='item' separator=',' open='(' close=')' >" +
            "#{item} " +
            "</foreach> " +
            "</script>")
    Integer updateExecutor(@Param("deleteEmployeeIds") List<Integer> deleteEmployeeIds, @Param("ei") int ei, @Param("sourceId") String sourceId);
}
