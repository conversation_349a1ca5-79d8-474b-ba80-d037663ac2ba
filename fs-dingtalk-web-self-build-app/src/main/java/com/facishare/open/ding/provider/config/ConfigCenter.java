package com.facishare.open.ding.provider.config;

import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Set;

/**
 * Create by max on 2019/07/05
 **/
public class ConfigCenter {

    public static String CRM_REST_OBJ_URL="http://172.31.101.246:17263/API/v1/rest/object";
    public static Set<String> OA_GRAY_TENANTS= Sets.newHashSet();
    public static Set<String> DATA_GRAY_TENANTS= Sets.newHashSet();
    public static Set<String> CRM_TO_BIZ_TYPES= Sets.newHashSet();
    public static Set<String> WORK_ORDER_BIZ_TYPES= Sets.newHashSet();
    public static Set<String> OA_WORK_ORDER_TENANTS= Sets.newHashSet();
    public static String SYS_TODO_USER_IDS;
    public static List<String> QPS_LIMIT_CODE = Lists.newArrayList("90002", "Forbidden.AccessDenied.QpsLimitForApi", "90018", "Forbidden.AccessDenied.QpsLimitForAppkeyAndApi");
    public static String QPS_LIMIT_MAX_EA = "{\"default\":100,\"1111\":2}";
    public static Set<String> NOT_UPDATE_EA = Sets.newHashSet();
    public static Set<String> QPS_LIMIT_EA = Sets.newHashSet();
    public static String DING_CLIENT_IP;
    public static String AVA_FS_COMMON_WEBVIEW_URL = "https://www.ceshi112.com/hcrm/dingtalk/#/ava_fs_common/pages/webview/index?url={url}";
    public static String DING_FUNCTION_URL = "https://www.ceshi112.com/hcrm/dingtalk/function/";
    public static String DING_FILE_URL = "https://www.ceshi112.com/dps/preview/bypath?path={path}&showHeader=1";
    public static String DING_ATME_URL = "https://www.ceshi112.com/hcrm/dingtalk?feedId={feedId}#/feed/detail";
    public static Set<String> BI_AND_FILE_MESSAGE_EA = Sets.newHashSet("true");
    public static String XOR_SECRET_KEY;
    //密钥
    public static String BASE64_SECRET = "";
    public static String CRM_RATE_LIMIT = "{\"defaultRateLimit\":20,\"84883\":2}";
    /**
     * 灰度开始日期
     */
    public static Long GRAY_START_DATE_TIME = 1732636800000L;
    static {
        ConfigFactory.getInstance().getConfig("fs-dingtalk-provider", config -> {
            CRM_REST_OBJ_URL = config.get("CRM_REST_OBJ_URL", CRM_REST_OBJ_URL);
            OA_GRAY_TENANTS= ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("OA_GRAY_TENANTS", "")));
            DATA_GRAY_TENANTS= ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("DATA_GRAY_TENANTS", "")));
            //401,402,403,404,406,408,410,411,452,456,457,460
            String biz=config.get("CRM_TO_BIZ_TYPES");
            CRM_TO_BIZ_TYPES=  ImmutableSet.copyOf(
                    Splitter.on(",").split(config.get("CRM_TO_BIZ_TYPES", "")));
            OA_WORK_ORDER_TENANTS= ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("OA_WORK_ORDER_TENANTS", "")));
            WORK_ORDER_BIZ_TYPES=  ImmutableSet.copyOf(
                    Splitter.on(",").split(config.get("WORK_ORDER_BIZ_TYPES", "")));
            SYS_TODO_USER_IDS = config.get("SYS_TODO_USER_IDS", SYS_TODO_USER_IDS);
            QPS_LIMIT_MAX_EA = config.get("QPS_LIMIT_MAX_EA", QPS_LIMIT_MAX_EA);
            NOT_UPDATE_EA = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("NOT_UPDATE_EA", "")));
            QPS_LIMIT_EA = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("QPS_LIMIT_EA", "")));
            DING_CLIENT_IP = config.get("DING_CLIENT_IP", DING_CLIENT_IP);
            AVA_FS_COMMON_WEBVIEW_URL = config.get("AVA_FS_COMMON_WEBVIEW_URL", AVA_FS_COMMON_WEBVIEW_URL);
            DING_FUNCTION_URL = config.get("DING_FUNCTION_URL", DING_FUNCTION_URL);
            DING_FILE_URL = config.get("DING_FILE_URL", DING_FILE_URL);
            DING_ATME_URL = config.get("DING_ATME_URL", DING_ATME_URL);
            BI_AND_FILE_MESSAGE_EA = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("BI_AND_FILE_MESSAGE_EA", "")));
            XOR_SECRET_KEY = config.get("XOR_SECRET_KEY", XOR_SECRET_KEY);
            BASE64_SECRET = config.get("BASE64_SECRET", BASE64_SECRET);
            CRM_RATE_LIMIT = config.get("CRM_RATE_LIMIT", CRM_RATE_LIMIT);
            GRAY_START_DATE_TIME = config.getLong("GRAY_START_DATE_TIME", GRAY_START_DATE_TIME);
        });

    }
}