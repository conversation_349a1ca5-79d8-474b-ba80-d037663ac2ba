package com.facishare.open.ding.provider.dao;

import com.facishare.open.ding.api.result.BindFxEaResult;
import com.facishare.open.ding.provider.entity.DingEnterprise;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/10 16:25
 * @version 1.0 
 */
public interface DingEnterpriseDao extends ICrudMapper<DingEnterprise> {


    @Select("SELECT * FROM ding_enterprise WHERE ea = #{ea}")
    DingEnterprise findByEA(@Param("ea") String ea);

    @Select("SELECT * FROM ding_enterprise WHERE ei = #{ei}")
    DingEnterprise findByEI(@Param("ei") Integer ei);

    @Select("SELECT * FROM ding_enterprise WHERE ding_corp_id = #{dingCorpId} limit 1")
    DingEnterprise findByDingCorpId(@Param("dingCorpId") String dingCorpId);

    @Select("SELECT * FROM ding_enterprise WHERE app_key = #{appKey}")
    DingEnterprise queryEnterPriseByAppKey(@Param("appKey") String appKey);


    @Insert("insert ignore into ding_enterprise " +
            "(ea,ei,enterprise_name,ding_corp_id,agent_id,app_key,app_secret," +
            "redirect_app_id,redirect_app_secret,client_ip,token,is_callback,alert_status,dev_model)" +
            "values(#{dingEnterPrise.ea},#{dingEnterPrise.ei},#{dingEnterPrise.enterpriseName},#{dingEnterPrise.dingCorpId}," +
            "#{dingEnterPrise.agentId},#{dingEnterPrise.appKey},#{dingEnterPrise.appSecret},#{dingEnterPrise.redirectAppId},#{dingEnterPrise.redirectAppSecret},#{dingEnterPrise.clientIp}," +
            "#{dingEnterPrise.token},#{dingEnterPrise.isCallback},#{dingEnterPrise.alertStatus},#{dingEnterPrise.devModel})")
    Integer insertEnterPrise(@Param("dingEnterPrise") DingEnterprise dingEnterprise);

    @Update("<script>" +
            "UPDATE ding_enterprise " +
            "<set>"+
            "<if test=\"dingEnterprise.agentId != null and dingEnterprise.agentId != ''\">"+
            "agent_id = #{dingEnterprise.agentId},"+
            "</if>"+
            "<if test=\"dingEnterprise.appKey != null and dingEnterprise.appKey != ''\">"+
            "app_key = #{dingEnterprise.appKey},"+
            "</if>"+
            "<if test=\"dingEnterprise.appSecret != null and dingEnterprise.appSecret != ''\">"+
            "app_secret = #{dingEnterprise.appSecret},"+
            "</if>"+
            "<if test=\"dingEnterprise.updateBy != null and dingEnterprise.updateBy != ''\">"+
            "update_by = #{dingEnterprise.updateBy},"+
            "</if>"+
            "<if test=\"dingEnterprise.redirectAppId != null and dingEnterprise.redirectAppId != ''\">"+
            "redirect_app_id = #{dingEnterprise.redirectAppId},"+
            "</if>"+
            "<if test=\"dingEnterprise.redirectAppSecret != null and dingEnterprise.redirectAppSecret != ''\">"+
            "redirect_app_secret = #{dingEnterprise.redirectAppSecret},"+
            "</if>"+
            "<if test=\"dingEnterprise.clientIp != null and dingEnterprise.clientIp != ''\">"+
            "client_ip = #{dingEnterprise.clientIp},"+
            "</if>"+
            "<if test=\"dingEnterprise.isCallback != null\">"+
            "is_callback = #{dingEnterprise.isCallback},"+
            "</if>"+
            "<if test=\"dingEnterprise.alertStatus != null \">"+
            "alert_status = #{dingEnterprise.alertStatus},"+
            "</if>"+
            "<if test=\"dingEnterprise.allIndex != null \">"+
            "all_index = #{dingEnterprise.allIndex},"+
            "</if>"+
            "<if test=\"dingEnterprise.autBind != null \">"+
            "aut_bind = #{dingEnterprise.autBind},"+
            "</if>"+
            "<if test=\"dingEnterprise.todoType != null \">"+
            "todo_type = #{dingEnterprise.todoType},"+
            "</if>"+
            "update_time = NOW()," +
            "</set>"+
            "WHERE ea = #{dingEnterprise.ea}" +
            "</script>")
    Integer updateEnterpriseByEi(@Param("dingEnterprise") DingEnterprise dingEnterprise);

    @Select("SELECT ei, ea, enterprise_name name FROM ding_enterprise " +
            "ORDER BY create_time LIMIT #{offset}, #{limit}")
    List<BindFxEaResult> queryBindFxEa(@Param("offset") Integer offset, @Param("limit") Integer limit);
}
