package com.facishare.open.ding.provider.entity;

import lombok.Data;

import javax.persistence.Id;
import java.util.Date;

/**
 * <p>职员映射</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/7/11 16:18
 */
@Data
public class DingMappingEmployee {
    @Id
    private Long id;

    /** 纷享EI **/
    private Integer ei;

    /** 纷享职员ID**/
    private Integer employeeId;

    /** 职员状态 **/
    private Integer employeeStatus;

    /** 职员名称 **/
    private String employeeName;

    /** 职员电话 **/
    private String employeePhone;

    /** 钉钉职员ID **/
    private String dingEmployeeId;

    /** 钉钉unionID **/
    private String dingUnionId;

    /** 钉钉职员状态 **/
    private Integer dingEmployeeStatus;

    /** 钉钉职员名称 **/
    private String dingEmployeeName;

    /** 钉钉职员电话 **/
    private String dingEmployeePhone;

    /**
     * 钉钉职员邮箱
     */
    private String dingEmployeeEmail;

    /**
     * 钉钉职员职员工号
     */
    private String dingJobNumber;

    /** 钉钉部门id **/
    private Long dingDeptId;

    /** 钉钉部门名称 **/
    private String dingDeptName;

    /** 创建时间 **/
    private Date createTime;

    /** 更新时间 **/
    private Date updateTime;

    /** 创建人 **/
    private Integer createBy;

    /** 修改人 **/
    private Integer updateBy;

    /** 是否绑定 0：初始化 1：未绑定 2:绑定 3：同步的，包括未绑定和绑定的  5：异常**/
    private Integer bindStatus;

    /** 纷享员工性别 **/
    private String gender;

    /** 最后同步时间 **/
    private String lastModifyTime;
    /**CRMdept**/
    private Integer crmDeptId;
}
