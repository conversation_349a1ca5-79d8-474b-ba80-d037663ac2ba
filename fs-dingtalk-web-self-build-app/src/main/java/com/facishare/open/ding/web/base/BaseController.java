package com.facishare.open.ding.web.base;


import com.facishare.open.ding.api.exception.DingtalkException;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.web.ajax.AjaxCode;
import com.facishare.open.ding.web.ajax.AjaxResult;
import com.google.common.base.Strings;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>controller的父类</p>
 *
 * <AUTHOR> <EMAIL>
 * @version 1.0
 * @dateTime 2017/9/11 15:56
 */
public abstract class BaseController {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    public UserVo getUserVo() {
        return UserContextHolder.get().get();
    }

    protected void checkParamNotBlank(Object obj, String message) {
        if (null == obj) {
            throw new DingtalkException(AjaxCode.PARAM_ERROR, message);
        }

        if (obj instanceof String && Strings.isNullOrEmpty((String) obj)) {
            throw new DingtalkException(AjaxCode.PARAM_ERROR, message);
        }
    }

    protected void checkParamTrue(boolean trueValue, String message) {
        if (!trueValue) {
            throw new DingtalkException(AjaxCode.PARAM_ERROR, message);
        }
    }

    protected void checkParamRegex(String obj, String regex, String message) {
        checkParamNotBlank(obj, message);
        obj = obj.trim();
        if (null != regex && !obj.matches(regex)) {
            throw new DingtalkException(AjaxCode.PARAM_ERROR, message);
        }
    }

    /**
     * 异常通用处理
     *
     * @param request 请求.
     * @param e       异常
     * @throws Exception
     */
    @ExceptionHandler
    @ResponseBody
    public AjaxResult exception(HttpServletRequest request, Exception e) throws Exception {
        if (e instanceof DingtalkException) {
            logger.warn("req [ " + request.getRequestURI() + " ] " + e.getLocalizedMessage(), e);
            DingtalkException biz = (DingtalkException) e;
            return new AjaxResult(biz.getErrCode(), biz.getErrDescription());
        }
        logger.error("req [ " + request.getRequestURI() + " ] " + e.getLocalizedMessage(), e);
        return new AjaxResult(AjaxCode.BIZ_EXCEPTION, "系统异常");
    }

    /**
     * 参数绑定异常
     *
     * @param e 异常
     * @return 异常结果
     */
    @ExceptionHandler({BindException.class, MethodArgumentNotValidException.class})
    @ResponseBody
    public AjaxResult handleBindException(MethodArgumentNotValidException e) {

        return wrapperBindingResult(e.getBindingResult());
    }

    /**
     * 包装校验异常结果
     *
     * @param bindingResult 绑定结果
     * @return 异常结果
     */
    private AjaxResult wrapperBindingResult(BindingResult bindingResult) {
        StringBuilder msg = new StringBuilder();

        for (ObjectError error : bindingResult.getAllErrors()) {
            msg.append(", ");
            if (error instanceof FieldError) {
                msg.append(((FieldError) error).getField()).append(":");
            }
            msg.append(error.getDefaultMessage() == null ? "" : error.getDefaultMessage());
        }
        return new AjaxResult(ResultCode.PARAMS_ERROR.getErrorCode(), msg.substring(2));
    }

}
