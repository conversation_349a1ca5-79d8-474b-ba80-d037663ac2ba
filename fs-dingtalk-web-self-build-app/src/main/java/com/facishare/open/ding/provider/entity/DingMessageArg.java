package com.facishare.open.ding.provider.entity;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-10-16 16:27
 */
@Data
public class DingMessageArg {
    private String agent_id;
    private String userid_list;
    private Msg msg = new Msg();

    @Data
    public static class Msg{
        private String msgtype;
        private MarkDown markdown;
        private ActionCard action_card = new ActionCard();
    }

    @Data
    public static class ActionCard{
        private String title;
        private String markdown;
        private String single_title;
        private String single_url;
    }

    @Data
    @AllArgsConstructor
    public static class MarkDown{
        private String title;
        private String text;
    }

}
