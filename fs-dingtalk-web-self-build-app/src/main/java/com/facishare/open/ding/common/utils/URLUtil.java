package com.facishare.open.ding.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import sun.applet.Main;

/**
 * <p>URL处理工具类</p>
 * @dateTime 2018/7/17 10:54
 * <AUTHOR> y<PERSON><PERSON>@fxiaoke.com
 * @version 1.0
 */
public class URLUtil {

    private static final Pattern hostPattern = Pattern.compile("(?<=//|)(\\w+\\.)+\\w+", Pattern.CASE_INSENSITIVE);

    /**
     * 通过url获取域名主机部分
     * @param srcUrl 原始url地址
     * @return url中的域名部分，异常或者匹配不到情况返回 null
     */
    public static String getUrlDomain(String srcUrl) {
        if (StringUtils.isBlank(srcUrl)) {
            return null;
        }

        String hostName = null;

        Matcher matcher = hostPattern.matcher(srcUrl);
        if (matcher.find()) {
            hostName = matcher.group();
        }

        return hostName;
    }

    public static void main(String[] args) {
        System.out.println(getUrlDomain("http://fxiaoke.kingdiee.com/index.html"));
    }

}
