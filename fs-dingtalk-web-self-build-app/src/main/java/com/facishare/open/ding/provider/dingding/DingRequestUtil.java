package com.facishare.open.ding.provider.dingding;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.open.ding.api.enums.SmartworkEnum;
import com.facishare.open.ding.api.vo.FieldsEmployeeVo;
import com.facishare.open.ding.common.model.*;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.provider.constants.Constant;
import com.facishare.open.ding.provider.handler.DingRetryHandlerFromProvider;
import com.facishare.open.ding.provider.model.HttpResponseMessage;
import com.facishare.open.ding.provider.utils.HttpRequestUtils;
import com.facishare.open.ding.provider.utils.HttpUtils;
import com.facishare.open.ding.provider.utils.OkHttp3MonitorUtilsFromProvider;
import com.facishare.restful.common.StopWatch;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jayway.jsonpath.JsonPath;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>获取token</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-08-19 14:45
 */
@Slf4j
public class DingRequestUtil {

    public static String token = "";

    public static Long TOKEN_ERROR_CODE = 40014L;

    public static String appendUrl(String clientIp) {
        String clientUrl = clientIp + "proxy/proxyRequest";
        return clientUrl;
    }

    public static String appendProxyHttpUrl(String clientIp) {
        String clientUrl = clientIp + "proxy/proxyHttpRequest";
        return clientUrl;
    }

    public static String appendCallBackUrl(String clientIp) {
        String clientUrl = clientIp + "proxy/registCallBack";
        return clientUrl;
    }

    public static String appendDetpListUrl(String clientIp) {
        String clientUrl = clientIp + "proxy/queryDetpList";
        return clientUrl;
    }

    public static String appendDetpUsersUrl(String clientIp) {
        String clientUrl = clientIp + "proxy/queryDetpUsers";
        return clientUrl;
    }

    public static String appendGetUsersUrl(String clientIp) {
        String clientUrl = clientIp + "proxy/getUser";
        return clientUrl;
    }
    private static String url="https://oapi.dingtalk.com/user/listbypage?";
    public static Object proxyRequest(String url, String argJson) {
        StopWatch requestWatch=StopWatch.create("proxyRequest");
        HttpResponseMessage response = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(url, new HashMap<>(), argJson);
        log.info("proxyRequest,url={},response={}",url,response);
        //CloseableHttpResponse response = HttpUtils.httpPost(url, argJson, null);
        if (Objects.isNull(response) || StringUtils.isEmpty(response.getContent())) {
            log.warn("proxy post failed,url={},argJson={}",url,argJson);
            return null;
        }
        Object entity = response.getContent();
        requestWatch.lap("result");
        log.info("proxy request url={},argJson={},result:{}", url,argJson,entity);
        requestWatch.log();
        return entity;
    }

    public static String getToken(String clientIp, String appKey, String appSecret) throws RuntimeException {
        Gson gson = new Gson();
        String clientUrl = DingRequestUtil.appendUrl(clientIp);
        Map<String, Object> getTokenArg = new HashMap<>();
        getTokenArg.put("type", "GET");
        String getTokenUrl = DingUrl.GET_TOKEN_URL.concat("?appkey=").concat(appKey).concat("&appsecret=").concat(appSecret);
        getTokenArg.put("url", getTokenUrl);
        Object tokenResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(getTokenArg));
        JSONObject jsonObject = JSONObject.parseObject(tokenResult.toString());
        if (Objects.isNull(tokenResult)||ObjectUtils.isEmpty(jsonObject.get("errcode"))) {
            log.warn("startConnect appKey或appSecret错误.:{}",clientIp);
            return null;
        }
        if (!HttpRequestUtils.DING_SUCCESS.equals(jsonObject.get("errcode"))) {
            log.warn("startConnect appKey或appSecret错误.");
            return null;
        }
        return jsonObject.getString("access_token");
    }

    public static Result<String> getJsApiSecret(String clientIp, String accessToken) throws RuntimeException {
        Gson gson = new Gson();
        String clientUrl = DingRequestUtil.appendUrl(clientIp);
        Map<String, Object> getTokenArg = new HashMap<>();
        getTokenArg.put("type", "GET");
        String getTokenUrl = DingUrl.GET_JS_TICKET_URL.concat("?access_token=").concat(accessToken);
        getTokenArg.put("url", getTokenUrl);
        Object tokenResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(getTokenArg));
        JSONObject jsonObject = JSONObject.parseObject(tokenResult.toString());
        if (Objects.isNull(tokenResult)||ObjectUtils.isEmpty(jsonObject.get("errcode"))) {
            log.warn("startConnect appKey或appSecret错误.:{}",clientIp);
            return null;
        }
        if(Integer.valueOf(jsonObject.get("errcode").toString()).equals(40014)){
            //不合法accessToken
           return Result.newError(ResultCode.ACCESS_TOKEN_INVALID);
        }
        return Result.newSuccess(jsonObject.getString("ticket"));
    }








    public static UserDetailVo queryUserByDeptId(String clientIp,String accessToken,Integer deptId, Long agentId){
        UserDetailVo userDetailVo=new UserDetailVo();
        List<UserDetailVo.useItem> useItems=new ArrayList<>();
        Gson gson = new Gson();
        Integer offset=0;
        Integer size=100;
        do{
            Map<String, Object> messageArg = new HashMap<>();
            String clientUrl = DingRequestUtil.appendUrl(clientIp);
            StringBuilder builder=new StringBuilder();
            String queryUserUrl= builder.append(url).append("access_token=").append(accessToken).append("&department_id=").append(deptId).append("&offset=").append(offset).append("&size=").append(size).toString();
            messageArg.put("url", queryUserUrl);//查询员工信息
            messageArg.put("type", "GET");
            messageArg.put("token", "Ly2HXCIZG7/QlpFaFpUqaNxiKnzMq/eWQwVTNbjgF9Z9AcyFiQNHUvQgBir3geA0REYTme85lO6ivEcEEdIdwMk7DCy3NNv++dyE+6YT1NVGVt8/Pl3QFNUM+ph9HaN/OcJ1gwRv0t6X5i3IRTfAOpIfxs+1ng+jUc9RbyBgVm4=");
            Object queryResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));
             userDetailVo= gson.fromJson(queryResult.toString(), new TypeToken<UserDetailVo>() {}.getType());
             if(!userDetailVo.getErrcode().equals(Constant.SUCCESS_CODE)){
                 log.info("rpc code error:{}",userDetailVo);
                 return userDetailVo;
             }
            useItems.addAll(userDetailVo.getUserlist());
            //获取
            offset=size*(offset+1);
        }while (userDetailVo.getHasMore());
        if(CollectionUtils.isNotEmpty(useItems) && agentId != null) {
            useItems = useItems.stream()
                    .map(useItem -> {
                        useItem.setSexType(0);
                        useItem.setMainDepartment(useItem.getDepartment().get(0));
                        return useItem;
                    })
                    .collect(Collectors.toList());
            //每次最多查询100个员工
            Map<Integer, List<UserDetailVo.useItem>> userItemsMap = groupList(useItems);

            for (Map.Entry<Integer, List<UserDetailVo.useItem>> userItemsEntry : userItemsMap.entrySet()) {
                List<String> userIds = userItemsEntry.getValue().stream().map(UserDetailVo.useItem::getUserid).collect(Collectors.toList());
                Map<String, Object> smartworkHrmEmployeeMap = new HashMap<>();
                smartworkHrmEmployeeMap.put("agentid", agentId);
                smartworkHrmEmployeeMap.put("userid_list", String.join(",", userIds));
                smartworkHrmEmployeeMap.put("field_filter_list", (SmartworkEnum.SYS02_SEX_TYPE.getFileName() + "," + SmartworkEnum.SYS00_MAIN_DEPT_ID.getFileName()));
                SmartworkEmployeeVo smartWorkList = DingRequestUtil.smartworkHrmEmployeeResponse(clientIp, accessToken, smartworkHrmEmployeeMap);
                if(ObjectUtils.isNotEmpty(smartWorkList) && CollectionUtils.isNotEmpty(smartWorkList.getResult())) {
                    Map<String, SmartworkEmployeeVo.SmartworkEmployeeResult> smartworkEmployeeResultMap = smartWorkList.getResult().stream()
                            .collect(Collectors.toMap(SmartworkEmployeeVo.SmartworkEmployeeResult::getUserid, Function.identity(), (v1, v2) -> v1));
                    for(UserDetailVo.useItem user : userItemsEntry.getValue()) {
                        SmartworkEmployeeVo.SmartworkEmployeeResult smartworkEmployeeResult = smartworkEmployeeResultMap.get(user.getUserid());
                        if(ObjectUtils.isEmpty(smartworkEmployeeResult) || CollectionUtils.isEmpty(smartworkEmployeeResult.getField_data_list())) {
                            continue;
                        }
                        Map<String, SmartworkEmployeeVo.SmartworkEmployeeResult.FieldDataList> fieldDataListMap = smartworkEmployeeResult.getField_data_list().stream()
                                .collect(Collectors.toMap(SmartworkEmployeeVo.SmartworkEmployeeResult.FieldDataList::getField_code, Function.identity(), (v1, v2) -> v1));
                        //获取性别
                        if(ObjectUtils.isNotEmpty(fieldDataListMap.get(SmartworkEnum.SYS02_SEX_TYPE.getFileName()))) {
                            List<SmartworkEmployeeVo.SmartworkEmployeeResult.FieldDataList.FieldValueList> sexTypeList = fieldDataListMap.get(SmartworkEnum.SYS02_SEX_TYPE.getFileName()).getField_value_list();
                            if(CollectionUtils.isNotEmpty(sexTypeList)) {
                                String sexType = sexTypeList.get(0).getValue();
                                if(StringUtils.isNotEmpty(sexType)) {
                                    //0:男 1:女
                                    user.setSexType(Integer.valueOf(sexType));
                                }
                            }
                        }
                        if(ObjectUtils.isNotEmpty(fieldDataListMap.get(SmartworkEnum.SYS00_MAIN_DEPT_ID.getFileName()))) {
                            //获取主属部门
                            List<SmartworkEmployeeVo.SmartworkEmployeeResult.FieldDataList.FieldValueList> mainDeptIdList = fieldDataListMap.get(SmartworkEnum.SYS00_MAIN_DEPT_ID.getFileName()).getField_value_list();
                            if(CollectionUtils.isNotEmpty(mainDeptIdList)) {
                                String mainDeptId = mainDeptIdList.get(0).getValue();
                                if(StringUtils.isNotEmpty(mainDeptId)) {
                                    //当前架构不会处理主部门是根部门的情况，所以不处理
                                    if(!mainDeptId.equals("-1")) {
                                        user.setMainDepartment(Long.valueOf(mainDeptId));
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        userDetailVo.setUserlist(useItems);
        return userDetailVo;
    }


    //统一请求注册回调地址
    public static String registerCallBack(String clientIp, String appKey, String appSecret) throws RuntimeException {
        Gson gson = new Gson();
        Map<String, Object> messageArg = new HashMap<>();
        String accessToken = DingRequestUtil.getToken(clientIp, appKey, appSecret);
        if (StringUtils.isEmpty(accessToken)) {
            return null;
        }
        String clientUrl = DingRequestUtil.appendUrl(clientIp);
        String scopeUrl = DingUrl.REGISTER_URL.concat("?access_token=").concat(accessToken);
        messageArg.put("url", scopeUrl);//获取应用权限范围
        messageArg.put("type", "POST");
        messageArg.put("token", token);
        Map<String, Object> arg = new HashMap<>();
        List<String> tagList = new ArrayList<>();
//        ["user_add_org", "user_modify_org", "user_leave_org","org_dept_create","org_dept_modify","org_dept_remove"]
        tagList.add("user_add_org");
        tagList.add("user_modify_org");
        tagList.add("user_leave_org");
        tagList.add("org_dept_create");
        tagList.add("org_dept_modify");
        tagList.add("org_dept_remove");
        String[] tag={"user_add_org","user_modify_org","user_leave_org","org_dept_create","org_dept_modify","org_dept_remove"};
        arg.put("call_back_tag", tagList);
        arg.put("token", Constant.TOKEN);
        arg.put("aes_key", Constant.ENCODING_AES_KEY);
        JSONObject jsonObject=new JSONObject();
        jsonObject.put("call_back_tag",tag);
        jsonObject.put("token",Constant.TOKEN);
        jsonObject.put("aes_key",Constant.ENCODING_AES_KEY);
        messageArg.put("data", jsonObject);
        Object queryResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));
        if (Objects.isNull(queryResult)) {
            log.warn("注册回调地址失败,appKey={}, queryResult={}.", appKey, queryResult);
            return null;
        }
        log.info("queryDeptList result={}", queryResult);
//        JSONObject queryObj = JSONObject.parseObject(queryResult.toString());
//        JSONObject jsonObject = JSON.parseObject(queryObj.get("auth_org_scopes").toString());
//        List<Long> deptList = JSON.parseArray(jsonObject.get("authed_dept").toString(), Long.class);
//        List<String> userList = JSON.parseArray(jsonObject.get("authed_user").toString(), String.class);
//        return new ScopeVo(deptList, userList);
        return null;
    }

    //查询部门所有上级部门
    public static List<Long> getListParent(String clientIp, Long id,String accessToken){
        Gson gson = new Gson();
        Map<String, Object> messageArg = new HashMap<>();

        if (StringUtils.isEmpty(accessToken)) {
            return null;
        }
        String clientUrl = DingRequestUtil.appendUrl(clientIp);
        String scopeUrl = DingUrl.LIST_PARENT_DEPTS.concat("?access_token=").concat(accessToken).concat("&id=").concat(id.toString());
        messageArg.put("url", scopeUrl);//获取应用权限范围
        messageArg.put("type", "GET");
        messageArg.put("token", token);
        Object queryResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));
        JSONObject jsonObject = JSONObject.parseObject(queryResult.toString());
        if(ObjectUtils.isEmpty(queryResult)){
            queryResult= tryAgainRequest(clientUrl,gson.toJson(messageArg));
            if(ObjectUtils.isEmpty(queryResult)){
                log.info("get dingDept parent list fail:{}",queryResult);
                return Collections.EMPTY_LIST;
            }
        }
        List<Long> deptList = JSON.parseArray(jsonObject.get("parentIds").toString(), Long.class);
        return deptList;
    }


    public static List<Dept> queryDeptList(String clientIp, String appKey, String appSecret, String token, String id) {
        //查询所有部门
        Gson gson = new Gson();
        String queryUrl = DingRequestUtil.appendDetpListUrl(clientIp);
        Map<String, Object> queryArg = new HashMap<>();
        queryArg.put("appKey", appKey);
        queryArg.put("appSecret", appSecret);
        queryArg.put("token", token);
        queryArg.put("deptId", id);
        Object queryResult = DingRequestUtil.proxyRequest(queryUrl, gson.toJson(queryArg));
        if (ObjectUtils.isEmpty(queryResult)) {
            log.warn("查询部门列表失败,appKey={}, queryResult={}.", appKey, queryResult);
            return null;
        }
//        log.info("queryDeptList result={}", queryResult);
        List<Dept> depts = gson.fromJson(queryResult.toString(), new TypeToken<List<Dept>>() {
        }.getType());
        return depts;
    }

    public static ScopeVo queryScoreDeptEmployee(Integer ei,String clientIp, String appKey, String appSecret, String token,String accessToken) {
        //查询权限所在的部门
        Gson gson = new Gson();
        Map<String, Object> messageArg = new HashMap<>();
        if (StringUtils.isEmpty(accessToken)) {
            return null;
        }
        String clientUrl = DingRequestUtil.appendUrl(clientIp);
        String scopeUrl = DingUrl.SCOPE_URL.concat("?access_token=").concat(accessToken);
        messageArg.put("url", scopeUrl);//获取应用权限范围
        messageArg.put("type", "GET");
        messageArg.put("token", token);

        log.info("queryScoreDeptEmployee,scopeUrl={},messageArg={}",scopeUrl,messageArg);
        Object queryResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));
        log.info("queryScoreDeptEmployee,queryResult={}",queryResult);
        if (Objects.isNull(queryResult)) {
            log.warn("queryScoreDeptEmployee,查询应用范围权限失败,appKey={}, queryResult={}.", appKey, queryResult);
            return null;
        }
        log.info("queryScoreDeptEmployee,queryResult={}", queryResult);
        JSONObject queryObj = JSONObject.parseObject(queryResult.toString());
        JSONObject jsonObject = JSON.parseObject(queryObj.get("auth_org_scopes").toString());
        List<Long> deptList = JSON.parseArray(jsonObject.get("authed_dept").toString(), Long.class);
        List<String> userList = JSON.parseArray(jsonObject.get("authed_user").toString(), String.class);
        log.info("queryScoreDeptEmployee,deptList={},userList={}", deptList,userList);
        return new ScopeVo(deptList, userList);
    }

    public static Dept queryDeptDetail(String clientIp,  String accessToken, String token, Long deptID) {
        Gson gson = new Gson();
        Map<String, Object> messageArg = new HashMap<>();
        String clientUrl = DingRequestUtil.appendUrl(clientIp);
        if(StringUtils.isEmpty(accessToken))return null;
        String scopeUrl = DingUrl.DEPT_DETAIL.concat("?access_token=").concat(accessToken).concat("&id=").concat(deptID.toString());
        messageArg.put("url", scopeUrl);//查询部门信息
        messageArg.put("type", "GET");
        messageArg.put("token", token);
        Object queryResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));
        if (Objects.isNull(queryResult)) {
            log.warn("查询应用范围权限失败,appKey={}, queryResult={}.", queryResult);
            return null;
        }
        log.info("queryDeptDetail result={}", queryResult);
        JSONObject jsonObject = JSONObject.parseObject(queryResult.toString());
        if (ObjectUtils.isEmpty(jsonObject)||!jsonObject.get("errcode").equals(0)) {
            log.warn("查询部门范围权限失败, queryResult={}.", queryResult);
            return null;
        }
        JSONObject queryObj = JSONObject.parseObject(queryResult.toString());
        Dept dept = new Dept();
        dept.setName(queryObj.get("name").toString());
        //部门主管,优先钉钉部门的主管 对应 CRM部门的负责人，如果主管没有值群主有值，则取群主
        Object managerList = Optional.ofNullable(queryObj.get("deptManagerUseridList")).orElse("");
        String owner = convert(managerList.toString());
        if(StringUtils.isEmpty(owner) && StringUtils.isNotEmpty(Optional.ofNullable(queryObj.get("orgDeptOwner")).orElse("").toString())) {
            owner = queryObj.get("orgDeptOwner").toString();
        }
        dept.setDeptOwner(owner);
        dept.setId(Long.parseLong(queryObj.get("id").toString()));
        if (Integer.parseInt(queryObj.get("id").toString()) != 1) {
            dept.setParentid(Long.parseLong(queryObj.get("parentid").toString()));
        }

        return dept;
    }

    private static String convert(String managerList){
        if(StringUtils.isNotEmpty(managerList)){
            String[] split = managerList.split("\\|");
            return split[0];
        }
        return "";
    }

    public static List<User> queryDeptUser(String clientIp, Long deptId, String appKey, String appSecret, String token, String accessToken, Long agentId) {
        Gson gson = new Gson();
        String queryUrl = DingRequestUtil.appendDetpUsersUrl(clientIp);
        Map<String, Object> queryArg = new HashMap<>();
        queryArg.put("appKey", appKey);
        queryArg.put("appSecret", appSecret);
        queryArg.put("deptId", deptId);
        queryArg.put("token", token);
        Object queryResult = DingRequestUtil.proxyRequest(queryUrl, gson.toJson(queryArg));
        if (Objects.isNull(queryResult)) {
            queryResult= tryAgainRequest(queryUrl,gson.toJson(queryArg));
            if(ObjectUtils.isEmpty(queryResult)){
                log.warn("查询部门员工列表失败,appKey={}，deptId={}, queryResult={}.", appKey, deptId, queryResult);
                return null;
            }
        }
        log.info("queryDeptUser result={}", queryResult);
        List<User> users = gson.fromJson(queryResult.toString(), new TypeToken<List<User>>(){}.getType());
        if(CollectionUtils.isNotEmpty(users) && agentId != null) {
            users = users.stream()
                    .map(useItem -> {
                        useItem.setSexType(0);
                        return useItem;
                    })
                    .collect(Collectors.toList());
            //每次最多查询100个员工
            Map<Integer, List<User>> usersMap = groupList(users);
            for (Map.Entry<Integer, List<User>> usersEntry : usersMap.entrySet()) {
                List<String> userIds = usersEntry.getValue().stream().map(User::getUserid).collect(Collectors.toList());
                Map<String, Object> smartworkHrmEmployeeMap = new HashMap<>();
                smartworkHrmEmployeeMap.put("agentid", agentId);
                smartworkHrmEmployeeMap.put("userid_list", String.join(",", userIds));
                smartworkHrmEmployeeMap.put("field_filter_list", SmartworkEnum.SYS02_SEX_TYPE.getFileName());
                SmartworkEmployeeVo smartWorkList = DingRequestUtil.smartworkHrmEmployeeResponse(clientIp, accessToken, smartworkHrmEmployeeMap);
                if(ObjectUtils.isNotEmpty(smartWorkList) && CollectionUtils.isNotEmpty(smartWorkList.getResult())) {
                    Map<String, SmartworkEmployeeVo.SmartworkEmployeeResult> smartworkEmployeeResultMap = smartWorkList.getResult().stream()
                            .collect(Collectors.toMap(SmartworkEmployeeVo.SmartworkEmployeeResult::getUserid, Function.identity(), (v1, v2) -> v1));
                    for(User user : usersEntry.getValue()) {
                        SmartworkEmployeeVo.SmartworkEmployeeResult smartworkEmployeeResult = smartworkEmployeeResultMap.get(user.getUserid());
                        if(ObjectUtils.isEmpty(smartworkEmployeeResult) || CollectionUtils.isEmpty(smartworkEmployeeResult.getField_data_list())) {
                            continue;
                        }
                        Map<String, SmartworkEmployeeVo.SmartworkEmployeeResult.FieldDataList> fieldDataListMap = smartworkEmployeeResult.getField_data_list().stream()
                                .collect(Collectors.toMap(SmartworkEmployeeVo.SmartworkEmployeeResult.FieldDataList::getField_code, Function.identity(), (v1, v2) -> v1));
                        //获取性别
                        List<SmartworkEmployeeVo.SmartworkEmployeeResult.FieldDataList.FieldValueList> fieldValueList = fieldDataListMap.get(SmartworkEnum.SYS02_SEX_TYPE.getFileName()).getField_value_list();
                        if(CollectionUtils.isEmpty(fieldValueList)) {
                            continue;
                        }
                        String sexType = fieldValueList.get(0).getValue();
                        if(StringUtils.isEmpty(sexType)) {
                            continue;
                        }
                        //0:男 1:女
                        user.setSexType(Integer.valueOf(sexType));
                    }
                }
            }
        }

        return users;
    }

    public static User getUser(String clientIp, String appKey, String appSecret, String userId, String token, String accessToken, Long agentId) {
        Gson gson = new Gson();
        String queryUrl = DingRequestUtil.appendGetUsersUrl(clientIp);
        Map<String, Object> queryArg = new HashMap<>();
        queryArg.put("appKey", appKey);
        queryArg.put("appSecret", appSecret);
        queryArg.put("userId", userId);
        queryArg.put("token", token);
        Object queryResult = DingRequestUtil.proxyRequest(queryUrl, gson.toJson(queryArg));
        if (ObjectUtils.isEmpty(queryResult)) {
            log.warn("查询员工详情失败,appKey={}，userId={}，queryResult={}.", appKey, userId, queryResult);
            return null;
        }
        log.info("getUser result={}", queryResult);
        User users = gson.fromJson(queryResult.toString(), User.class);
        log.info("getUser,users={}",users);
        if(ObjectUtils.isNotEmpty(users) && agentId != null) {
            users.setSexType(0);
            Map<String, Object> smartworkHrmEmployeeMap = new HashMap<>();
            smartworkHrmEmployeeMap.put("agentid", agentId);
            smartworkHrmEmployeeMap.put("userid_list", users.getUserid());
            smartworkHrmEmployeeMap.put("field_filter_list", (SmartworkEnum.SYS02_SEX_TYPE.getFileName() + "," + SmartworkEnum.SYS00_MAIN_DEPT_ID.getFileName()));
            SmartworkEmployeeVo smartworkEmployeeVo = DingRequestUtil.smartworkHrmEmployeeResponse(clientIp, accessToken, smartworkHrmEmployeeMap);
            if(ObjectUtils.isNotEmpty(smartworkEmployeeVo) && CollectionUtils.isNotEmpty(smartworkEmployeeVo.getResult())) {
                SmartworkEmployeeVo.SmartworkEmployeeResult smartworkEmployeeResult = smartworkEmployeeVo.getResult().get(0);
                if(ObjectUtils.isNotEmpty(smartworkEmployeeResult) && CollectionUtils.isNotEmpty(smartworkEmployeeResult.getField_data_list())) {
                    Map<String, SmartworkEmployeeVo.SmartworkEmployeeResult.FieldDataList> fieldDataListMap = smartworkEmployeeResult.getField_data_list().stream()
                            .collect(Collectors.toMap(SmartworkEmployeeVo.SmartworkEmployeeResult.FieldDataList::getField_code, Function.identity(), (v1, v2) -> v1));
                    //获取性别
                    if(ObjectUtils.isNotEmpty(fieldDataListMap.get(SmartworkEnum.SYS02_SEX_TYPE.getFileName()))) {
                        List<SmartworkEmployeeVo.SmartworkEmployeeResult.FieldDataList.FieldValueList> sexTypeList = fieldDataListMap.get(SmartworkEnum.SYS02_SEX_TYPE.getFileName()).getField_value_list();
                        if(CollectionUtils.isNotEmpty(sexTypeList)) {
                            String sexType = sexTypeList.get(0).getValue();
                            if(StringUtils.isNotEmpty(sexType)) {
                                //0:男 1:女
                                users.setSexType(Integer.valueOf(sexType));
                            }
                        }
                    }
                    if(ObjectUtils.isNotEmpty(fieldDataListMap.get(SmartworkEnum.SYS00_MAIN_DEPT_ID.getFileName()))) {
                        //获取主属部门
                        List<SmartworkEmployeeVo.SmartworkEmployeeResult.FieldDataList.FieldValueList> mainDeptIdList = fieldDataListMap.get(SmartworkEnum.SYS00_MAIN_DEPT_ID.getFileName()).getField_value_list();
                        if(CollectionUtils.isNotEmpty(mainDeptIdList)) {
                            String mainDeptId = mainDeptIdList.get(0).getValue();
                            if(StringUtils.isNotEmpty(mainDeptId)) {
                                //当前架构不会处理主部门是根部门的情况，所以不处理
                                if(!mainDeptId.equals("-1")) {
                                    users.setMainDepartment(Long.valueOf(mainDeptId));
                                }
                            }
                        }
                    }
                }
            }
        }
        return users;
    }

    public static UserVo getUserDetail(String accessToken,String userId,String clientIp, Long agentId) {
        Gson gson = new Gson();
        String clientUrl = DingRequestUtil.appendUrl(clientIp);
        Map<String, Object> arg = new HashMap<>();
        arg.put("type", "GET");
        String getUserDetailUrl = DingUrl.GET_USER_DETAIL.concat("?access_token=").concat(accessToken).concat("&userid=").concat(userId);
        arg.put("url", getUserDetailUrl);
        Object queryResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(arg));
        Integer errcode= JsonPath.read(queryResult.toString(),"$.errcode");
        if (Objects.isNull(queryResult)||!errcode.equals(0)) {
            log.warn("startConnect appKey或appSecret错误.或者员工部门不在授权范围");
            return null;
        }

        UserVo users = gson.fromJson(queryResult.toString(), UserVo.class);
        log.info("getUserDetail,users={}",users);
        if(ObjectUtils.isNotEmpty(users) && agentId != null) {
            users.setSexType(0);
            users.setMainDepartment(users.getDepartment().get(0));
            Map<String, Object> smartworkHrmEmployeeMap = new HashMap<>();
            smartworkHrmEmployeeMap.put("agentid", agentId);
            smartworkHrmEmployeeMap.put("userid_list", users.getUserid());
            smartworkHrmEmployeeMap.put("field_filter_list", (SmartworkEnum.SYS02_SEX_TYPE.getFileName() + "," + SmartworkEnum.SYS00_MAIN_DEPT_ID.getFileName()));
            SmartworkEmployeeVo smartworkEmployeeVo = DingRequestUtil.smartworkHrmEmployeeResponse(clientIp, accessToken, smartworkHrmEmployeeMap);
            if(ObjectUtils.isNotEmpty(smartworkEmployeeVo) && CollectionUtils.isNotEmpty(smartworkEmployeeVo.getResult())) {
                SmartworkEmployeeVo.SmartworkEmployeeResult smartworkEmployeeResult = smartworkEmployeeVo.getResult().get(0);
                if(ObjectUtils.isNotEmpty(smartworkEmployeeResult) && CollectionUtils.isNotEmpty(smartworkEmployeeResult.getField_data_list())) {
                    Map<String, SmartworkEmployeeVo.SmartworkEmployeeResult.FieldDataList> fieldDataListMap = smartworkEmployeeResult.getField_data_list().stream()
                            .collect(Collectors.toMap(SmartworkEmployeeVo.SmartworkEmployeeResult.FieldDataList::getField_code, Function.identity(), (v1, v2) -> v1));
                    //获取性别
                    if(ObjectUtils.isNotEmpty(fieldDataListMap.get(SmartworkEnum.SYS02_SEX_TYPE.getFileName()))) {
                        List<SmartworkEmployeeVo.SmartworkEmployeeResult.FieldDataList.FieldValueList> sexTypeList = fieldDataListMap.get(SmartworkEnum.SYS02_SEX_TYPE.getFileName()).getField_value_list();
                        if(CollectionUtils.isNotEmpty(sexTypeList)) {
                            String sexType = sexTypeList.get(0).getValue();
                            if(StringUtils.isNotEmpty(sexType)) {
                                //0:男 1:女
                                users.setSexType(Integer.valueOf(sexType));
                            }
                        }
                    }
                    if(ObjectUtils.isNotEmpty(fieldDataListMap.get(SmartworkEnum.SYS00_MAIN_DEPT_ID.getFileName()))) {
                        //获取主属部门
                        List<SmartworkEmployeeVo.SmartworkEmployeeResult.FieldDataList.FieldValueList> mainDeptIdList = fieldDataListMap.get(SmartworkEnum.SYS00_MAIN_DEPT_ID.getFileName()).getField_value_list();
                        if(CollectionUtils.isNotEmpty(mainDeptIdList)) {
                            String mainDeptId = mainDeptIdList.get(0).getValue();
                            if(StringUtils.isNotEmpty(mainDeptId)) {
                                //当前架构不会处理主部门是根部门的情况，所以不处理
                                if(!mainDeptId.equals("-1")) {
                                    users.setMainDepartment(Long.valueOf(mainDeptId));
                                }
                            }
                        }
                    }
                }
            }
        }
        return users;
    }

    //TODO 查询父级部门




    public static Dept getDeptDetail(String accessToken,String clientIp,Long deptID) {
        Gson gson = new Gson();
        String clientUrl = DingRequestUtil.appendUrl(clientIp);
        Map<String, Object> arg = new HashMap<>();
        arg.put("type", "GET");
        String getUserDetailUrl = DingUrl.GET_DEPT_DETAIL.concat("?access_token=").concat(accessToken).concat("&id=").concat(deptID.toString());
        arg.put("url", getUserDetailUrl);

        Object queryResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(arg));
        if (Objects.isNull(queryResult)) {
            log.warn("startConnect appKey或appSecret错误.");
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(queryResult.toString());
        if (jsonObject.get("errcode").equals(Constant.SCOPE_OUT_DEPT)) {
            log.warn("查询部门范围权限失败, queryResult={}.", queryResult);
            return null;
        }
        log.info("queryDeptList result={}", queryResult);
        JSONObject queryObj = JSONObject.parseObject(queryResult.toString());
        Dept dept = new Dept();
        dept.setName(queryObj.get("name").toString());
        //部门主管,优先钉钉部门的主管 对应 CRM部门的负责人，如果主管没有值群主有值，则取群主
        Object managerList = Optional.ofNullable(queryObj.get("deptManagerUseridList")).orElse("");
        String owner = convert(managerList.toString());
        if(StringUtils.isEmpty(owner) && StringUtils.isNotEmpty(Optional.ofNullable(queryObj.get("orgDeptOwner")).orElse("").toString())) {
            owner = queryObj.get("orgDeptOwner").toString();
        }
        dept.setDeptOwner(owner);
        dept.setId(Long.parseLong(queryObj.get("id").toString()));
        if (Integer.parseInt(queryObj.get("id").toString()) != 1) {
            dept.setParentid(Long.parseLong(queryObj.get("parentid").toString()));
        }

        return dept;
    }

    public static List<EmployeeDingVo> getSmartWorkList(String ids,String accessToken,String clientIp){
        Gson gson = new Gson();
        String clientUrl = DingRequestUtil.appendUrl(clientIp);
        Map<String, Object> arg = new HashMap<>();
        arg.put("type", "POST");
        String getUserDetailUrl = DingUrl.LIST_SMART_EMPLOYEES.concat("?access_token=").concat(accessToken);
        arg.put("url", getUserDetailUrl);
        JSONObject jsonObject=new JSONObject();
        String fields="sys00-name,sys00-dept,sys00-mainDept,sys00-mobile";
        jsonObject.put("userid_list",ids);
        jsonObject.put("token",Constant.TOKEN);
        jsonObject.put("field_filter_list",fields);
        arg.put("data", jsonObject);
        Object queryResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(arg));
        if (Objects.isNull(queryResult)) {
            queryResult= tryAgainRequest(clientUrl,gson.toJson(arg));
            if(ObjectUtils.isEmpty(queryResult)){
                log.warn("startConnect appKey或appSecret错误.");
                return null;
            }
        }
        String  read = JSONPath.read(queryResult.toString(), "$.result").toString();
        List<FieldsEmployeeVo> fieldsEmployeeVos = JSONObject.parseArray(read, FieldsEmployeeVo.class);
        log.info("DingRequestUtils  query data read:{},fieldsEmployee:{}",read,fieldsEmployeeVos);
        List<EmployeeDingVo> userVos=Lists.newArrayList();
        fieldsEmployeeVos.stream().forEach(item ->{
            List<FieldsEmployeeVo.fieldMap> field_list = item.getField_list();
            EmployeeDingVo vo=new EmployeeDingVo();
            vo.setUserid(item.getUserId());
            field_list.stream().forEach(temp ->{
                try {
                    String value = temp.getValue();
                    if (null != value) {
                        switch (temp.getField_code()) {
                            case Constant.FIELD_NAME:
                                vo.setName(value);
                                break;
                            case Constant.FIELD_MOBILE:
                                vo.setMobile(convertMobile(value));
                                break;
                            case Constant.FIELD_DEPT:
                                vo.setDepartment(conVertDepts(value));
                                break;
                            case Constant.FIELD_MAIN_DEPT:
                                vo.setMainDept(Long.valueOf(value).equals(-1L) ? 1L : Long.valueOf(value));
                                break;
                        }
                    }
                }catch (Exception e) {
                    log.error("trace DingRequestUtils temp:{}, get excetpion , ",temp, e);
                }
            });
            userVos.add(vo);
        });
        log.info("Ding requestUtil userVos:{}",userVos);
        return userVos;
    }

    //返回result可能是连接超时导致，sleep5s
    private static Object tryAgainRequest(String clientUrl, String arg){
        try {
            log.info("请求超时，重新尝试。。。。");
            Thread.sleep(20000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return DingRequestUtil.proxyRequest(clientUrl, arg);
    }



    private static List<Integer> conVertDepts(String value){
        //376323342|375403450 转化成list
        if(StringUtils.isEmpty(value)){
            return Lists.newArrayList(1);
        }
        String[] split = value.split("\\|");
        List<Integer> depts=new ArrayList<>(split.length);
        for (int i = 0; i < split.length; i++) {
            depts.add(Integer.valueOf(split[i]).equals(-1)?1:Integer.valueOf(split[i]));
        }
        return depts;
    }

    private static String convertMobile(String phone){
        if(StringUtils.isNotEmpty(phone)){
            String[] split = phone.split("\\-");
            return split[1];
        }

        return null;
    }

    public static Object createToDo(String url, String argJson, Map<String, String> header) {
        StopWatch requestWatch=StopWatch.create("CreateToDo");
        CloseableHttpResponse response = HttpUtils.httpPost(url, argJson, header);
        if (Objects.isNull(response)) {
            log.warn("toDo post failed,url={},argJson={}");
            return null;
        }
        Object entity = null;
        try {
            entity = EntityUtils.toString(response.getEntity(), "UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        requestWatch.lap("result");
        log.info("toDo request url={},argJson={},result:{}", url,argJson,entity);
        requestWatch.log();
        return entity;
    }

    public static Object getTakleId(String url, Map<String, String> header) {
        StopWatch requestWatch=StopWatch.create("CreateToDo");
        CloseableHttpResponse response = HttpUtils.doGet(url, header);
        if (Objects.isNull(response)) {
            log.warn("toDo post failed,url={},argJson={}");
            return null;
        }
        Object entity = null;
        try {
            entity = EntityUtils.toString(response.getEntity(), "UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        requestWatch.lap("result");
        log.info("toDo request url={},result:{}", url,entity);
        requestWatch.log();
        return entity;
    }

    public static Object DealTodo(String url, String argJson, Map<String, String> header) {
        StopWatch requestWatch=StopWatch.create("DealToDo");
        CloseableHttpResponse response = HttpUtils.httpPut(url, argJson, header);
        if (Objects.isNull(response)) {
            log.warn("toDo post failed,url={},argJson={}");
            return null;
        }
        Object entity = null;
        try {
            entity = EntityUtils.toString(response.getEntity(), "UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        requestWatch.lap("result");
        log.info("toDo request url={},argJson={},result:{}", url,argJson,entity);
        requestWatch.log();
        return entity;
    }
    public static Object deleteToDo(String url, Map<String, String> header) {
        StopWatch requestWatch=StopWatch.create("DeleteToDo");
        CloseableHttpResponse response = HttpUtils.doDelete(url, header);
        if (Objects.isNull(response)) {
            log.warn("toDo post failed,url={},argJson={}");
            return null;
        }
        Object entity = null;
        try {
            entity = EntityUtils.toString(response.getEntity(), "UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        requestWatch.lap("result");
        log.info("toDo request url={},result:{}", url,entity);
        requestWatch.log();
        return entity;
    }

    public static SmartworkEmployeeVo smartworkHrmEmployeeResponse(String clientIp, String accessToken, Map<String, Object> smartworkHrmEmployeeMap){
        Gson gson = new Gson();
        Map<String, Object> messageArg = new HashMap<>();
        messageArg.put("url", DingUrl.SMART_WORK_EMPLOYEE_URL.concat("?access_token=").concat(accessToken));
        messageArg.put("type", "POST");
        messageArg.put("data", smartworkHrmEmployeeMap);
        String clientUrl = DingRequestUtil.appendProxyHttpUrl(clientIp);
        HttpResponseMessage messageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(clientUrl, new HashMap<>(),gson.toJson(messageArg));
        log.info("DingRequestUtil.smartworkHrmEmployeeResponse.messageResult={}", messageResult);
        JSONObject msgJsonObject = JSONObject.parseObject(messageResult.getContent());
        Integer createCode = (Integer) msgJsonObject.get("errorCode");
        String content = msgJsonObject.get("data").toString();
        if(createCode != 200) {
            log.info("DingRequestUtil.smartworkHrmEmployeeResponse.messageResult={}", messageResult);
            return null;
        }
        SmartworkEmployeeVo vo = new Gson().fromJson(content, SmartworkEmployeeVo.class);
        return vo;
    }

    /**
     * 实现java 中 list集合中有超过100条数据,每100条为一组取出
     * @param list 可穿入超过100条数据的List
     * @return map 每一Kye中有100条数据的List
     */
    public static Map groupList(List list) {

        int listSize = list.size();
        int toIndex = 100;
        Map map = new HashMap();     //用map存起来新的分组后数据
        int keyToken = 0;
        for (int i = 0; i < list.size(); i += 100) {
            if (i + 100 > listSize) {        //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
                toIndex = listSize - i;
            }
            List newList = list.subList(i, i + toIndex);
            map.put(keyToken, newList);
            keyToken++;
        }

        return map;
    }
}
