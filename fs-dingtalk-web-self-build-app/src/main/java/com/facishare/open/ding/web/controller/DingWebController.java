package com.facishare.open.ding.web.controller;

import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.web.base.BaseController;
import com.facishare.open.ding.web.base.UserVo;
import com.facishare.open.feishu.syncapi.enums.BindTypeEnum;
import com.facishare.open.feishu.syncapi.model.info.EnterpriseTrialInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by system on 2018/4/4.
 */
@Slf4j
@RestController
@RequestMapping("/web")
public class DingWebController extends BaseController{

    /**
     * 留资功能使用的接口，得到企业的最新订单情况和绑定类型
     * @return
     */
    @RequestMapping(value = "/order/getEnterpriseTrialInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<EnterpriseTrialInfo> getEnterpriseTrialInfo() {
        UserVo userVo = getUserVo();
        if(ObjectUtils.isEmpty(userVo)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        //插件直接返回
        EnterpriseTrialInfo info = new EnterpriseTrialInfo();
        info.setBindType(BindTypeEnum.manual);
        return Result.newSuccess(info);
    }
}
