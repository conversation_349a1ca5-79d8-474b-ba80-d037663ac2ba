package service;

import base.BaseAbstractTest;
import com.facishare.open.ding.api.result.CreateUserEventResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.result.DingOrderResult;
import com.facishare.open.ding.api.result.DingPersonResult;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.api.vo.EmployeeVo;
import com.facishare.open.ding.common.model.*;
import com.facishare.open.ding.common.result.Result;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.common.utils.HttpUtils;
import com.facishare.open.ding.provider.constants.Constant;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.manager.DingDeptMananger;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.facishare.organization.adapter.api.config.model.GetConfigDto;
import com.facishare.organization.adapter.api.config.service.EnterpriseConfigService;
import com.facishare.organization.adapter.api.exception.ErrorUndefinedErrorException;
import com.facishare.organization.adapter.api.model.EnterpriseConfigKey;
import com.facishare.organization.adapter.api.model.biz.department.ModifiedDepartment;
import com.facishare.organization.adapter.api.model.biz.department.arg.ModifyDepartmentArg;
import com.facishare.organization.adapter.api.model.biz.department.result.ModifyDepartmentResult;
import com.facishare.organization.adapter.api.model.biz.employee.ModifyEmployee;
import com.facishare.organization.adapter.api.model.biz.employee.arg.CreateEmployeeArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.CreateEmployeeResult;
import com.facishare.organization.adapter.api.service.DepartmentService;
import com.facishare.organization.adapter.api.service.EmployeeService;

import com.facishare.organization.api.exception.OrganizationException;
import com.facishare.organization.api.model.employee.arg.BatchModifyEmployeeActiveArg;
import com.facishare.organization.api.model.employee.result.BatchModifyEmployeeActiveResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.util.EntityUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/7/13 17:29
 */
@Slf4j
public class ObjectMappingServiceTest extends BaseAbstractTest {
    @Autowired
    private ObjectMappingService objectMappingService;

    @Autowired
    private EmployeeService employeeAdapterService;
    @Autowired
    private EnterpriseConfigService enterpriseConfigService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private EmployeeProviderService employeeProviderService;
//    @Autowired
//    private CircleService circleService;
    @Autowired
    private DingMappingEmployeeManager dingMappingEmployeeManager;
    @Autowired
    private DingDeptMananger dingDeptMananger;
//    @Autowired
//    private com.facishare.open.addressbook.api.EmployeeService employeeService;
    @Autowired
    private DingAuthService dingAuthService;

    @Test
    public void testbindEmployee() {
//        Result<List<DingMappingEmployeeResult>> result =  objectMappingService.queryNewEmployee(2);
//        log.info("=========bindEmployee={}", result);
    }

    @Test
    public void testGetEmployee() {
        Result<List<DingMappingEmployeeResult>> result = objectMappingService.getEmployeeFs(83384, new LinkedList<>());
        System.out.println(result);

    }

    @Test
    public void testDeleteBind() {
        Result<Integer> result = objectMappingService.deleteBind(2, "111");

    }

//    @Test
//    public void testActive(){
//        BatchModifyEmployeeActiveArg arg=new BatchModifyEmployeeActiveArg();
//        arg.setActive(false);
//        arg.setEmployeeIds(Lists.newArrayList(1007));
//        arg.setUpdateTime(new Date().getTime());
//        arg.setOperatorId(Constant.SYSTEM_MANAGER);
//        arg.setEnterpriseId(80164);
//        BatchModifyEmployeeActiveResult activeResult = employeeProviderService.batchModifyEmployeeActive(arg);
//        MetaParam metaParam=new MetaParam("80164",Constant.SYSTEM_USER);
//        BaseResult baseResult = employeeService.setEmployeeStatus(metaParam, 1007,false);
//
//    }


    @Test
    public void testCreate() {
        EmployeeVo arg = new EmployeeVo();
        arg.setName("贤杰的小小笑晓号");
        arg.setGender("F");
        arg.setMobile("15518882313");
        arg.setEi(83384);
        Result<Integer> result = objectMappingService.createFxEmployee(arg);

    }

    @Test
    public void testModifyFxEmp() {
        User user = new User();
        user.setName("吴贝贝");
        user.setHiredDate(System.currentTimeMillis());
        user.setMobile("18926584793");
        Result<Void> result = objectMappingService.modifyFxEmployee(82777,1006,user);
        log.info("=========result===="+result);
    }

    @Test
    public void testStopFxEmp() {
        Result<Void> result = objectMappingService.stopFxEmployee(71658, 1013, "zhangsan");

    }

    @Test
    public void testCallBackEmployee() {
        List<String> lists = Lists.newArrayList();
        lists.add("175718530421871316");
        Result<Void> result = objectMappingService.updateEmp(83384, lists);
//        objectMappingService.stopEmp("ding5fe5aa1ba078facc24f2f5cc6abecb85", lists);
        System.out.println(result);
    }

    @Test
    public void testModifyEmployee() {
        GetConfigDto.Argument argument = new GetConfigDto.Argument();
        String key = EnterpriseConfigKey.CONFIG_KEY_NEW_EMP_MOBILE_SETTING.getKey();
        argument.setKey(key);
        argument.setEmployeeId(-9);
        argument.setCurrentEmployeeId(-9);
        argument.setEnterpriseId(76235);
        GetConfigDto.Result configResult = enterpriseConfigService.getConfig(argument);
        String configValue = configResult.getValue();


        ModifyEmployee.Argument argument1 = new ModifyEmployee.Argument();
        argument1.setEnterpriseId(76235);
        argument1.setEmployeeId(1017);
        argument1.setMainDepartmentId(1003);
        argument1.setViceDepartmentIds(Lists.newArrayList(1002,1003));
        argument1.setViceDepartmentIds(Lists.newArrayList());
        argument1.setCurrentEmployeeId(-9);
        argument1.setName("三虎hhh");
//        argument1.setMobile("***********");
        argument1.setAccount("***********");
//        argument.setEmail("");
//        argument1.setRole(1);
//        argument1.setMobileStatus(MobileStatus.valueOf(Integer.valueOf(configValue)).getValue());
//        ModifyEmployeeArg arg = new ModifyEmployeeArg();
//        ModifiedEmployee modifiedEmployee = new ModifiedEmployee();
//        modifiedEmployee.setName("测试员工003哈哈");
//        modifiedEmployee.setEmployeeId(1014);
//        arg.setModifiedEmployee(modifiedEmployee);
//        arg.setEnterpriseId(76235);
//        arg.setUpdateTime(new Date().getTime());
//        arg.setOperatorId(-10000);


//        ModifyEmployeeResult modifyEmployeeResult = employeeApiService.modifyEmployee(arg);
        ModifyEmployee.Result result = employeeAdapterService.modifyEmployeeV2(argument1);

    }

    @Test
    public void testJson() {
//        String plainText = "{\"CorpId\":\"ding5fe5aa1ba078facc24f2f5cc6abecb85\",\"EventType\":\"org_dept_create\",\"DeptId\":[*********],\"TimeStamp\":\"1596560733627\"}";
//        Gson gson = new Gson();
//        JSONObject obj = JSON.parseObject(plainText);
//        List<Long> modifyDepts = gson.fromJson(obj.getString("DeptId"), new TypeToken<ArrayList<Long>>() {
//        }.getType());
//        log.info("" + modifyDepts);
        DeptVo deptVo = dingDeptMananger.queryByDingId(80164, 427942486L);
        String name="财政部二";
        String[] sameName = name.split("__");

    }

    @Test
    public void syncEmployee() {
        List<DingMappingEmployeeResult> list = Lists.newArrayList();
        //bindStatus: 0
        //createBy: 1000
        //createTime: "Aug 5, 2020 6:00:51 PM"
        //dingDeptId: 1
        //dingDeptName: "阿南科技"
        //dingEmployeeId: "2438553522-217949988"
        //dingEmployeeName: "测试员工003哈哈"
        //dingEmployeePhone: "19874562369"
        //dingEmployeeStatus: 1
        //dingUnionId: "fe2E4UgtCnqiPfdiPXiSoyImAiEiE"
        //ei: 76246
        //id: 884
        //key: 1
        //updateBy: 1000
        //updateTime: "Aug 5, 2020 6:00:51 PM"
        DingMappingEmployeeResult dingMappingEmployeeResult = new DingMappingEmployeeResult();
        dingMappingEmployeeResult.setBindStatus(0);
        dingMappingEmployeeResult.setCreateBy(1000);
        dingMappingEmployeeResult.setDingDeptId(1L);
        dingMappingEmployeeResult.setDingDeptName("阿南科技");
        dingMappingEmployeeResult.setDingEmployeeId("01105636172129201422");
        dingMappingEmployeeResult.setDingEmployeeName("王建国01");
        dingMappingEmployeeResult.setDingEmployeePhone("13578923481");
        dingMappingEmployeeResult.setDingEmployeeStatus(1);
        dingMappingEmployeeResult.setDingUnionId("iSgPuNqii86NaZoZVRHxUiPmQiEiE");
        dingMappingEmployeeResult.setEi(76246);
        list.add(dingMappingEmployeeResult);
        objectMappingService.bindEmployee(list, 76246, 1000, false);
    }

    @Test
    public void testPost(){
        Map<String, String> argMap = new HashMap<>();
        argMap.put("redirectAppId", "dingoariybxo4kz9c7qsin");
        argMap.put("redirectAppSecret", "6dmDBMw0eixujDJwXbqzeWUdxHfdSvnSmiY4PZvBsnhF_a87qQKSNemJzVxd2hrm");
        argMap.put("token", "NyB0KLZ0oEwvh8aRqrz1r67llBFg4MH2fy7rElNYcG2A9z0qurpLAvpmZ4NB8pSJ3wj04El0iM1Uw5+E8uym1IasRaLGPI8ijnoP76Ry2Uyj4tVMyiKblbHTf3T4StbimeArN8ZXhqZZjrsXjAspHOMLVAtKBpeIw2nxPwtdup8=");
        argMap.put("code", "79e88679d2113bfdba9d36b4285b752a");
        Gson gson=new Gson();
        CloseableHttpResponse response =HttpUtils.httpPost("http://www.91xxkj.com:8089/proxy/getUserByCode",gson.toJson(argMap),null);
        String entity = null;
        try {
            entity = EntityUtils.toString(response.getEntity(), "UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testDingSmartList(){
//        List<String> ids=Lists.newArrayList();
//        ids.add("manager9802");
//        ids.add("092159206936640993");
//        String ids="0163622837887300";
//        DingRequestUtil.getSmartWorkList(ids,"22db7a8dd48d36b998cb531190253d14","http://**************:8089/");
        objectMappingService.againEmployeeMapping(79478);

    }

    @Test
    public void testInsertAll(){
        DingMappingEmployeeResult result=new DingMappingEmployeeResult();
        result.setEi(74315);
        result.setEmployeeId(1335);
        result.setEmployeeStatus(1);
        result.setEmployeeName("柯南颖019");
        result.setDingEmployeeId("manager9802");
        result.setEmployeePhone("13432858633");
        result.setDingEmployeePhone("13432858633");
        result.setDingUnionId("7e9pUxEWuwSCVn6KhKqiPYQiEiE");
        result.setDingDeptId(375403452L);
        result.setCrmDeptId(0);
        result.setDingDeptName("技术部");
        result.setDingEmployeeName("柯南颖019");
        dingMappingEmployeeManager.insertAllModelData(Lists.newArrayList(result),74315);
    }





    @Test
    public void testEmployee(){
        CreateEmployeeArg arg=new CreateEmployeeArg();
        arg.setName("liuhua");
        arg.setFullName("liuhua");
        arg.setGender("F");
        arg.setMobile("***********");
        arg.setTelephone("***********");
        arg.setAccount("***********");
        arg.setCurrentEmployeeId(-9);
        arg.setMainDepartmentId(1006);
        arg.setDepartmentIds(Lists.newArrayList(1005,1006));
        arg.setEnterpriseId(76246);
        CreateEmployeeResult employee = null;
        try {
            employee = employeeAdapterService.createEmployee(arg);
        } catch (OrganizationException e) {
            if(e instanceof ErrorUndefinedErrorException){
                Result createResult = Result.newError(ResultCode.CREATE_FXEMP_FAILED);
                createResult.setErrorDescription(e.getMessage());
                System.out.println(createResult);
            }


            System.out.println(""+e.getErrorCode()+e.getMessage());
            e.printStackTrace();
        }
        System.out.println(employee);
    }


    @Test
    public void testSpecific(){
        String name="刘德华|热血2@!合唱@ 团";
        String match="[^\\\\-\\\\\\\\/\\\\[\\\\]【】()（）_a-zA-Z0-9\\u4E00-\\u9fAF\\u3400-\\u4dBF\\u3300-\\u33FF\\uF900-\\uFAFF]";
        // 创建 Pattern 对象
        Pattern r = Pattern.compile(match);
        // 现在创建 matcher 对象
        Matcher m = r.matcher(name);
        StringBuilder validName=new StringBuilder();
        while (m.find()){
            System.out.println(m.start());
            System.out.println(m.end());
            String result = m.replaceAll("-");
//            validName.append(name.substring(name,m.end()));
            System.out.println(m.group());
        }
        System.out.println(m);

    }

    @Test
    public void initModelEmployee(){
        //初始化逻辑
        objectMappingService.initModelEmployee(89779,1000,"89779");
    }

    @Test
    public void insertMappingEmp(){
        //初始化逻辑
        Dept dept = new Dept();
        dept.setId(686977080L);
        objectMappingService.insertMappingEmp(dept, 89779,"http://172.31.100.247:25775/dingtalkinner/", 1755125166L);
    }

    @Test
    public void createDeptOwner(){
        //初始化逻辑
        objectMappingService.createDeptOwner("http://47.113.195.161:8089/","d03b750a99fb377f9d8ebed89d45a0d0","NyB0KLZ0oEwvh8aRqrz1r67llBFg4MH2fy7rElNYcG2A9z0qurpLAvpmZ4NB8pSJ3wj04El0iM1Uw5+E8uym1IasRaLGPI8ijnoP76Ry2Uyj4tVMyiKblbHTf3T4StbimeArN8ZXhqZZjrsXjAspHOMLVAtKBpeIw2nxPwtdup8=",
                76340 );
    }




    @Test
    public void testModify(){
        //修改部门
//        ModifyDepartment.Arg arg=new ModifyDepartment.Arg();
//        ModifiedDepartment modifiedDepartment=new ModifiedDepartment();
//        modifiedDepartment.setDepartmentId(1016);
//        modifiedDepartment.setName("营销一处");
// //查询部门的员工
//
//        Integer crmEmployeeId = null;
//        modifiedDepartment.setDepartmentPrincipalId(crmEmployeeId);
//        modifiedDepartment.setParentDepartmentId(1005);
//        arg.setModifiedDepartment(modifiedDepartment);
//        arg.setEnterpriseId(76246);
//        arg.setModifiedDepartment(modifiedDepartment);
//
//
//
//
////        ModifyDepartment.Result result = departmentProviderService.modifyDepartment(arg);
////        System.out.println(result);
//        //修改员工
//        ModifyEmployee.Argument argument=new ModifyEmployee.Argument();
//        argument.setEnterpriseId(76246);
//        argument.setName("二珂");
//        argument.setEmployeeId(1034);
//        ModifyEmployee.Result modifyEmployeeV2 = employeeAdapterService.modifyEmployeeV2(argument);
//        System.out.println(modifyEmployeeV2);

    }

    //测试修改部门
//    @Test
//    public void testModifyDept(){
//
//        MetaParam metaParam=new MetaParam();
//        metaParam.setEnterpriseAccount("76246");
//        metaParam.setCurrentEmployeeId(1000);
//
//        BaseResult result = circleService.updateCircle(metaParam, 1014, "营销1一1处", 1005, null);
//        System.out.println(result);
//    }

    @Test
    public void testUpsertAllDept(){
        objectMappingService.upsertAllDept(76406,1000,"76406");
    }


    @Test
    public void testCrmManager(){
//        objectMappingService.createDeptOwner("http://**************:8089/","22db7a8dd48d36b998cb531190253d14","NyB0KLZ0oEwvh8aRqrz1r67llBFg4MH2fy7rElNYcG2A9z0qurpLAvpmZ4NB8pSJ3wj04El0iM1Uw5+E8uym1IasRaLGPI8ijnoP76Ry2Uyj4tVMyiKblbHTf3T4StbimeArN8ZXhqZZjrsXjAspHOMLVAtKBpeIw2nxPwtdup8=",76406);


    }

//    @Test
//    public void testCircle(){
//        MetaParam metaParam=new MetaParam("78527",1000);
//        ListResult<Circle> allCircles = circleService.getAllCircles(metaParam);
//
//
//    }


    @Test
    public void testManager(){
        ModifyDepartmentArg modifyDepartmentArg=new ModifyDepartmentArg();
        modifyDepartmentArg.setEnterpriseId(80787);
        modifyDepartmentArg.setLastUpdateTime(new Date().getTime());
        modifyDepartmentArg.setCurrentEmployeeId(1000);
        ModifiedDepartment modifiedDepartment=new ModifiedDepartment();
        modifiedDepartment.setDepartmentPrincipalId(1006);
        //石谨旖
        modifiedDepartment.setDepartmentId(1000);
        modifiedDepartment.setName("测试部门一");
        modifyDepartmentArg.setModifiedDepartment(modifiedDepartment);
        ModifyDepartmentResult modifyDepartmentResult = departmentService.modifyDepartment(modifyDepartmentArg);


    }


    @Test
    public void testSpilt(){
        String name="工程部__12";
        String[] s = name.split("__");
        System.out.println(s[0]);
    }



    @Test
    public void  testEmployees(){
        List<Integer> emps=new ArrayList<>();
        for(int i=0;i<17980;i++){
           emps.add(i);
        }
        for(int i=0;i<emps.size();i++){
            //一次请求20个员工
            int end=i+20>emps.size()?emps.size():i+20;
            List<Integer> items = emps.subList(i, end);
            items.stream().forEach(item -> System.out.println(item));
//            System.out.println(emps.size());
            i+=19;
        }
    }

    @Test
    public void testNoDepts(){
//        GetNoDepartmentEmployeesDtoArg arg=new GetNoDepartmentEmployeesDtoArg();
//        arg.setEnterpriseId(79478);
//        arg.setMainDepartment(MainDepartment.MAIN);
//        arg.setRunStatus(RunStatus.ALL);
//        GetNoDepartmentEmployeesDtoResult noDepartmentEmployees = employeeProviderService.getNoDepartmentEmployees(arg);
//        System.out.println(noDepartmentEmployees);
        objectMappingService.fixNoMainDept(79478,1000,"zslceshi018");




    }


    @Test
    public void testSmartList(){


        UserDetailVo userDetailVo = DingRequestUtil.queryUserByDeptId("https://www.ceshi112.com/dingtalkinner/", "f749c4b43c4f3e93890583f746ada534", 686977080, 1755125166L);

        Gson gson = new Gson();
        Map<String, Object> messageArg = new HashMap<>();
        String clientUrl = DingRequestUtil.appendUrl("http://**************:8089/");
        String Url="https://oapi.dingtalk.com/user/simplelist?access_token=替换真实token";
        messageArg.put("url", Url);//查询员工信息
        messageArg.put("type", "GET");
        messageArg.put("token", "Ly2HXCIZG7/QlpFaFpUqaNxiKnzMq/eWQwVTNbjgF9Z9AcyFiQNHUvQgBir3geA0REYTme85lO6ivEcEEdIdwMk7DCy3NNv++dyE+6YT1NVGVt8/Pl3QFNUM+ph9HaN/OcJ1gwRv0t6X5i3IRTfAOpIfxs+1ng+jUc9RbyBgVm4=");
        Object queryResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));

    
    
    }


    @Test
    public void testUpdateEmps(){
        //285966	79478					1	顾宗平	136510323838266554	15851275351	gKKhN3W5IsH4BwR2iiI2F9AiEiE	399314992		KA导购员	2020-11-26 20:55:31	2020-11-26 20:55:31	1000	1000	0
        //285440	79478					1	王红艳	270356540229463772	13620783987	o1GKIsxcOqcSiifiPD7GVPjAiEiE	331043134		吉林老板代理公司组织关联	2020-11-26 20:55:19	2020-11-26 20:55:19	1000	1000	0
        List<User> userList=Lists.newArrayList();
        User user=new User();
        user.setUnionid("fLcTfcr7PMLQYUGNl9t9OgiEiE");
        user.setUserid("074915225021101684");
        user.setName("刘淑萍");
        userList.add(user);
//285441	79478					1	刘淑萍	074915225021101684	18004413712	fLcTfcr7PMLQYUGNl9t9OgiEiE	331043134		吉林老板代理公司组织关联	2020-11-26 20:55:19	2020-11-26 20:55:19	1000	1000	0

        User user1=new User();
        user1.setUnionid("o1GKIsxcOqcSiifiPD7GVPjAiEiE");
        user1.setUserid("270356540229463772");
        user1.setName("王红艳");
        userList.add(user1);

        Integer count = dingMappingEmployeeManager.initMappingEmployee(userList, 79478, 1000, 331043134L, "吉林老板代理公司组织关联");

    }



//    @Test
//    public   void nomainDept(){
//        // 读取 excel 表格的路径
//        String fileName = "C:/Users/<USER>/Desktop/工作簿1.xlsx";
//        long beforeTime = System.currentTimeMillis();
//        EasyExcel.read(fileName,ExcelUserVo.class,new ExcelModelListener(ExcelUserVo)).sheet().doRead();
//        System.out.println("读取数据所花的时间："+(System.currentTimeMillis()-beforeTime)+"毫秒");
//    }

    @Test
    public void  testSmartListV2(){
        String ids="074915225021101684";
        List<EmployeeDingVo> smartWorkList = DingRequestUtil.getSmartWorkList(ids, "22db7a8dd48d36b998cb531190253d14", "http://**************:8089/");

        //全量更新
        System.out.println(smartWorkList);
    }

    @Test
    public void  testExecuterService(){
        ExecutorService executorService = Executors.newFixedThreadPool(20);

        for(int i=0;i<10000;i++){
            int index=i;
            executorService.submit(new Runnable() {
                @Override
                public void run() {

                    System.out.println("calling index:{}"+index);
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }

                }
            });
        }
        try {
            Thread.sleep(100000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }


    @Test
    public void testCreateFxDept(){
        objectMappingService.createFxDept(72045,Lists.newArrayList(485338072L));
    }

    @Test
    public void testDept(){
        objectMappingService.fixTreeDept(76246,"76246",1000);
    }

//    @Test
//    public  void testObjectMapping(){
//
//        MetaParam metaParam = new MetaParam();
//        metaParam.setEnterpriseAccount("76246");
//        metaParam.setCurrentEmployeeId(-10000);
//        //停用部门
//        BaseResult baseResult = circleService.setCircleStatus(metaParam, 1538, true);
//        System.out.println(baseResult);
//    }

    @Test
    public void testDingAuth(){
        dingAuthService.createJsApiSignature("https://www.ceshi112.com/hcrm/dingtalk","76246",70480L);
    }


    @Test
    public void testBindEmployees() {
        CreateUserEventResult createUserEventResult = new CreateUserEventResult();
        createUserEventResult.setEa("83384");
        createUserEventResult.setEi(83384);
        createUserEventResult.setUser(1000);
        Result<Void> result = objectMappingService.autoSaveEmployeeAccount(createUserEventResult);
        System.out.println(result);
    }

    @Test
    public void testDIngEmployee() {
        Result<Void> result = objectMappingService.autoSaveDingEmployeeAccount(84375, new LinkedList<>());
        System.out.println(result);
    }

    @Test
    public void testBatchGetDingEmployeesByFsIds() {
        Result<List<DingMappingEmployeeResult>> listResult = dingAuthService.batchGetDingEmployeesByFsIds("83384", Lists.newArrayList(1000, 1006, 1011));
        System.out.println(listResult);
    }

    @Test
    public void testBatchGetDingEmployeesByDingIds() {
        Result<List<DingMappingEmployeeResult>> listResult = dingAuthService.batchGetDingEmployeesByDingIds("ding328b43b93b4861db4ac5d6980864d335", Lists.newArrayList("092159206936640993","www.baidu.com","manager1076"));
        System.out.println(listResult);
    }

    @Test
    public void testQueryUserInfoByAuth() {
        Result<DingPersonResult> listResult = dingAuthService.queryUserInfoByAuth(null, null, null);
        System.out.println(listResult);
    }

    @Test
    public void testQueryLastOrder() {
        Result<DingOrderResult> listResult = dingAuthService.queryLastOrder(null);
        System.out.println(listResult);
    }

    @Test
    public void testUpdateEmployeeAccountBindInfo() {
        DingMappingEmployeeResult employeeResult = new DingMappingEmployeeResult();
        employeeResult.setEi(83384);
        employeeResult.setEmployeeId(1009);
        employeeResult.setEmployeeName("U-FSQYWX-RenHaiT");
        employeeResult.setEmployeePhone("***********");
        Result<Integer> listResult = objectMappingService.updateEmployeeAccountBindInfo(employeeResult);
        System.out.println(listResult);
    }

    @Test
    public void  smartworkHrmEmployeeResponse(){
        Map<String, Object> smartworkHrmEmployeeMap = new HashMap<>();
        smartworkHrmEmployeeMap.put("agentid", 1755125166L);
        smartworkHrmEmployeeMap.put("userid_list", "****************,094831680719970827");
        smartworkHrmEmployeeMap.put("field_filter_list", "sys02-sexType,sys00-mainDept,sys00-mainDeptId");
        SmartworkEmployeeVo smartWorkList = DingRequestUtil.smartworkHrmEmployeeResponse("http://172.31.100.247:25775/dingtalkinner/", "50aaabe3a6d23f7a8482d54e455804c5", smartworkHrmEmployeeMap);

        System.out.println(smartWorkList);
    }

    @Test
    public void testSex() {
        List<User> users = DingRequestUtil.queryDeptUser("http://172.31.100.247:25775/dingtalkinner/", 1L, "xxx", "xxx", "test", "xxx", 1755125166L);
        System.out.println(users);
        User user = DingRequestUtil.getUser("http://172.31.100.247:25775/dingtalkinner/", "xxx", "xxx", "51614229839851", "test", "xxx", 1755125166L);
        System.out.println(user);
        UserVo userDetail = DingRequestUtil.getUserDetail("xxx", "094831680719970827", "http://172.31.100.247:25775/dingtalkinner/", 1755125166L);
        System.out.println(userDetail);
        UserDetailVo userDetailVo = DingRequestUtil.queryUserByDeptId("http://172.31.100.247:25775/dingtalkinner/", "xxx", 1, 1755125166L);
        System.out.println(userDetailVo);
    }
}
