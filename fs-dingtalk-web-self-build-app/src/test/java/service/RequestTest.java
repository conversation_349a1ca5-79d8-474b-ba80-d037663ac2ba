package service;

import base.BaseAbstractTest;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.open.ding.api.service.DingtalkUserService;
import com.facishare.open.ding.api.service.RedisDingService;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.model.UserVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.crm.CrmUrlUtils;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.model.HttpResponseMessage;
import com.facishare.open.ding.provider.model.InnerSearchQueryInfo;
import com.facishare.open.ding.provider.utils.OkHttp3MonitorUtilsFromProvider;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020/8/3 10:29
 * @Version 1.0
 */
@Slf4j
public class RequestTest extends BaseAbstractTest {

    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private RedisDingService redisDingService;
    @Autowired
    private DingtalkUserService dingtalkUserService;

//    private CrmHttpUtils crmHttpUtils;

    @Test
    public void testCreateDept(){
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei","78583");
        hearsMap.put("x-fs-userinfo","-10000");

        ActionAddArg addArg = new ActionAddArg();
        Map<String,Object> objectData= Maps.newHashMap();
        objectData.put("object_describe_api_name","DepartmentObj");
        objectData.put("name","测试部门099");
        objectData.put("record_type","default__c");
        objectData.put("status","0");
        //负责人
        objectData.put("manager_id",Lists.newArrayList("1000"));
        objectData.put("parent_id",Lists.newArrayList("1005"));
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("object_data", objectData);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(CrmUrlUtils.createDeptUrl(), hearsMap, JSONObject.toJSONString(paramMap));

        Object read = JSONPath.read(httpResponseMessage.getContent(), "$.data.objectData._id");


    }

    @Test
    public void testModifyDept(){
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei","78583");
        hearsMap.put("x-fs-userinfo","-10000");
        Map<String,Object> objectData= Maps.newHashMap();
        objectData.put("_id",1007);
        objectData.put("name","测试部门1123");
        objectData.put("manager_id",Lists.newArrayList(1000));
        objectData.put("parent_id",Lists.newArrayList(1005));
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("data", objectData);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(CrmUrlUtils.updateDeptUrl(), hearsMap, JSONObject.toJSONString(paramMap));

    }

    @Test
    public void testStopDept(){
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei","78583");
        hearsMap.put("x-fs-userinfo","-10000");
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("dataIds", Lists.newArrayList("1007"));
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(CrmUrlUtils.stopDeptUrl(), hearsMap, JSONObject.toJSONString(paramMap));

    }

    @Test
    public void testCreateEmployee(){
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei","78583");
        hearsMap.put("x-fs-userinfo","-10000");

        Map<String,Object> objectData= Maps.newHashMap();
        objectData.put("object_describe_api_name","PersonnelObj");

        objectData.put("name","笑笑116");
        objectData.put("full_name","笑笑16");
        objectData.put("record_type","default__c");
        objectData.put("status","0");
        objectData.put("sex","M");
        objectData.put("main_department",Lists.newArrayList("1000"));
        objectData.put("phone","18023458928");
        //登录账号
        objectData.put("user_name","18023458928");
        objectData.put("vice_departments",Lists.newArrayList("1007"));
        objectData.put("leader",Lists.newArrayList("1009"));

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("object_data", objectData);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(CrmUrlUtils.createUserUrl(), hearsMap, JSONObject.toJSONString(paramMap));

    }


    @Test
    public void testModifyEmp(){
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei","78583");
        hearsMap.put("x-fs-userinfo","-10000");

        Map<String,Object> objectData= Maps.newHashMap();
        objectData.put("object_describe_api_name","PersonnelObj");

        objectData.put("name","笑笑28");
        objectData.put("_id","1028");
        objectData.put("full_name","笑笑28");
        objectData.put("record_type","default__c");
//        objectData.put("object_describe_id","5ece43021ae47000012c2582");
        objectData.put("status","0");
        objectData.put("sex","M");
        objectData.put("main_department",Lists.newArrayList("1000"));
        objectData.put("phone","18023458921");
        //登录账号
        objectData.put("user_name","18023458921");
//        objectData.put("vice_departments",Lists.newArrayList("1006"));
//        objectData.put("leader",Lists.newArrayList("1009"));

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("object_data", objectData);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(CrmUrlUtils.modifyUserUrl(), hearsMap, JSONObject.toJSONString(paramMap));

    }


    @Test
    public void testQueryEmp(){
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei","78583");
        hearsMap.put("x-fs-userinfo","-10000");


        String url = CrmUrlUtils.queryList("/PersonnelObj");
        InnerSearchQueryInfo innerSearchQueryInfo = new InnerSearchQueryInfo();
        InnerSearchQueryInfo.Filter orderIdFilter = new InnerSearchQueryInfo.Filter();
        orderIdFilter.setOperator("EQ");
        orderIdFilter.setField_name("phone");
        orderIdFilter.setField_values(Lists.newArrayList("18023458928"));

        List<InnerSearchQueryInfo.Filter> filters = Lists.newArrayList(orderIdFilter);

        innerSearchQueryInfo.setFilters(filters);
        innerSearchQueryInfo.setOffset(0);
        innerSearchQueryInfo.setLimit(100);
        InnerSearchQueryInfo.Order order=new InnerSearchQueryInfo.Order();
        order.setFieldName("last_modified_time");
        order.setAsc(false);
        List<InnerSearchQueryInfo.Order> orders = Lists.newArrayList(order);
        innerSearchQueryInfo.setOrders(orders);

        Map<String, Object> queryMap = Maps.newHashMap();
        Gson gson=new Gson();
        queryMap.put("object_describe_api_name", "DepartmentObj");
        queryMap.put("search_query_info", gson.toJson(innerSearchQueryInfo));


        HttpResponseMessage responseMessage = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(url, hearsMap,gson.toJson(queryMap));
        //查列表会带出描述，先不打印过多的结果 responseMessage
//        log.info("responseMessage:{}",responseMessage);
        String userID = JSONPath.read(responseMessage.getContent(), "$.data.dataList[0].user_id").toString();


        HeaderObj headerObj=new HeaderObj(78583,-10000);

//        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
//        List<String> values = new ArrayList<String>();
//        values.add("技术");
//        searchTemplateQuery.addFilter("full_name", values, "LIKE");
//        searchTemplateQuery.setPermissionType(null);
//        searchTemplateQuery.setDataRightsParameter(null);
//        Result<QueryBySearchTemplateResult> resultResult =
//                objectDataService.queryBySearchTemplate(headerObj, "PersonnelObj", searchTemplateQuery);
////        )objectDataService.queryBySearchTemplate(headerObj,"PersonnelObj",
//        log.info("{}",resultResult);

    }

   @Test
   public void deleteSql(){
        String sql="delete from ding_enterprise WHERE ei=780521";
       Result<Integer> result = dingtalkUserService.deleteSql(sql);
       System.out.println(result);
   }










    @Test
    public void testStopEmp(){
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei","78583");
        hearsMap.put("x-fs-userinfo","-10000");

        Map<String,Object> objectData= Maps.newHashMap();
        objectData.put("object_describe_api_name","PersonnelObj");
        objectData.put("_id","1028");
        objectData.put("status","1");

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("object_data", objectData);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(CrmUrlUtils.modifyUserUrl(), hearsMap, JSONObject.toJSONString(paramMap));

    }


    @Test
    public void testRequestUser(){

        UserVo userDetail = DingRequestUtil.getUserDetail("d7da175eaf8c3e4f938aef47b8922466", "094831680719970827", "https://www.ceshi112.com/dingtalkinner/", null);
        if(ObjectUtils.isEmpty(userDetail)){
        }

    }

    @Test
    public void testRequestDept(){

        Dept deptDetail = DingRequestUtil.getDeptDetail("22db7a8dd48d36b998cb531190253d14", "http://122.224.208.52:8089/", 427390399L);
        if(ObjectUtils.isEmpty(deptDetail)){
        }

    }
    @Test
    public void testQueryRequestDept(){

        Dept deptDetail = DingRequestUtil.queryDeptDetail("http://47.107.20.34:8089/", "20045f4297753784b1c4f8258b769cda","NyB0KLZ0oEwvh8aRqrz1r67llBFg4MH2fy7rElNYcG2A9z0qurpLAvpmZ4NB8pSJ3wj04El0iM1Uw5+E8uym1IasRaLGPI8ijnoP76Ry2Uyj4tVMyiKblbHTf3T4StbimeArN8ZXhqZZjrsXjAspHOMLVAtKBpeIw2nxPwtdup8=",458693348L);

        if(ObjectUtils.isEmpty(deptDetail)){

        }

    }

    /**
     * String proxyMessageUrl = DingUrl.CORP_MESSAGE.concat("?access_token=").concat(accessToken);
     */
    @Test
    public void testConcat(){
        String url="acc";
        url.concat(null);

    }

    @Test
    public void testOnlineConsult(){
        String clientUrl = DingRequestUtil.appendUrl("http://120.236.121.87:9191/");
        String url="https://oapi.dingtalk.com/call_back/get_call_back?access_token=替换真实token";
        Map<String, Object> messageArg = new HashMap<>();
        messageArg.put("url", url);//
        messageArg.put("type", "GET");
        Gson gson = new Gson();
        messageArg.put("token", "Ly2HXCIZG7/QlpFaFpUqaNxiKnzMq/eWQwVTNbjgF9Z9AcyFiQNHUvQgBir3geA0REYTme85lO6ivEcEEdIdwMk7DCy3NNv++dyE+6YT1NVGVt8/Pl3QFNUM+ph9HaN/OcJ1gwRv0t6X5i3IRTfAOpIfxs+1ng+jUc9RbyBgVm4=");
        Object queryResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));

    }


    @Test
    public void testToken(){
        String token = DingRequestUtil.getToken("https://www.ceshi112.com/dingtalk/", "dinglxjp3fxyzpggmixa", "LDTXNlLXW3HmaZjOVbtN3KNVnfSZ68Go1skDlYCkkXKDdJT9j2w6gaAOcM0RyFxL");
        log.info("token",token);
    }

    @Test
    public void deleteRedis(){
        Result<Map<Long, DeptVo>> deptInfos = redisDingService.getDeptInfos("76340");

    }
}
