package com.facishare.open.feishu.sync.test.service;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.enums.DepartmentIdTypeEnum;
import com.facishare.open.feishu.syncapi.enums.UserIdTypeEnum;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.service.FeishuUserService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.LinkedList;
import java.util.List;

public class FeishuUserServiceTest extends BaseTest {
    @Autowired
    private FeishuUserService feishuUserService;

    @Test
    public void createUser() {
//        for(int i=2;i<=10000;i++) {
            UserData.User user = new UserData.User();
            user.setName("测试用户100001");
            long mobile = 18926584799L;
            user.setMobile(mobile+"");
            user.setDepartment_ids(Lists.newArrayList("26e86g59467b3c1a"));
            user.setEmployee_type(1);
            Result<UserData.User> result = feishuUserService.createUser("cli_a20193041afcd00e", user);
            System.out.println(result);
//        }
    }

    @Test
    public void deleteUser() {
        try {
            Result<List<UserData.User>> listResult = feishuUserService.getUserList("cli_a20193041afcd00e",
                    null,
                    "26e86g59467b3c1a",
                    UserIdTypeEnum.user_id,
                    DepartmentIdTypeEnum.department_id,
                    50);
            if(listResult.isSuccess()) {
                for(UserData.User user : listResult.getData()) {
                    Result<Void> result = feishuUserService.deleteUser("cli_a20193041afcd00e", user.getUserId());
                    System.out.println(result);
                }
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void getAllUserByDepartment(){
        Result<List<UserData.User>> allUserByDepartment = feishuUserService.getUserList("cli_a3ddeb52763b100c",
                "100d08b69448975d",
                "0");
        System.out.println(allUserByDepartment.getData());
    }

    @Test
    public void getSingleUserInfo() {
        Result<UserData.User> result = feishuUserService.getUserInfo("cli_a20192f6afb8d00c",
                "11b1a8c266da9758",
                "ou_77db7a8a6ff1bc0c465a041843ba9b52");
        System.out.println(result);
    }

    @Test
    public void batchGetUserIds() {
        List<String> emails = new LinkedList<>();
        List<String> mobiles = new LinkedList<>();
        for(int i = 0; i < 130; i++) {
            int t = 10000;
            emails.add(String.valueOf(t + i));
            mobiles.add(String.valueOf(t + i));
        }
        //emails.add("<EMAIL>");
        mobiles.add("13711911803");
        Result<List<UserData.User>> result =
                feishuUserService.batchGetUserIds("cli_a3ddeb52763b100c", "100d08b69448975d", emails, mobiles);
        System.out.println(result);
    }
}
