package com.facishare.open.feishu.sync.test.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.sync.manager.FeishuSelfBuildAppManager;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;

public class FeishuSelfBuildAppManagerTest extends BaseTest {

    @Autowired
    private FeishuSelfBuildAppManager feishuSelfBuildAppManager;

    @Autowired
    private RedisDataSource redisDataSource;

    @ReloadableProperty("feishu_app_info")
    private String appInfo;

    @Test
    public void getTenantAccessToken(){
        Result<String> tenantAccessToken = feishuSelfBuildAppManager.getTenantAccessToken("cli_a20193041afcd00e");
        System.out.println(tenantAccessToken.getData());
    }

    @Test
    public void test(){
        HashMap hashMap = JSON.parseObject(appInfo, HashMap.class);
        System.out.println(hashMap.get("cli_a20192f6afb8d00c"));
    }

}
