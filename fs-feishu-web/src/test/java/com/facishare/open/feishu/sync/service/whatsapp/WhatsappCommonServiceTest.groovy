package com.facishare.open.feishu.sync.service.whatsapp

import com.facishare.open.feishu.sync.manager.EnterpriseBindManager
import com.facishare.open.feishu.syncapi.data.whatsapp.*
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity
import com.facishare.open.feishu.syncapi.enums.ChannelEnum
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappCommonService
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappService
import com.google.common.collect.Lists
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource
import java.nio.file.Files

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class WhatsappCommonServiceTest extends Specification {
    @Resource
    private WhatsappCommonService whatsappCommonService;
    def "getBalance"() {
        expect:
        def a = whatsappCommonService.getBalance("88146")
        print(a)
    }
}
