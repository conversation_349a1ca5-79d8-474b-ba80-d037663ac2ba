package com.facishare.open.feishu.sync.service

import com.facishare.open.feishu.sync.service.impl.CustomServiceImpl
import com.facishare.open.feishu.syncapi.enums.ChannelEnum
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class CustomServiceTest extends Specification {
    @Resource
    private CustomServiceImpl customService;
    def "getServiceAuth"() {
        expect:
        def a = customService.getCustomUrls(ChannelEnum.feishu)
        print(a)
    }
}
