package com.facishare.open.feishu.sync.test.service;

import com.facishare.open.feishu.sync.manager.FeishuAppManager;
import com.facishare.open.feishu.syncapi.model.login.LoginAuthModel;
import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.Code2UserInfoData;
import com.facishare.open.feishu.syncapi.service.LoginService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

public class LoginServiceTest extends BaseTest {
    @Resource
    private LoginService loginService;
    @Autowired
    private FeishuAppManager feishuAppManager;

    @Test
    public void code2UserInfo() {
        Result<Code2UserInfoData> result = loginService.code2UserInfo(LoginAuthModel.builder().code("e61r997c1d724ad19e672d9c57c26fc5").appId("cli_a3ddeb52763b100c").build());
        System.out.println(result);

        Result<String> data = feishuAppManager.getAppAccessToken("cli_a3ddeb52763b100c");

        System.out.println(data);
    }
}
