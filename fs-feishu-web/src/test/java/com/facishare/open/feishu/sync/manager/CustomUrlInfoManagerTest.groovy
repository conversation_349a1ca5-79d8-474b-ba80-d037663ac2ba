package com.facishare.open.feishu.sync.manager

import com.facishare.open.feishu.syncapi.entity.CustomUrlInfoEntity
import com.facishare.open.feishu.syncapi.enums.ChannelEnum
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class CustomUrlInfoManagerTest extends Specification {
    @Resource
    private CustomUrlInfoManager customUrlInfoManager;

    def "insert"() {
        given:
        CustomUrlInfoEntity entity = new CustomUrlInfoEntity()
        entity.setChannel(channel)
        entity.setUrlName(urlName)
        entity.setUrl(url)
        expect:
        def count = customUrlInfoManager.insert(entity)
        print(count)
        where:
        channel | urlName | url
        ChannelEnum.feishu | "CRM首页【PC端】" | "/hcrm/feishu"
        ChannelEnum.feishu | "CRM首页【移动端】" | "/hcrm/feishu"
        ChannelEnum.feishu | "CRM待办-待处理的审批流程【PC端】" | "/XV/UI/Home#crm/remind/452"
        ChannelEnum.feishu | "CRM待办-待处理的业务流程【PC端】" | "/XV/UI/Home#crm/remind/457"
        ChannelEnum.feishu | "CRM待办-待处理的阶段任务【PC端】" | "/XV/UI/Home#crm/remind/460"
        ChannelEnum.feishu | "CRM待办-待审批的工单【PC端】" | "/XV/UI/Home#crm/remind/457WaitApproval"
        ChannelEnum.feishu | "CRM待办-待处理的工单【PC端】" | "/XV/UI/Home#crm/remind/457WaitDeal"
        ChannelEnum.feishu | "CRM提醒-CRM通知【PC端】" | "/XV/UI/Home#crm/remind/405"
        ChannelEnum.feishu | "CRM提醒-审批消息【PC端】" | "/XV/UI/Home#crm/remind/454"
        ChannelEnum.feishu | "CRM提醒-工作流消息【PC端】" | "/XV/UI/Home#crm/remind/458"
        ChannelEnum.feishu | "CRM待办列表【移动端】" | "/hcrm/feishu?_menuType=1&appType=CRM&beforeHash=uipaas_todo%2Fpages%2Fuipaas_todo_list%2Findex&hash=uipaas_custom%2Fpages%2Fuipaas_todo_list%2Findex#/uipaas_custom/pages/uipaas_todo_list/index"
    }

    def "queryEntities"() {
        expect:
        def count = customUrlInfoManager.queryEntities(ChannelEnum.feishu)
        print(count)
    }
}
