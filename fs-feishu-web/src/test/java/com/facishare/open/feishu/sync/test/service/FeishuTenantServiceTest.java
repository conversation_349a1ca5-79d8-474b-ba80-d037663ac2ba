package com.facishare.open.feishu.sync.test.service;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.QueryTenantInfoData;
import com.facishare.open.feishu.syncapi.service.FeishuTenantService;
import org.junit.Test;

import javax.annotation.Resource;

public class FeishuTenantServiceTest extends BaseTest {
    @Resource
    private FeishuTenantService feishuTenantService;

    @Test
    public void queryTenantInfo() {
        Result<QueryTenantInfoData> result = feishuTenantService.queryTenantInfo("cli_a20192f6afb8d00c","11b1a8c266da9758");
        System.out.println(result);
    }
}
