package com.facishare.open.feishu.web.handler;

import com.facishare.open.feishu.web.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;

public class OrderPaidEventHandlerTest extends BaseTest {
    @Resource
    private OrderPaidEventHandler orderPaidEventHandler;
    @Test
    public void handle(){
        String json = "{\"tenant_key\":\"11b6fe24b58e175d\",\"buy_type\":\"buy\",\"create_time\":\"1659943372\",\"src_order_id\":\"\",\"buy_count\":1,\"type\":\"order_paid\",\"seats\":0,\"pay_time\":\"1659943371\",\"order_pay_price\":0,\"price_plan_id\":\"price_a25f5f67a172d00e\",\"price_plan_type\":\"trial\",\"app_id\":\"cli_a20192f6afb8d00c\",\"order_id\":\"7129402494660526082\"}";
        String handle = orderPaidEventHandler.handle(null, json);
        System.out.println(handle);
    }
}
