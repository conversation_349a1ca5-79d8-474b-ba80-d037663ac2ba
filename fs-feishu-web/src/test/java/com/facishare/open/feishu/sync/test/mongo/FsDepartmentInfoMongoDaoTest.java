package com.facishare.open.feishu.sync.test.mongo;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.sync.mongo.dao.FsDepartmentInfoMongoDao;
import com.facishare.open.feishu.sync.mongo.document.FsDepartmentInfoDoc;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.LinkedList;
import java.util.List;

public class FsDepartmentInfoMongoDaoTest extends BaseTest {
    @Autowired
    private FsDepartmentInfoMongoDao fsDepartmentInfoMongoDao;

    @Test
    public void batchReplace() {

    }

    @Test
    public void countDocuments() {
        Long counts = fsDepartmentInfoMongoDao.countDocuments("fszdbd2575");
        System.out.println(counts);
    }

    @Test
    public void deleteNotInCollectionDocs() {
        DeleteResult deleteResult = fsDepartmentInfoMongoDao.deleteNotInCollectionDocs("85903", new LinkedList<>());
        System.out.println(deleteResult);
    }

    @Test
    public void addIndex() {
        fsDepartmentInfoMongoDao.addIndex();
    }
}
