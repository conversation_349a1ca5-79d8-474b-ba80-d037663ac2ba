package com.facishare.open.feishu.web.controller;

import com.facishare.open.feishu.syncapi.model.jsapi.JsApiSignatureModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.JsApiService;
import com.facishare.open.feishu.web.BaseTest;
import com.facishare.open.feishu.web.controller.outer.feishu.FeishuWeb2Controller;
import org.junit.Test;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

public class FeishuWeb2ControllerTest extends BaseTest {
    @Resource
    private FeishuWeb2Controller feishuWeb2Controller;
    @Resource
    private JsApiService jsApiService;


    @Test
    public void uploadEmployeesBindSyncFile() throws IOException {
        String filePath = "D:\\Temp\\employeeBindSyncFile.xlsx";
        MultipartFile multipartFile = convert(filePath);
        Result<Void> result = feishuWeb2Controller.uploadEmployeesBindSyncFile(multipartFile);
        System.out.println(result);
    }

    @Test
    public void getJsApiSignature() throws IOException {
        Result<JsApiSignatureModel> result = jsApiService.getJsApiSignature2("cli_a3ddeb52763b100c",
                "85903",
                null,
                "https://crm.ceshi112.com/hcrm/feishu");
        System.out.println(result);
    }

    private static MultipartFile convert(String filePath) throws IOException {
        File file = new File(filePath);
        FileInputStream input = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile("file",
                file.getName(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", input);
        return multipartFile;
    }
}
