package com.facishare.open.feishu.sync.test.service;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.ToolsService;
import org.junit.Test;

import javax.annotation.Resource;

public class ToolsServiceTest extends BaseTest {
    @Resource
    private ToolsService toolsService;

    @Test
    public void updateAppInfo() {
        Result<Void> result = toolsService.initEnterpriseCache("85903",null);
        System.out.println(result);
    }

    @Test
    public void clearEnterpriseInfoCache() {
        Result<Void> result = toolsService.clearEnterpriseInfoCache("85903",null, 4);
        System.out.println(result);
    }

    @Test
    public void queryFsEnterpriseOpen() {
        Result<String> result = toolsService.queryFsEnterpriseOpen("F385026706");
        System.out.println(result);
    }

    @Test
    public void queryFsEmployeeOpen() {
        Result<String> result = toolsService.queryFsEmployeeOpen("F385026706", "13711911803");
        System.out.println(result);
    }

    @Test
    public void queryEnterpriseBindType() {
        Result<String> result = toolsService.queryEnterpriseBindType("fszdbd2575",null);
        System.out.println(result);
    }

    @Test
    public void queryFsEmployeeStatus() {
        Result<String> result = toolsService.queryFsEmployeeStatus("F385026706", "13711911803");
        System.out.println(result);
    }

    @Test
    public void pushCorpBindData2Cloud() {
        toolsService.pushCorpBindData2Cloud("https://crm.ceshi112.com");
    }
}
