package com.facishare.open.feishu.sync.service.whatsapp

import com.alibaba.fastjson.JSONObject
import com.facishare.open.feishu.syncapi.data.whatsapp.WhatsappGetMedia
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity
import com.facishare.open.feishu.syncapi.enums.ChannelEnum
import com.facishare.open.feishu.syncapi.event.WhatsappEvent
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappEventService
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class WhatsappEventServiceImplTest extends Specification {
    @Resource
    private WhatsappEventService whatsappEventService

    def "WhatsappGetMedia"() {
        expect:
//        String text = "{\"messaging_product\":\"whatsapp\",\"metadata\":{\"display_phone_number\":\"8613520614030\",\"phone_number_id\":\"141530859053087\"},\"statuses\":[{\"meta_message_id\":\"wamid.HBgNODYxNzcwNDA3OTQzMxUCABEYEkNDNUVEMUI5NjU4M0I0MDMzMQA=\",\"id\":\"wamid.a8d117923f60416ea010c21a73fd7fe2\",\"recipient_id\":\"8617704079433\",\"status\":\"read\",\"timestamp\":\"1703680007\"}],\"app_id\":\"994\",\"business_phone\":\"8613520614030\",\"merchant_phone\":\"8613520614030\",\"channel\":2}"
//        String text = "{\"messaging_product\":\"whatsapp\",\"contacts\":[{\"wa_id\":\"8617704079433\",\"profile\":{\"name\":\"centerhahaha\"}}],\"messages\":[{\"from\":\"8617704079433\",\"id\":\"wamid.HBgNODYxNzcwNDA3OTQzMxUCABIYIDA5MEI3QjdBM0Q0QkQ1M0U3NEUyMjAwMzVENjgzOTQwAA==\",\"timestamp\":\"1703757313\",\"type\":\"image\",\"image\":{\"mime_type\":\"image/jpeg\",\"sha256\":\"eY+A9g0kcDqAdoaWBZ7rs3Jv05c2IJX3qpoeX2K5WHU=\",\"id\":\"901552208143009\"},\"cost\":{\"currency\":\"CNY\",\"price\":0,\"foreign_price\":0,\"cdr_type\":1,\"message_id\":\"wamid.HBgNODYxNzcwNDA3OTQzMxUCABIYIDA5MEI3QjdBM0Q0QkQ1M0U3NEUyMjAwMzVENjgzOTQwAA==\",\"direction\":2}}],\"metadata\":{\"display_phone_number\":\"8613520614030\",\"phone_number_id\":\"141530859053087\"},\"app_id\":\"994\",\"business_phone\":\"8613520614030\",\"merchant_phone\":\"8613520614030\",\"channel\":2}"
//        String text = "{\"messaging_product\":\"whatsapp\",\"contacts\":[{\"wa_id\":\"8617704079433\",\"profile\":{\"name\":\"centerhahaha\"}}],\"messages\":[{\"from\":\"8617704079433\",\"id\":\"wamid.HBgNODYxNzcwNDA3OTQzMxUCABIYIEU1RjM5QjU5QTZERDk5QTNGNkQ1MzAwM0JFNDMxMTQ4AA==\",\"timestamp\":\"1704193304\",\"type\":\"image\",\"image\":{\"mime_type\":\"image/jpeg\",\"sha256\":\"2KmRxckCtaal5oEltCWU9NubQTNffs5xgdZk5cjigGg=\",\"id\":\"1033057894445051\"},\"cost\":{\"currency\":\"CNY\",\"price\":0,\"foreign_price\":0,\"cdr_type\":1,\"message_id\":\"wamid.HBgNODYxNzcwNDA3OTQzMxUCABIYIEU1RjM5QjU5QTZERDk5QTNGNkQ1MzAwM0JFNDMxMTQ4AA==\",\"direction\":2}}],\"metadata\":{\"display_phone_number\":\"8613520614030\",\"phone_number_id\":\"141530859053087\"},\"app_id\":\"994\",\"business_phone\":\"8613520614030\",\"merchant_phone\":\"8613520614030\",\"channel\":2}";
//        String text = "{\"contacts\":[{\"profile\":{\"name\":\"Jay\"},\"wa_id\":\"852xxxx705\"}],\"messages\":[{\"from\":\"8617704079433\",\"id\":\"ABGHhhdgYFCBnwIQ1kuKFSU5LfuDxSUPOjKIwA\",\"sticker\":{\"id\":\"1033057894445051\",\"metadata\":{\"emojis\":[\"☕\",\"🙂\"],\"is-first-party-sticker\":1,\"sticker-pack-id\":\"whatsappcuppy\"},\"mime_type\":\"image/webp\",\"sha256\":\"98267fedaeac67a4cc6b5e18a2444249fba5b6363a690115139675d53a63b0ff\"},\"timestamp\":\"1663054803\",\"type\":\"sticker\",\"cost\":{\"currency\":\"USD\",\"price\":0,\"foreign_price\":0,\"cdr_type\":4,\"message_id\":\"ABGHhhdgYFCBnwIQ1kuKFSU5LfuDxSUPOjKIwA\",\"direction\":1}}],\"merchant_phone\":\"8613520614030\",\"messaging_product\":\"whatsapp\"}";
        String text = "{\"messaging_product\":\"whatsapp\",\"metadata\":{\"display_phone_number\":\"8613520614030\",\"phone_number_id\":\"141530859053087\"},\"statuses\":[{\"meta_message_id\":\"wamid.HBgNODYxNTMwMjYwODA3NBUCABEYEjIyMkEzNzM2RkY4MDE1MjFDNwA=\",\"id\":\"wamid.b5a2a37748d0498782db46d507d9ab14\",\"recipient_id\":\"8615302608074\",\"status\":\"read\",\"timestamp\":\"1705045387\",\"conversation\":{\"id\":\"bd1a7060998efdb8a9a0323d7bb98d0a\",\"origin\":{\"type\":\"marketing\"}}}],\"app_id\":\"994\",\"business_phone\":\"8613520614030\",\"merchant_phone\":\"8613520614030\",\"channel\":2}";
        WhatsappEvent whatsappEventData = JSONObject.parseObject(text, WhatsappEvent.class)
        def a = whatsappEventService.whatsappEventHandle(whatsappEventData)
        print(a)
    }
}
