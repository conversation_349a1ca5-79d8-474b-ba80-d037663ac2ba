package com.facishare.open.feishu.sync.test.manager;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.enums.BindStatusEnum;
import org.junit.Test;

import javax.annotation.Resource;

public class EnterpriseBindManagerTest extends BaseTest {
    @Resource
    private EnterpriseBindManager enterpriseBindManager;

    @Test
    public void updateBindStatus() {
        int status = enterpriseBindManager.updateBindStatus("fsea",null, BindStatusEnum.stop);
        System.out.println(status);
    }

    @Test
    public void getEntity() {
        EnterpriseBindEntity enterpriseBindEntity = enterpriseBindManager.getEntity("82777");
        System.out.println(enterpriseBindEntity);
    }
}
