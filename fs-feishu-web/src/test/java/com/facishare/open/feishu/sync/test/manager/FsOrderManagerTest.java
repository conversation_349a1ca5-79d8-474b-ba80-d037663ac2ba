package com.facishare.open.feishu.sync.test.manager;

import com.facishare.open.feishu.sync.manager.FsOrderManager;
import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.paas.license.pojo.ModuleFlag;
import org.junit.Test;

import javax.annotation.Resource;

public class FsOrderManagerTest extends BaseTest {
    @Resource
    private FsOrderManager fsOrderManager;

    @Test
    public void judgeFeishuConnectorModule() {
        ModuleFlag moduleFlag = fsOrderManager.judgeFeishuConnectorModule("81243");
        System.out.println(moduleFlag);
    }
}
