package com.facishare.open.feishu.web.controller;

import com.facishare.open.feishu.syncapi.model.jsapi.JsApiSignatureModel;
import com.facishare.open.feishu.syncapi.model.login.FsUserModel;
import com.facishare.open.feishu.syncapi.model.login.LoginAuthModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.Code2UserInfoData;
import com.facishare.open.feishu.syncapi.service.LoginService;
import com.facishare.open.feishu.web.BaseTest;
import com.facishare.open.feishu.web.controller.outer.feishu.FeishuEventController;
import com.facishare.open.feishu.web.controller.outer.feishu.FeishuExternalController;
import com.facishare.open.feishu.web.controller.outer.feishu.FeishuInternalController;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import org.apache.zookeeper.Login;
import org.junit.Test;

import javax.annotation.Resource;

public class FeishuEventControllerTest extends BaseTest {
    @Resource
    private FeishuEventController feishuEventController;
    @Resource
    private FeishuInternalController feishuInternalController;
    @Resource
    private FeishuExternalController feishuExternalController;
    @Resource
    private LoginService loginService;

    @Resource
    private I18NStringManager i18NStringManager;

    @Test
    public void push() {
        String json = "{\"encrypt\":\"f3geJabFtna7oDOYb73Lhm2ALJ6S7sZIiJcQNK9WvSFhIErQhtqVzPXlpRTaBxrndV5rHFFhPnVMa5ieJoBuajkxvZetCe0gNDSyuxVO0mFIMC4XzH+utkIHqzTmp5hrmdIp/MlRbQ1V5PYl0H5BSYuWrGIF3qIrwBEotkpB+Beog8EHbuySd+v6z8yPI2wL1uiPMRAf+RyS4yF6C6sxniPtEDzPXuqU6fmKXnWEvI6XjAkbkqtgKZybz0enkXJxG8xMgaP5y/Ue2NaG1UAu6sU7OHrc8GQwOlGpsEIAeGLo4hgizX4R1kYmuQ9+iaLP8yx/BaCuNJ0m6l+oc8I9XLURlqcjvYQmR4DZ9LmcNdWyrbeZObt+6x6eFbOlk4sllQbnSafrS/hF1zzpTGyxYM+OavVAyNtZz8wyHq6ySoXzfx1x12IEXiopxa067PBTwHZSKfe1AwMk+Law6BPY+H2A5OEQ8YQn+E6tIGBVeHibu23yPlO337CwgnyK6uuixnN86AJ9gwhzifhRnDtyxDSzHfPhsIZH/+4FJZafXdjyjPDsOVHnVrtTLUZRgEuxdUZQ+6kse8n8EYq4xpA7jrKYQzjJx5fyfXY9npmg4pQw5gnaI5dNhlsPHA53iMk06zA0og7ZWbqfRQVH8V3R67dSZjQ+PveV4TWtPA9r9OEyHd2nGoKQib7mkBinqpDWuuz9VAMQCfMXkwCpnNY4SHj0ZLDQnGFqlMMrSYkFAT5p5IujyPE4Rv/+R8r9Um03Wbv+nzUpc69EdH9C1048+r+a4M8swGf8Q1WxVmSoPrgn5SkMfc7njRKu7xAklpEjLD4locevBUx+9xJ2um6rQcJBaCtYbJ/VcUCTsrF3uT1sY55gLm6WYO2HADMwTrfFialZSvG/FuY8A4bhvzZHQ2dnmmvYcYhCTw75cDK2kthhQPYVQe3D7V0cu/NduLdGhIWcr2CF/eDpsBuaU8D6pB/km/7AIXYox238GakS8jvIrBffn9/DRusvdJuyzXrlZ1KI8fd2giqYzbEcLak0agwFk3TJYVkG++RcIm+EHcvMYfkxhgO+OG52zMyIhYAa6eVCqL3nK6V95UJwPpD56LuhIkwImGoEblGxBVI9hitmmVt/7f2/u/9r/yb4z7zu8DELRk+AgjhIPHTWXEdmqTD2SwcWK2ujq+LhALw0PMFdbqRHxPcdpOAmBEF19jZ7evZizvWeIUfkrgibuiuh+6Yx2blbrrHDA0CzDIbZHy5/xpPN14kw9wpLi051VLLw7gAyN8S3LrMx/cxXuIyBh3n0emKYJ3hqsmVXzZwsw54irkysc9Q7qGkqFT0ttr8ZB47N5RDp3S/MUoNkNlBvZ/xruce7gnBEOS7UKSXIP4bMADkh/Xo1vj2Uk9VR7UIRJ42kjk+7+KnjIHhSaCqkFXeLal6UjR7c9tLoJK8Qrdjf9MRi8TKCdUri5u/O3o1Kj4P7uWPANAyw2Gk+63cQBjgRtaS5WxBeq/ePhjO5/2tKoDtNJaWVtcYHjgIqcRNr5Y8Vsa9LrxvrXgzoSmi3CTSXeqkM4Kt/Ze+TMo/kRlMvspsDfLZclfKBoJShL/+LgmHohyh7Tk3v8RZmLnfjyPvBqm4SxEAeVMo54a86m4Nv0ZRGaiEgv336OSZwiXGCsLNmz2iMHCmryLajQe5MRUJY14HarBEPoia/cE0Zf6eVtdPM/+56xSOq7GDHsy1hSCIFqAVDlHKeUiEk33vsLYyHJ3+0G0KhCkyCjLn1IMqeW5iNexsL+anV6IYM4ph0yere1q8DXwryN0Fo7xJy1bwa8OBy4dG7Lki6UBztjNAkJ1SXq4/0Is5+JxqEhnMHyr9cgtBawC5xW7pobPZ9Ky0+mDaXO7vR7KqRHD0+H4rOyWLidqtprs6XJ693NX0rrpiNECgivcF0UVeUXEcAuRej7L3XDf+5k532dm3kvMAydbZMr9jVOMoWPbQN/6x2\"}";
        String json1 = "{\"encrypt\":\"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\"}";
        String json2 = "{\"encrypt\":\"a9sRQxF9e9fWitoRryYFsAlW+G4uGI30XNb1dE/fbzFTbhSTetKN6RWHbKpYlvZ7DdpUOjPuhF6PGXazgVz794U6MMU8Wbilh3CkzeS1VgeGSSmd0l5vp7bgw2ueFIAw62eoo8k9gA3SbJwwih7RuXMd+LAvdEHi6ezx6hrMFFippqdzgLpLGxbIMEDOEOTOWN23XXuTUrff+n0utO6a/w==\"}";
        String push = feishuEventController.push(json2);
        System.out.println(push);
    }

    @Test
    public void getJsApiSignature() {
        Result<JsApiSignatureModel> push = feishuInternalController.getJsApiSignature("cli_a20192f6afb8d00c",
                "https://crm.ceshi112.com/hcrm/index", null,null);
        System.out.println(push);
    }

    @Test
    public void code2UserInfo() {
        Result<Code2UserInfoData> result = loginService.code2UserInfo(LoginAuthModel.builder().code("87a9dc03be1d1cc690dd18bf90397bcd").build());
        System.out.println(result);
    }

    @Test
    public void getFsUser() {
        Result<FsUserModel> result = loginService.getFsUser("06ebd75c1fa88e82804116c0b6f67b75");
        System.out.println(result);
    }

    @Test
    public void i18NStringManagerGet() {
        String result = i18NStringManager.get("erpdss_outer_oa_connector.feishu.global.s4", "en", "88521", "");
        System.out.println(result);
        String defaultLang = i18NStringManager.getDefaultLang("88521");
        System.out.println(defaultLang);
    }
}
