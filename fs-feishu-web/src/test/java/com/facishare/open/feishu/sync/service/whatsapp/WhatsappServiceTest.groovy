package com.facishare.open.feishu.sync.service.whatsapp;

import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.syncapi.data.whatsapp.*
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappService;
import com.google.common.collect.Lists;
import org.springframework.test.context.ContextConfiguration;
import spock.lang.Specification;

import javax.annotation.Resource;
import java.nio.file.Files;

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class WhatsappServiceTest extends Specification {
    @Resource
    private WhatsappService whatsappService;
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    def "whatsappSendMsg"() {
        expect:
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, "84883", null)
        WhatsappSendMsg s = new WhatsappSendMsg()
        s.setFsEa("84883")
        s.setAppkey("r82UO8uO")
        s.setBusiness_phone("8613520614030")
        s.setType("text")
        s.setMessaging_product("whatsapp")
        s.setRecipient_type("individual")
        s.setTo("8617704079433")
        WhatsappSendMsg.TextModel text = new WhatsappSendMsg.TextModel()
        text.setBody("一去二三里，烟村四五家，亭台六七座，八九十枝花")
        s.setText(text)
        def a = whatsappService.whatsappSendMsg(entity, s)
        print(a)
    }

    def "whatsappUploadFile"() {
        expect:
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, "84883", null)
        WhatsappUploadMedia s = new WhatsappUploadMedia()
        s.setFsEa("84883")
        s.setBusiness_phone("8613520614030")
        s.setType("image/png")
        s.setMessaging_product("whatsapp")
        s.setFileName("test001")
        // 将文件读取为byte数组
        File file = new File("D:\\Temp\\GetByPath.png");
        byte[] fileContent;
        try {
            fileContent = Files.readAllBytes(file.toPath());
        } catch (IOException e) {
            // 处理异常，可能是文件未找到或无法读取
            e.printStackTrace();
            return;
        }
        s.setFile(fileContent)
        def a = whatsappService.whatsappUploadFile(entity, s)
        print(a)
    }

    def "whatsappGetTemplate"() {
        expect:
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, "84883", null)
        WhatsappGetTemplate s = new WhatsappGetTemplate()
        s.setFsEa("84883")
        s.setAppkey("r82UO8uO")
        s.setBusiness_phone("8613520614030")
        s.setMessaging_product("whatsapp")
        def a = whatsappService.whatsappGetTemplate(entity, s)
        print(a)
    }

    def "whatsappCreateTemplate"() {
        expect:
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, "84883", null)
        WhatsappCreateTemplate s = new WhatsappCreateTemplate()
        s.setFsEa("84883")
        s.setAppkey("r82UO8uO")
        s.setBusiness_phone("8613520614030")
        s.setMessaging_product("whatsapp")
        s.setCategory("MARKETING")
        s.setLanguage("zh_CN")
        s.setName("i_am_iron_man_001")
        Component component = new Component();
        component.setType("HEADER")
        component.setFormat("IMAGE")
        Component.Example example = new Component.Example()
        example.setHeader_handle("4:R2V0QnlQYXRoLnBuZw==:aW1hZ2UvcG5n:ARZKXLX1xTWM_ghw664quQDYVef_PoFhe4UxfbkgVO5R0WMKKqy_x61IWDjYUFc8V2B72qgZan3vmwIYFndrTOkaZjuQKDX3Cct8yfols9ZzKQ:e:1703919094:1407008009801826:100084713375965:ARaNeF4h-MKGA3vIhQg")
        component.setExample(example)

        Component component1 = new Component();
        component1.setType("BODY")
//        component1.setFormat("TEXT")
        component1.setText("are you ok?")

        s.setComponents(Lists.newArrayList(component, component1))

        def a = whatsappService.whatsappCreateTemplate(entity, s)
        print(a)
    }

    def "whatsappUploadTemplateFile"() {
        expect:
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, "84883", null)
        WhatsappUploadMedia s = new WhatsappUploadMedia()
        s.setFsEa("84883")
        s.setBusiness_phone("8613520614030")
        s.setType("image/png")
        s.setMessaging_product("whatsapp")
        s.setFileName("GetByPath.png")
        // 将文件读取为byte数组
        File file = new File("D:\\Temp\\GetByPath.png");
        byte[] fileContent;
        try {
            fileContent = Files.readAllBytes(file.toPath());
        } catch (IOException e) {
            // 处理异常，可能是文件未找到或无法读取
            e.printStackTrace();
            return;
        }
        s.setFile(fileContent)
        def a = whatsappService.whatsappUploadTemplateFile(entity, s)
        print(a)
    }

    def "whatsappUpdateTemplate"() {
        expect:
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, "84883", null)
        WhatsappUpdateTemplate s = new WhatsappUpdateTemplate()
        s.setFsEa("84883")
        s.setAppkey("r82UO8uO")
        s.setBusiness_phone("8613520614030")
        s.setMessaging_product("whatsapp")
//        s.setCategory("MARKETING")
//        s.setLanguage("zh_CN")
//        s.setName("I am Iron Man")
        //381850674397724
        s.setTemplate_id("1368090297175562")
//        Component component = new Component();
//        component.setType("HEADER")
//        component.setFormat("IMAGE")
//        Component.Example example = new Component.Example()
//        example.setHeader_handle("")
//        component.setExample(example)

        Component component1 = new Component();
        component1.setType("BODY")
//        component1.setFormat("TEXT")
        component1.setText("are you ok?ddd")

        s.setComponents(Lists.newArrayList(component1))

        def a = whatsappService.whatsappUpdateTemplate(entity, s)
        print(a)
    }

    def "whatsappDeleteTemplate"() {
        expect:
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, "84883", null)
        WhatsappDeleteTemplate s = new WhatsappDeleteTemplate()
        s.setFsEa("84883")
        s.setAppkey("r82UO8uO")
        s.setBusiness_phone("8613520614030")
        s.setMessaging_product("whatsapp")
        s.setId("1368090297175562")
        s.setName("i_am_iron_man_001")
        def a = whatsappService.whatsappDeleteTemplate(entity, s)
        print(a)
    }

    def "WhatsappGetMedia"() {
        expect:
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, "84883", null)
        WhatsappGetMedia s = new WhatsappGetMedia()
        s.setFsEa("84883")
        s.setBusiness_phone("8613520614030")
        s.setMessaging_product("whatsapp")
        s.setMedia_id("884786056381519")
        def a = whatsappService.whatsappGetMedia(entity, s)
        print(a)
    }

    def "whatsappGetBalance"() {
        expect:
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, "82846", null)
        WhatsappBindInfo bindInfo = new WhatsappBindInfo();
        bindInfo.setFsEa(entity.getFsEa());
        bindInfo.setAppKey(entity.getAppKey());
        bindInfo.setAccessKey(entity.getAccessKey());
        bindInfo.setAccessSecret(entity.getAccessSecret());
        bindInfo.setNxAppKey(entity.getNxAppKey());
        bindInfo.setNxSecretKey(entity.getNxSecretKey());
        def a = whatsappService.whatsappGetBalance(bindInfo)
        print(a)
    }
}
