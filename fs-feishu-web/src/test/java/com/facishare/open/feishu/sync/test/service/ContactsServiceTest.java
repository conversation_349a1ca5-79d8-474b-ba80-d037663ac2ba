package com.facishare.open.feishu.sync.test.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.model.ContactScopeModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.DepartmentData;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.google.gson.Gson;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class ContactsServiceTest extends BaseTest {
    @Resource
    private ContactsService contactsService;

    @Test
    public void initContactsAsync() {
        Result<Void> result = contactsService.initContactsAsync("fsfxcs8780");
        System.out.println(result);
    }

    @Test
    public void addUserList() {
        String json = "[{\"avatar\":{\"avatar_240\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_e2064861-843a-4f42-b42c-821255b281eg~?image_size=240x240\\u0026cut_type=\\u0026quality=\\u0026format=png\\u0026sticker_format=.webp\",\"avatar_640\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_e2064861-843a-4f42-b42c-821255b281eg~?image_size=640x640\\u0026cut_type=\\u0026quality=\\u0026format=png\\u0026sticker_format=.webp\",\"avatar_72\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_e2064861-843a-4f42-b42c-821255b281eg~?image_size=72x72\\u0026cut_type=\\u0026quality=\\u0026format=png\\u0026sticker_format=.webp\",\"avatar_origin\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_e2064861-843a-4f42-b42c-821255b281eg~?image_size=noop\\u0026cut_type=\\u0026quality=\\u0026format=png\\u0026sticker_format=.webp\"},\"city\":\"\",\"country\":\"\",\"description\":\"\",\"employee_no\":\"\",\"employee_type\":1,\"en_name\":\"\",\"gender\":0,\"job_title\":\"\",\"join_time\":0,\"name\":\"李玲\",\"open_id\":\"ou_7f23d6d54ecbb30c8cb6ade6ed9656c0\",\"status\":{\"is_activated\":true,\"is_exited\":false,\"is_frozen\":false,\"is_resigned\":false,\"is_unjoin\":false},\"union_id\":\"on_412b8bc893da4d760e9c413985dbed73\",\"user_id\":null,\"work_station\":\"\"}]";
        List<UserData.User> userList = JSONObject.parseArray(json, UserData.User.class);
        userList.clear();
        UserData.User user = new UserData.User();
        user.setOpenId("ou_660dd99854e3c3f289117bbad64a931d");
        user.setUnionId("on_5128145bc0ac4d18f7e975faeb25ccc8");
        user.setName("海洋");
        user.setGender(0);
        userList.add(user);
        Result<Void> result = contactsService.addUserList(testAppId,testTenantKey,userList);
        System.out.println(result);
    }

    @Test
    public void addDepList() {
        String json = "[{\"open_department_id\":\"od-2210d8bfd7675447e7edfcb7e4051bb7\",\"department_id\":\"51ed62ff56fd38cc\",\"i18n_name\":{\"en_us\":\"\",\"zh_cn\":\"开发2部\",\"ja_jp\":\"\"},\"name\":\"开发2部\",\"leader_user_id\":\"ou_d9292ea3f4405f4663083a15ffd9a333\",\"member_count\":2,\"chat_id\":\"oc_00ec955f1fe959c4c7b029c461d5e27c\",\"order\":\"4000\",\"parent_department_id\":\"0\",\"status\":{\"is_deleted\":false}}]";
        List<DepartmentData.Department> departmentDataList = JSONObject.parseArray(json, DepartmentData.Department.class);
        Result<Void> result = contactsService.addDepList("cli_a20192f6afb8d00c","11b1a8c266da9758",departmentDataList);
        System.out.println(result);
    }

    @Test
    public void removeDepList() {
        String json = "[{\"open_department_id\":\"od-2210d8bfd7675447e7edfcb7e4051bb7\",\"department_id\":\"51ed62ff56fd38cc\",\"i18n_name\":{\"en_us\":\"\",\"zh_cn\":\"开发2部\",\"ja_jp\":\"\"},\"name\":\"开发2部\",\"leader_user_id\":\"ou_d9292ea3f4405f4663083a15ffd9a333\",\"member_count\":2,\"chat_id\":\"oc_00ec955f1fe959c4c7b029c461d5e27c\",\"order\":\"4000\",\"parent_department_id\":\"0\",\"status\":{\"is_deleted\":false}}]";
        List<DepartmentData.Department> departmentDataList = JSONObject.parseArray(json, DepartmentData.Department.class);
        Result<Void> result = contactsService.removeDepList("cli_a20192f6afb8d00c","11b1a8c266da9758",departmentDataList);
        System.out.println(result);
    }

//    @Test
//    public void resetAdminRole() {
//        Result<Void> result = contactsService.resetAdminRole("cli_a20192f6afb8d00c","1062af3ebe60575d");
//        System.out.println(result);
//    }

    @Test
    public void resumeEmployee() {
        Result<Void> result = contactsService.resumeEmployee("1062af3ebe60575d","ou_aa13aeb82fd974a23521e4ced23d459f");
        System.out.println(result);
    }

    @Test
    public void autoGetContactData() {
        Result<Void> result = contactsService.autoGetContactData();
        System.out.println(result);
    }

    @Test
    public void saveOrUpdateContactData() {
        Result<ContactScopeModel> result = contactsService.saveOrUpdateContactData("cli_a7c94d9508f85009","146a0bcf64d7175a");
        System.out.println(result);
    }

    @Test
    public void refreshContactScopeDataCacheAsync() {
        Result<Void> result = contactsService.refreshContactScopeDataCacheAsync("contact.scope.updated_v3","cli_a3ddeb52763b100c","100d08b69448975d","{test}");
        System.out.println(result);
    }

    @Test
    public void saveOrUpdateContactUser() {
        Result<Void> result = contactsService.saveOrUpdateContactUser("contact.user.created_v3","cli_a3ddeb52763b100c","100d08b69448975d",null, null, "");
        System.out.println(result);
    }

    @Test
    public void deleteContactUser() {
        UserData.User user = new Gson().fromJson("{\"name\":\"小田\",\"en_name\":\"\",\"mobile_visible\":true,\"gender\":0,\"avatar\":{\"avatar_240\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_45f0085d-60f2-4b9f-894b-56a1d392e19g~?image_size\\u003d240x240\\u0026cut_type\\u003d\\u0026quality\\u003d\\u0026format\\u003dpng\\u0026sticker_format\\u003d.webp\",\"avatar_640\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_45f0085d-60f2-4b9f-894b-56a1d392e19g~?image_size\\u003d640x640\\u0026cut_type\\u003d\\u0026quality\\u003d\\u0026format\\u003dpng\\u0026sticker_format\\u003d.webp\",\"avatar_72\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_45f0085d-60f2-4b9f-894b-56a1d392e19g~?image_size\\u003d72x72\\u0026cut_type\\u003d\\u0026quality\\u003d\\u0026format\\u003dpng\\u0026sticker_format\\u003d.webp\",\"avatar_origin\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_45f0085d-60f2-4b9f-894b-56a1d392e19g~?image_size\\u003dnoop\\u0026cut_type\\u003d\\u0026quality\\u003d\\u0026format\\u003dpng\\u0026sticker_format\\u003d.webp\"},\"status\":{\"is_activated\":false,\"is_exited\":false,\"is_frozen\":false,\"is_resigned\":false,\"is_unjoin\":false},\"department_ids\":[\"od-23f0a91fc17c969a39d8aa43a6cd8361\"],\"city\":\"\",\"country\":\"\",\"work_station\":\"\",\"join_time\":\"Jan 1, 1970 8:00:00 AM\",\"is_tenant_manager\":true,\"employee_no\":\"\",\"employee_type\":1,\"orders\":[{\"department_id\":\"od-23f0a91fc17c969a39d8aa43a6cd8361\",\"department_order\":1,\"user_order\":0}],\"job_title\":\"\",\"unionId\":\"on_3e92a0c7d8a7c44bd29bec93e1b0ed4d\",\"openId\":\"ou_c83b9f3b92d1b1e4a9677e90c131d80a\"}", UserData.User.class);
        Result<Void> result = contactsService.deleteContactUser("cli_a3ddeb52763b100c","100d08b69448975d", user);
        System.out.println(result);
    }

    @Test
    public void deleteContactDepartment() {
        DepartmentData.Department department = new Gson().fromJson("{\"name\":\"测试部\",\"parentDepartmentId\":\"0\",\"departmentId\":\"gffee66da84e3cca\",\"openDepartmentId\":\"od-a8d4f07729fbc2d30e6947c5dcdc23cf\",\"chatId\":\"oc_ab305ebbdc20c7469c65dd36d9465d7a\",\"order\":\"2000\",\"memberCount\":2,\"i18nName\":{\"zhCn\":\"\",\"jaJp\":\"\",\"enUs\":\"\"},\"status\":{\"isDeleted\":false}}", DepartmentData.Department.class);
        Result<Void> result = contactsService.deleteContactDepartment("cli_a3ddeb52763b100c","100d08b69448975d", department);
        System.out.println(result);
    }

    @Test
    public void saveOrUpdateContactDepartment() {
        Result<Void> result = contactsService.saveOrUpdateContactDepartment("contact.department.created_v3","cli_a3ddeb52763b100c","100d08b69448975d",null , null, "");
        System.out.println(result);
    }
}
