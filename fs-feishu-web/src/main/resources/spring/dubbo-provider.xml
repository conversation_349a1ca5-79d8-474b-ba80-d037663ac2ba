<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <description>dubbo提供者接口</description>

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.AppService"-->
<!--                   ref="appService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.CorpService"-->
<!--                   ref="corpService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.OrderService"-->
<!--                   ref="orderService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.EnterpriseBindService"-->
<!--                   ref="enterpriseBindService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.DepartmentBindService"-->
<!--                   ref="departmentBindService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.EmployeeBindService"-->
<!--                   ref="employeeBindService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.LoginService"-->
<!--                   ref="loginService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.JsApiService"-->
<!--                   ref="jsApiService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.ContactsService"-->
<!--                   ref="contactsService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.order.contacts.proxy.api.service.FsEventService"-->
<!--                   ref="fsEventService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.MsgService"-->
<!--                   ref="msgService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.SuperAdminService"-->
<!--                   ref="superAdminService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.ExternalMsgService"-->
<!--                   ref="externalMsgService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.ExternalTodoMsgService"-->
<!--                   ref="externalTodoMsgService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.NotificationService"-->
<!--                   ref="notificationService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.ToolsService"-->
<!--                   ref="toolsService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.MonitorService"-->
<!--                   ref="monitorService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappService"-->
<!--                   ref="whatsappService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappEnterpriseBindService"-->
<!--                   ref="whatsappEnterpriseBindService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappEventService"-->
<!--                   ref="whatsappEventService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappSendMsgService"-->
<!--                   ref="whatsappSendMsgService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappTemplateService"-->
<!--                   ref="whatsappTemplateService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappCommonService"-->
<!--                   ref="whatsappCommonService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.ServiceAuthService"-->
<!--                   ref="serviceAuthService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.FeishuAppService"-->
<!--                   ref="feishuAppService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.FeishuTenantService"-->
<!--                   ref="feishuTenantService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.ExternalCalendarService"-->
<!--                   ref="externalCalendarService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.CustomService"-->
<!--                   ref="customService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.ExternalApprovalsService"-->
<!--                   ref="externalApprovalsService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="3"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.FeishuUserService"-->
<!--                   ref="feishuUserService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="1"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.FeishuDepartmentService"-->
<!--                   ref="feishuDepartmentService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="1"/>-->

<!--    <dubbo:service interface="com.facishare.open.feishu.syncapi.service.FeishuUserGroupService"-->
<!--                   ref="feishuUserGroupService"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="300000"-->
<!--                   version="1.0"-->
<!--                   retries="1"/>-->
</beans>
