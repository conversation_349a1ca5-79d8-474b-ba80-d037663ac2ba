package com.facishare.open.feishu.web.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.open.feishu.syncapi.model.event2.FeishuContactDepartmentDeletedV3Event;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.facishare.open.feishu.syncapi.service.EmployeeBindService;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 员工离职处理器
 * <AUTHOR>
 * @date 20220818
 */
@Slf4j
@Component
public class ContactDepartmentDeletedV3EventHandler extends FeishuEventHandler {
    @Resource
    private ContactsService contactsService;
    @Resource
    private EnterpriseBindService enterpriseBindService;
    @Resource
    private EmployeeBindService employeeBindService;

    @Override
    public String getSupportEventType() {
        return "contact.department.deleted_v3";
    }

    @Override
    public String handle(FeishuEventModel2.EventModelHeader header, String eventData) {
        LogUtils.info("ContactDepartmentDeletedV3EventHandler.handle,eventData={}",eventData);
        FeishuContactDepartmentDeletedV3Event event = JSON.parseObject(eventData, FeishuContactDepartmentDeletedV3Event.class);
        LogUtils.info("ContactDepartmentDeletedV3EventHandler.handle,event={}",event);
        Result<Boolean> booleanResult = enterpriseBindService.hasManualBind(header.getTenantKey());
        if(!booleanResult.isSuccess()) {
            return SUCCESS;
        }
        if(booleanResult.getData()) {
            log.info("ContactDepartmentDeletedV3EventHandler.handle,outEa={},手动绑定的企业，不支持删除部门",header.getTenantKey());
            contactsService.deleteContactDepartment(header.getAppId(),
                    header.getTenantKey(),
                    event.getObject());
            return SUCCESS;
        }
        Result<Void> result = contactsService.removeDepList(header.getAppId(),
                header.getTenantKey(),
                Lists.newArrayList(event.getObject()));
        LogUtils.info("ContactDepartmentDeletedV3EventHandler.handle,removeDepList,result={}",result);
        return SUCCESS;
    }
}
