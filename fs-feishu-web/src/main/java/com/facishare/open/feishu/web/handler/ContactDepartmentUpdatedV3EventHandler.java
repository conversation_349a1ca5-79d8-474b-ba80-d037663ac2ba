package com.facishare.open.feishu.web.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.open.feishu.syncapi.model.event2.FeishuContactDepartmentUpdateV3Event;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 修改部门处理器
 * <AUTHOR>
 * @date 20220818
 */
@Slf4j
@Component
public class ContactDepartmentUpdatedV3EventHandler extends FeishuEventHandler {
    @Resource
    private ContactsService contactsService;
    @Resource
    private EnterpriseBindService enterpriseBindService;

    @Override
    public String getSupportEventType() {
        return "contact.department.updated_v3";
    }

    @Override
    public String handle(FeishuEventModel2.EventModelHeader header, String eventData) {
        LogUtils.info("ContactDepartmentUpdatedV3EventHandler.handle,eventData={}",eventData);
        FeishuContactDepartmentUpdateV3Event event = JSON.parseObject(eventData, FeishuContactDepartmentUpdateV3Event.class);
        LogUtils.info("ContactDepartmentUpdatedV3EventHandler.handle,event={}",event);
        Result<Boolean> booleanResult = enterpriseBindService.hasManualBind(header.getTenantKey());
        if(!booleanResult.isSuccess()) {
            return SUCCESS;
        }
        if(booleanResult.getData()) {
            log.info("ContactDepartmentUpdatedV3EventHandler.handle,outEa={},手动绑定的企业，不支持更新部门",header.getTenantKey());
            contactsService.saveOrUpdateContactDepartment(header.getEventType(), header.getAppId(),
                    header.getTenantKey(),
                    event.getObject(), event.getOldObject(), eventData);
            return SUCCESS;
        }
        Result<Void> result = contactsService.addDepList(header.getAppId(),
                header.getTenantKey(),
                Lists.newArrayList(event.getObject()));
        LogUtils.info("ContactDepartmentUpdatedV3EventHandler.handle,department update, not supported");
        return SUCCESS;
    }
}
