package com.facishare.open.feishu.web.controller.outer.feishu;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.syncapi.arg.FsBindWithFeishuArg;
import com.facishare.open.feishu.syncapi.arg.QueryConnectInfoArg;
import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.feishu.syncapi.info.CustomUrlInfo;
import com.facishare.open.feishu.syncapi.model.*;
import com.facishare.open.feishu.syncapi.model.connect.FeishuAppConnectParams;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.data.QueryTenantInfoData;
import com.facishare.open.feishu.syncapi.service.CustomService;
import com.facishare.open.feishu.syncapi.service.EmployeeBindService;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.feishu.syncapi.service.FeishuTenantService;
import com.facishare.open.feishu.web.arg.*;
import com.facishare.open.feishu.web.enums.UserContextSingleton;
import com.facishare.open.feishu.web.excel.BuildExcelFile;
import com.facishare.open.feishu.web.excel.ImportExcelFile;
import com.facishare.open.feishu.web.excel.service.ExcelFileService;
import com.facishare.open.feishu.web.model.AuthUserInfoModel;
import com.facishare.open.feishu.web.utils.ParallelUtils;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * 飞书WEB服务接口，需要具有管理员身份去调用
 *
 * <AUTHOR>
 * @date 20220726
 */
@Slf4j
@RestController
@RequestMapping(value = "/feishu/web")
public class FeishuWebController {
    @Resource
    private EnterpriseBindService enterpriseBindService;
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private CustomService customService;
    @Resource
    private ExcelFileService excelFileService;
    @Resource
    private RedisDataSource redisDataSource;
    @Resource
    private I18NStringManager i18NStringManager;
    @Autowired
    private FeishuTenantService feishuTenantService;
    /**
     * 纷享和飞书绑定
     *
     * @param arg 请求参数
     */
    @RequestMapping(value = "/fsBindWithFeishu", method = RequestMethod.POST)
    @ResponseBody
    public Result<CorpInfoEntity> fsBindWithFeishu(@RequestBody FsBindWithFeishuArg arg) {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        arg.setFsEa(fsEa);
        return enterpriseBindService.fsBindWithFeishu(arg);
    }

    /**
     * 纷享解除绑定
     *
     */
    @RequestMapping(value = "/fsUnBindWithFeishu", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> fsUnBindWithFeishu(@RequestParam(required = false) String fsEa,
                                           @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        FeiShuConnectParam connectParam = FeiShuConnectParam.parse(dcInfo);
        log.info("FeishuWebController.fsUnBindWithFeishu,connectParam={}",JSONObject.toJSONString(connectParam));

        if(StringUtils.isEmpty(fsEa)) {
            fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        }
        String displayId = FeiShuConnectParam.isInvalid(connectParam) ? null : connectParam.getDisplayId();
        return enterpriseBindService.fsUnBindWithFeishu(fsEa, displayId, connectParam.getDataCenterId());
    }
    /**
     * 测试appid
     *
     */
    @RequestMapping(value = "/connectToken", method = RequestMethod.POST)
    @ResponseBody
    public Result<QueryTenantInfoData.Tenant> connectToken(@RequestBody FeishuAppConnectParams feishuAppConnectParams) {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        log.info("FeishuWebController.queryConnectInfo,fsEa={}",fsEa);
        if(feishuAppConnectParams==null) {
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR);
        }

        //lark现在支持填写appid,appsecret;
        String appid=feishuAppConnectParams.getAppId();
        String appSecret=feishuAppConnectParams.getAppSecret();
        String larkDomain="larksuite.com";
        Result<QueryTenantInfoData> queryTenantInfoDataResult = feishuTenantService.initQueryTenantInfo(appid, appSecret, larkDomain);//自建应用不需要tenantId;
        if(!queryTenantInfoDataResult.isSuccess()){
            return Result.newError(ResultCodeEnum.FEISHU_APP_SECRET_ERROR);
        }

        log.info("FeishuWebController.queryConnectInfo,result={}",queryTenantInfoDataResult);
        return Result.newSuccess(queryTenantInfoDataResult.getData().getTenant());
    }


    /**
     * 纷享和飞书绑定
     *
     */
    @RequestMapping(value = "/fsUnBindWithFeishu2", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> fsUnBindWithFeishu2(@RequestBody FsUnBindWithFeishuArg arg) {
        if(arg==null || StringUtils.isEmpty(arg.getDisplayId())) {
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR);
        }
        return enterpriseBindService.fsUnBindWithFeishu2(arg.getDisplayId());
    }

    /**
     * 查询飞书连接器连接信息
     *
     */
    @RequestMapping(value = "/queryConnectInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<FeiShuConnectParam> queryConnectInfo(@RequestBody QueryConnectInfoArg arg) {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        log.info("FeishuWebController.queryConnectInfo,fsEa={}",fsEa);
        if(arg==null) {
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR);
        }

        arg.setFsEa(fsEa);
        Result<FeiShuConnectParam> result = enterpriseBindService.queryConnectInfo(arg);
        log.info("FeishuWebController.queryConnectInfo,result={}",result);
        return result;
    }

    /**
     * 查询飞书连接器连接信息
     *
     */
    @RequestMapping(value = "/queryScanCodeAuthUrl", method = RequestMethod.POST)
    @ResponseBody
    public Result<String> queryScanCodeAuthUrl(@RequestBody QueryConnectInfoArg arg) {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        log.info("FeishuWebController.queryScanCodeAuthUrl,fsEa={}",fsEa);
        if(arg==null || StringUtils.isEmpty(arg.getDataCenterId())) {
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR);
        }

        arg.setFsEa(fsEa);
        return enterpriseBindService.queryScanCodeAuthUrl(fsEa, arg.getDataCenterId());
    }

    /**
     * 查询已扫码授权的飞书用户信息，10分钟内有效
     *
     */
    @RequestMapping(value = "/getAuthUserInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<AuthUserInfoModel> getAuthUserInfo(@RequestBody QueryConnectInfoArg arg) {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        log.info("FeishuWebController.getAuthUserInfo,fsEa={}",fsEa);
        if(arg==null || StringUtils.isEmpty(arg.getDataCenterId())) {
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR);
        }

        arg.setFsEa(fsEa);

        String state = fsEa + "_" + arg.getDataCenterId();
        String authUserInfoJson = redisDataSource.getRedisClient().get(state);
        log.info("FeishuWebController.getAuthUserInfo,authUserInfoJson={}",authUserInfoJson);

        AuthUserInfoModel authUserInfoModel = JSONObject.parseObject(authUserInfoJson, AuthUserInfoModel.class);
        log.info("FeishuWebController.getAuthUserInfo,authUserInfoModel={}",authUserInfoModel);

        return new Result<>(authUserInfoModel);
    }

    /**
     * 查询绑定员工信息
     *
     */
    @RequestMapping(value = "/employee/searchBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<PageModel<List<EmployeeBindModel>>> searchBind(@RequestBody SearchBindArg arg,
                                                                 @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        FeiShuConnectParam connectParam = FeiShuConnectParam.parse(dcInfo);
        log.info("FeishuWebController.searchBind,connectParam={}",JSONObject.toJSONString(connectParam));

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String outEa = FeiShuConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        return employeeBindService.searchBind(fsEa, outEa, arg.getContent(), arg.getPageSize());
    }

    /**
     * 查询未绑定员工信息
     *
     */
    @RequestMapping(value = "/employee/searchUnBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<PageModel<List<EmployeeBindModel.FsEmployee>>> searchUnBind(@RequestBody SearchUnBindArg arg,
                                                                              @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        FeiShuConnectParam connectParam = FeiShuConnectParam.parse(dcInfo);
        log.info("FeishuWebController.searchUnBind,connectParam={}",JSONObject.toJSONString(connectParam));

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String outEa = FeiShuConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        return employeeBindService.searchUnBind(fsEa, outEa, arg.getContent(), arg.getPageSize());
    }

    /**
     * 查询纷享和飞书员工绑定信息
     *
     */
    @RequestMapping(value = "/employee/queryBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<PageModel<List<EmployeeBindModel>>> queryBind(@RequestBody QueryBindArg arg,
                                                                @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        FeiShuConnectParam connectParam = FeiShuConnectParam.parse(dcInfo);
        log.info("FeishuWebController.queryBind,connectParam={}",JSONObject.toJSONString(connectParam));

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String outEa = FeiShuConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        return employeeBindService.queryBind(fsEa, outEa, arg.getPageSize());
    }

    /**
     * 查询未绑定的纷享员工列表
     *
     */
    @RequestMapping(value = "/employee/queryFsUnbind", method = RequestMethod.POST)
    @ResponseBody
    public Result<PageModel<List<EmployeeBindModel.FsEmployee>>> queryFsUnbind(@RequestBody QueryFsUnbindArg arg,
                                                                               @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        FeiShuConnectParam connectParam = FeiShuConnectParam.parse(dcInfo);
        log.info("FeishuWebController.queryFsUnbind,connectParam={}",JSONObject.toJSONString(connectParam));

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String outEa = FeiShuConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        return employeeBindService.queryFsUnbind(fsEa, outEa, arg.getPageSize());
    }

    /**
     * 查询CRM应用可见范围未绑定的飞书员工列表
     *
     */
    @RequestMapping(value = "/employee/queryOutUnbind", method = RequestMethod.POST)
    @ResponseBody
    public Result<List<EmployeeBindModel.OutEmployee>> queryOutUnbind(@RequestBody QueryOutUnbindArg arg,
                                                                      @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        FeiShuConnectParam connectParam = FeiShuConnectParam.parse(dcInfo);
        log.info("FeishuWebController.queryOutUnbind,connectParam={}",JSONObject.toJSONString(connectParam));

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String outEa = FeiShuConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        return employeeBindService.queryOutUnbind(fsEa, outEa, arg.getSearchText(), arg.getPageSize());
    }

    /**
     * 查询CRM和飞书未绑定的员工信息
     *
     */
    @RequestMapping(value = "/employee/queryOutUnbind2", method = RequestMethod.POST)
    @ResponseBody
    public String queryOutUnbind2(@RequestBody QueryOutUnbindArg arg,
                                  @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        FeiShuConnectParam connectParam = FeiShuConnectParam.parse(dcInfo);
        log.info("FeishuWebController.queryOutUnbind2,connectParam={}",JSONObject.toJSONString(connectParam));

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String outEa = FeiShuConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();
        Integer employeeId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
        Result<List<EmployeeBindModel.OutEmployee>> result = employeeBindService.queryOutUnbind(fsEa, outEa, arg.getSearchText(), arg.getPageSize());

        com.facishare.open.feishu.syncapi.model.CepResult.Value value = new CepResult.Value();
        value.setData(result.getData());
        value.setErrCode(result.getCode()+"");
        value.setErrMsg(result.getMsg());
        value.setTraceMsg(result.getTraceMsg());

        com.facishare.open.feishu.syncapi.model.CepResult.Result result2 = new CepResult.Result();
        result2.setFailureCode(result.getCode());
        result2.setStatusCode(result.getCode());

        CepResult.UserInfo userInfo = new CepResult.UserInfo();
        userInfo.setEmployeeID(employeeId);
        userInfo.setEnterpriseAccount(fsEa);
        result2.setUserInfo(userInfo);

        CepResult cepResult = new CepResult();
        cepResult.setValue(value);
        cepResult.setResult(result2);

        return JSONObject.toJSONString(cepResult);
    }

    /**
     * 查询CRM和飞书未绑定的员工信息
     *
     */
    @RequestMapping(value = "/employee/queryUnbind", method = RequestMethod.POST)
    @ResponseBody
    public Result<EmployeeUnBindModel> queryUnbind(QueryUnbindArg arg,
                                                   @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        FeiShuConnectParam connectParam = FeiShuConnectParam.parse(dcInfo);
        log.info("FeishuWebController.queryUnbind,connectParam={}",JSONObject.toJSONString(connectParam));

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String outEa = FeiShuConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        return employeeBindService.queryUnbind(fsEa, outEa, arg.getPageSize());
    }

    /**
     * 查询飞书CRM应用可见范围内的所有员工信息
     *
     */
    @RequestMapping(value = "/employee/queryOutEmployeeList", method = RequestMethod.POST)
    @ResponseBody
    public Result<List<EmployeeBindModel.OutEmployee>> queryOutUserList(@RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        FeiShuConnectParam connectParam = FeiShuConnectParam.parse(dcInfo);
        log.info("FeishuWebController.queryOutUserList,connectParam={}",JSONObject.toJSONString(connectParam));

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String outEa = FeiShuConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        return employeeBindService.queryOutEmployeeList(fsEa, outEa);
    }

    /**
     * 解除员工绑定关系
     *
     */
    @RequestMapping(value = "/employee/unBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<Boolean> unBind(@RequestBody EmployeeBindModel2 arg,
                                  @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        FeiShuConnectParam connectParam = FeiShuConnectParam.parse(dcInfo);
        log.info("FeishuWebController.unBind,connectParam={}",JSONObject.toJSONString(connectParam));

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String outEa = FeiShuConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        return employeeBindService.unBind(fsEa,arg.getFsUserId(),arg.getOutUserId());
    }

    /**
     * 重新绑定
     *
     */
    @RequestMapping(value = "/employee/reBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<Boolean> reBind(@RequestBody EmployeeBindModel2 arg,
                                  @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        FeiShuConnectParam connectParam = FeiShuConnectParam.parse(dcInfo);
        log.info("FeishuWebController.reBind,connectParam={}",JSONObject.toJSONString(connectParam));

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String outEa = FeiShuConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        return employeeBindService.reBind(fsEa,arg.getFsUserId(),arg.getOutUserId(),outEa);
    }

    /**
     * 批量绑定
     *
     */
    @RequestMapping(value = "/employee/batchBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<List<EmployeeBindModel2>> batchBind(@RequestBody List<EmployeeBindModel2> employeeList,
                                                      @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        FeiShuConnectParam connectParam = FeiShuConnectParam.parse(dcInfo);
        log.info("FeishuWebController.batchBind,connectParam={}",JSONObject.toJSONString(connectParam));

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String outEa = FeiShuConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        return employeeBindService.batchBind(fsEa,employeeList,outEa);
    }

    /**
     * 批量解除员工绑定
     *
     */
    @RequestMapping(value = "/employee/batchUnBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<List<EmployeeBindModel2>> batchUnBind(@RequestBody List<EmployeeBindModel2> employeeList) {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        return employeeBindService.batchUnBind(fsEa,employeeList);
    }

    /**
     * 自定义url
     *
     */
    @RequestMapping(value = "/custom/getUrlInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<List<CustomUrlInfo>> getCustomUrls() {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String tenantId = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseId() + "";
        return customService.getCustomUrls(ChannelEnum.feishu, tenantId);
    }

    /**
     * 自动绑定设置保存
     *
     */
    @RequestMapping(value = "/enterprise/saveAutoBind", method = RequestMethod.GET)
    @ResponseBody
    public Result<Integer> saveAutoBind(@RequestParam Integer autoBind,@RequestParam(value = "channelEnum", required = false) ChannelEnum channelEnum,
                                        @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        FeiShuConnectParam connectParam = FeiShuConnectParam.parse(dcInfo);
        log.info("FeishuWebController.saveAutoBind,connectParam={}",JSONObject.toJSONString(connectParam));

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String outEa = FeiShuConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();
        channelEnum=ObjectUtils.isEmpty(channelEnum)?ChannelEnum.feishu:channelEnum;
        return enterpriseBindService.saveAutoBind(fsEa, outEa, autoBind,channelEnum);
    }

    /**
     * 查询自动绑定
     *
     */
    @RequestMapping(value = "/enterprise/queryAutoBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<Integer> queryAutoBind(@RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        FeiShuConnectParam connectParam = FeiShuConnectParam.parse(dcInfo);
        log.info("FeishuWebController.queryAutoBind,connectParam={}",JSONObject.toJSONString(connectParam));

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String outEa = FeiShuConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();
        return enterpriseBindService.queryAutoBind(fsEa, outEa);
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/getTemplate")
    public Result<BuildExcelFile.Result> getTemplate(@RequestBody GetTemplateArg arg) {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        Integer fsUserId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
        if (ObjectUtils.isEmpty(arg) || arg.getTemplateType()==null) {
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR);
        }
        return excelFileService.buildExcelTemplate(fsEa,arg.getTemplateType());
    }

    /**
     * 导入员工绑定关系
     */
    @PostMapping("/employee/importDepAndEmpMapping")
    public Result<ImportExcelFile.Result> importDepAndEmpMapping(@RequestBody ImportExcelFile.MappingDataArg arg,
                                                                                 @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        FeiShuConnectParam connectParam = FeiShuConnectParam.parse(dcInfo);
        log.info("importDepAndEmpMapping,dcInfo={},connectParam={}", dcInfo, connectParam);
        if (ObjectUtils.isEmpty(arg) || arg.getTemplateType()==null || FeiShuConnectParam.isInvalid(connectParam)) {
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR);
        }
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String tenantId = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseId() + "";
        Integer fsUserId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();

        arg.setFsEa(fsEa);
        arg.setOutEa(connectParam.getOutEa());
        arg.setFsUserId(fsUserId);

        String traceId = TraceUtils.getTraceId();
        String lang = TraceUtils.getLocale();
        ParallelUtils.createBackgroundTask().submit(() -> {
            TraceUtils.initTraceId(traceId);
            TraceUtils.setLocale(lang);
            try {
                Result<ImportExcelFile.Result> result2 = excelFileService.importExcelFile(arg);
                log.info("importDepAndEmpMapping,importExcelFile,result2={}", result2);
            } catch (IOException e) {
                log.error("importDepAndEmpMapping get error", e);
            }
        }).run();

        ImportExcelFile.Result result = new ImportExcelFile.Result();
        result.setPrintMsg(i18NStringManager.get(I18NStringEnum.s98, lang, tenantId));
        return new Result<>(result);
    }
}
