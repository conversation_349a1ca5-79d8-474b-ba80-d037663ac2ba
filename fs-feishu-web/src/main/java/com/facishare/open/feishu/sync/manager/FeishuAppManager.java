package com.facishare.open.feishu.sync.manager;

import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.result.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class FeishuAppManager {
    @Autowired
    private FeishuStoreAppManager feishuStoreAppManager;

    @Autowired
    private FeishuSelfBuildAppManager feishuSelfBuildAppManager;

    public Result<String> getTenantAccessToken(String appId, String tenantKey) {
        if(ConfigCenter.FEISHU_ISV_REQUEST_MAP.containsKey(appId)){
            //ISV应用
            return feishuStoreAppManager.getTenantAccessToken(appId, tenantKey);
        }else{
            return feishuSelfBuildAppManager.getTenantAccessToken(appId);
        }
    }

    public Result<String> initTenantAccessToken(String appId, String tenantKey,String appSecret,String domain) {
        if(ConfigCenter.FEISHU_ISV_REQUEST_MAP.containsKey(appId)){
            //ISV应用
            return feishuStoreAppManager.getTenantAccessToken(appId, tenantKey);
        }else{
            return feishuSelfBuildAppManager.initTenantToken(appId,appSecret,domain);
        }
    }

    public Result<String> getAppAccessToken(String appId) {
        if(ConfigCenter.FEISHU_ISV_REQUEST_MAP.containsKey(appId)){
            //ISV应用
            return feishuStoreAppManager.getAppAccessToken(appId);
        }else{
            return feishuSelfBuildAppManager.getAppAccessToken(appId);
        }
    }

    public void clearAppAccessToken(String appId) {
        feishuStoreAppManager.clearAppAccessToken(appId);
    }
}
