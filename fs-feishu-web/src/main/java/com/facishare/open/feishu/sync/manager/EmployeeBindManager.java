package com.facishare.open.feishu.sync.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.facishare.open.feishu.sync.mapper.EmployeeBindMapper;
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity;
import com.facishare.open.feishu.syncapi.enums.BindStatusEnum;
import com.facishare.open.feishu.syncapi.enums.BindTypeEnum;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * tb_employee_bind表管理器
 * <AUTHOR>
 * @date 20220722
 */
@Component
public class EmployeeBindManager {
    @Resource
    private EmployeeBindMapper employeeBindMapper;

    /**
     * 查询用户绑定信息
     * @param outEa
     * @param outUserId
     * @param fsEa 可为空
     * @return
     */
    public EmployeeBindEntity getEntity(String outEa,String outUserId,String fsEa) {
        LambdaQueryWrapper<EmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EmployeeBindEntity::getOutEa,outEa);
        wrapper.eq(EmployeeBindEntity::getOutUserId,outUserId);
        if(StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(EmployeeBindEntity::getFsEa,fsEa);
        }

        EmployeeBindEntity entity = employeeBindMapper.selectOne(wrapper);
        return entity;
    }

    public EmployeeBindEntity getEntity(ChannelEnum channel, String fsEa, String fsUserId, String outEa, String outUserId) {
        LambdaQueryWrapper<EmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EmployeeBindEntity::getChannel, channel.name());
        if(StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(EmployeeBindEntity::getFsEa, fsEa);
        }
        if(StringUtils.isNotEmpty(fsUserId)) {
            wrapper.eq(EmployeeBindEntity::getFsUserId, fsUserId);
        }
        if(StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(EmployeeBindEntity::getOutEa, outEa);
        }
        if(StringUtils.isNotEmpty(outUserId)) {
            wrapper.eq(EmployeeBindEntity::getOutUserId, outUserId);
        }

        EmployeeBindEntity entity = employeeBindMapper.selectOne(wrapper);
        return entity;
    }

    /**
     * 批量查询员工信息
     * @param outEa
     * @param outUserIdList
     * @param fsEa 可为空
     * @return
     */
    public List<EmployeeBindEntity> getEntityList(String outEa,List<String> outUserIdList,String fsEa) {
        LambdaQueryWrapper<EmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EmployeeBindEntity::getOutEa,outEa);
        wrapper.in(EmployeeBindEntity::getOutUserId,outUserIdList);
        if(StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(EmployeeBindEntity::getFsEa,fsEa);
        }

        return employeeBindMapper.selectList(wrapper);
    }

    /**
     * 查询用户绑定信息
     * @param fsEa
     * @param fsUserId
     * @return
     */
    public EmployeeBindEntity getEntityByFsUserId(String fsEa, String fsUserId, String outEa) {
        LambdaQueryWrapper<EmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EmployeeBindEntity::getFsEa,fsEa);
        wrapper.eq(EmployeeBindEntity::getFsUserId,fsUserId);
        if(StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(EmployeeBindEntity::getOutEa,outEa);
        }

        EmployeeBindEntity entity = employeeBindMapper.selectOne(wrapper);
        return entity;
    }

    /**
     * 查询用户绑定信息
     * @param fsEa
     * @param fsUserId
     * @return
     */
    public EmployeeBindEntity getBindEntity(String fsEa,String fsUserId,String outUserId) {
        LambdaQueryWrapper<EmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EmployeeBindEntity::getFsEa,fsEa);
        wrapper.eq(EmployeeBindEntity::getFsUserId,fsUserId);
        wrapper.eq(EmployeeBindEntity::getOutUserId,outUserId);

        EmployeeBindEntity entity = employeeBindMapper.selectOne(wrapper);
        return entity;
    }

    /**
     * 批量查询员工信息
     * @param fsEa
     * @param fsUserIdList
     * @return
     */
    public List<EmployeeBindEntity> getEntityList(String fsEa, List<String> fsUserIdList) {
        LambdaQueryWrapper<EmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EmployeeBindEntity::getFsEa,fsEa);
        wrapper.in(EmployeeBindEntity::getFsUserId,fsUserIdList);

        return employeeBindMapper.selectList(wrapper);
    }

    /**
     * 批量查询员工信息
     * @param fsEa
     * @param fsUserIdList
     * @return
     */
    public List<EmployeeBindEntity> getEntityList(String fsEa, String outEa, List<String> fsUserIdList) {
        LambdaQueryWrapper<EmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EmployeeBindEntity::getFsEa,fsEa);
        wrapper.eq(EmployeeBindEntity::getOutEa,outEa);
        wrapper.in(EmployeeBindEntity::getFsUserId,fsUserIdList);

        return employeeBindMapper.selectList(wrapper);
    }

//    /**
//     * 批量查询员工信息
//     * @param fsEa
//     * @return
//     */
//    public List<EmployeeBindEntity> getEntityList(String fsEa) {
//        LambdaQueryWrapper<EmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(EmployeeBindEntity::getFsEa,fsEa);
//
//        return employeeBindMapper.selectList(wrapper);
//    }

    /**
     * 批量查询员工信息
     * @param fsEa
     * @return
     */
    public List<EmployeeBindEntity> getEntityList(String fsEa, String outEa) {
        LambdaQueryWrapper<EmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EmployeeBindEntity::getFsEa,fsEa);
        if(StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(EmployeeBindEntity::getOutEa,outEa);
        }

        return employeeBindMapper.selectList(wrapper);
    }

    public int insert(EmployeeBindEntity entity) {
        int count = employeeBindMapper.insert(entity);
        LogUtils.info("EmployeeBindManager.insert,count={}",count);
        return count;
    }

    public int insert(ChannelEnum channel,
                      String fsEa,
                      String fsUserId,
                      String outEa,
                      String outUserId,
                      BindTypeEnum bindType,
                      BindStatusEnum bindStatus) {
        EmployeeBindEntity entity = EmployeeBindEntity.builder()
                .channel(channel)
                .fsEa(fsEa)
                .fsUserId(fsUserId)
                .outEa(outEa)
                .outUserId(outUserId)
                .bindType(bindType)
                .bindStatus(bindStatus)
                .build();
        return insert(entity);
    }

    public int updateById(EmployeeBindEntity entity) {
        int count = employeeBindMapper.updateById(entity);
        LogUtils.info("EmployeeBindManager.updateById,count={}",count);
        return count;
    }

    public int batchUpdateBindStatus(String fsEa,
                                     List<String> fsUserIdList,
                                     BindStatusEnum bindStatus,
                                     String outEa) {
        if(CollectionUtils.isEmpty(fsUserIdList)) return -1;

        EmployeeBindEntity entity = EmployeeBindEntity.builder()
                .bindStatus(bindStatus)
                .build();

        LambdaUpdateWrapper<EmployeeBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(EmployeeBindEntity::getFsEa,fsEa);
        wrapper.in(EmployeeBindEntity::getFsUserId,fsUserIdList);
        if(StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(EmployeeBindEntity::getOutEa,outEa);
        }
        int count = employeeBindMapper.update(entity, wrapper);
        LogUtils.info("EmployeeBindManager.batchUpdateBindStatus,count={}",count);
        return count;
    }

    public int batchUpdateBindStatusByFeiShu(String outEa,
                                     List<String> outUserIdList,
                                     BindStatusEnum bindStatus) {
        if(CollectionUtils.isEmpty(outUserIdList)) return -1;

        EmployeeBindEntity entity = EmployeeBindEntity.builder()
                .bindStatus(bindStatus)
                .build();

        LambdaUpdateWrapper<EmployeeBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(EmployeeBindEntity::getOutEa,outEa);
        wrapper.in(EmployeeBindEntity::getOutUserId,outUserIdList);
        int count = employeeBindMapper.update(entity, wrapper);
        LogUtils.info("EmployeeBindManager.batchUpdateBindStatusByFeiShu,count={}",count);
        return count;
    }

    public int updateBindStatusByFeiShu(String outEa,
                                        String outUserId,
                                        BindStatusEnum bindStatus) {
        return batchUpdateBindStatusByFeiShu(outEa, Lists.newArrayList(outUserId), bindStatus);
    }

//    public int updateBindStatus(String fsEa,
//                                BindStatusEnum bindStatus) {
//        EmployeeBindEntity entity = EmployeeBindEntity.builder()
//                .bindStatus(bindStatus)
//                .build();
//
//        LambdaUpdateWrapper<EmployeeBindEntity> wrapper = new LambdaUpdateWrapper<>();
//        wrapper.eq(EmployeeBindEntity::getFsEa, fsEa);
//        int count = employeeBindMapper.update(entity, wrapper);
//        LogUtils.info("EmployeeBindManager.updateBindStatus,count={}", count);
//        return count;
//    }

    /**
     * 删除指定ID的数据
     * @param id
     * @return
     */
    public int deleteById(Integer id) {
        return employeeBindMapper.deleteById(id);
    }

    /**
     * 删除已绑定的员工数据
     *
     * @param fsEa
     * @return
     */
    public int deleteByFsEa(String fsEa,String outEa) {
        LambdaUpdateWrapper<EmployeeBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(EmployeeBindEntity::getFsEa, fsEa);
        wrapper.eq(EmployeeBindEntity::getOutEa, outEa);
        int count = employeeBindMapper.delete(wrapper);
        LogUtils.info("EmployeeBindManager.deleteByFsEa,count={}", count);
        return count;
    }

    public EmployeeBindEntity getEntity(EmployeeBindEntity entity) {
        LambdaQueryWrapper<EmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EmployeeBindEntity::getChannel, entity.getChannel());
        wrapper.eq(EmployeeBindEntity::getFsEa, entity.getFsEa());
        wrapper.eq(EmployeeBindEntity::getOutEa, entity.getOutEa());
        if(StringUtils.isNotEmpty(entity.getFsUserId())) {
            wrapper.eq(EmployeeBindEntity::getFsUserId, entity.getFsUserId());
        }
        if(StringUtils.isNotEmpty(entity.getOutUserId())) {
            wrapper.eq(EmployeeBindEntity::getOutUserId, entity.getOutUserId());
        }

        return employeeBindMapper.selectOne(wrapper);
    }

    /**
     * 这个接口是获取外部人员的所有绑定记录
     * @param outEa
     * @param outUserId
     * @return
     */
    public List<EmployeeBindEntity> getEntityListByOutData(ChannelEnum channel, String outEa, String outUserId) {
        LambdaQueryWrapper<EmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EmployeeBindEntity::getChannel, channel);
        wrapper.eq(EmployeeBindEntity::getOutEa, outEa);
        wrapper.eq(EmployeeBindEntity::getOutUserId, outUserId);

        return employeeBindMapper.selectList(wrapper);
    }
}
