package com.facishare.open.feishu.web.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity;
import com.facishare.open.feishu.syncapi.enums.BindStatusEnum;
import com.facishare.open.feishu.syncapi.enums.BindTypeEnum;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.service.EmployeeBindService;
import com.facishare.open.feishu.syncapi.service.FeishuUserService;
import com.facishare.open.feishu.web.excel.BaseListener;
import com.facishare.open.feishu.web.excel.ImportExcelFile;
import com.facishare.open.feishu.web.excel.vo.EmployeeMappingVo;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Getter
public class EmployeeDataMappingListener extends BaseListener<EmployeeMappingVo> {

    private final ImportExcelFile.Result importResult;
    private String fsEa;
    private String outEa;
    private EmployeeBindService employeeBindService;
    private FeishuUserService feishuUserService;
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    private EIEAConverter eieaConverter;
    private String lang;
    private String tenantId;
    private I18NStringManager i18NStringManager;

    public EmployeeDataMappingListener(String fsEa,
                                       String outEa,
                                       EmployeeBindService employeeBindService,
                                       FeishuUserService feishuUserService,
                                       FsEmployeeServiceProxy fsEmployeeServiceProxy,
                                       EIEAConverter eieaConverter,
                                       String lang,
                                       String tenantId,
                                       I18NStringManager i18NStringManager) {
        importResult = new ImportExcelFile.Result();
        this.fsEa = fsEa;
        this.outEa = outEa;
        this.employeeBindService = employeeBindService;
        this.feishuUserService = feishuUserService;
        this.fsEmployeeServiceProxy = fsEmployeeServiceProxy;
        this.eieaConverter = eieaConverter;
        this.lang = lang;
        this.tenantId = tenantId;
        this.i18NStringManager = i18NStringManager;
    }

    @Override
    public void invoke(EmployeeMappingVo data, AnalysisContext context) {
        super.invoke(data, context);
        long startTime = System.currentTimeMillis();
        log.info("EmployeeDataMappingListener.invoke,data={}", data);
        importResult.incrInvoke(1);
        Integer rowNo = context.readRowHolder().getRowIndex();

        if(data.getFsUserId() == null
                || StringUtils.isEmpty(data.getOutUserMobile())) {
            importResult.addImportError(rowNo, i18NStringManager.get(I18NStringEnum.s119, lang, tenantId));
            return;
        }

        Integer ei = eieaConverter.enterpriseAccountToId(fsEa);
        com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> detail = fsEmployeeServiceProxy.detail(ei + "",
                data.getFsUserId() + "");
        if(!detail.isSuccess() || detail.getData()==null) {
            importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s120, lang, tenantId)+data.getFsUserId());
            return;
        }

        String outUserId = null;
        Result<List<UserData.User>> userInfo = feishuUserService.batchGetUserIds(ConfigCenter.feishuCrmAppId,
                outEa,
                null,
                Lists.newArrayList(data.getOutUserMobile()));
        log.info("EmployeeDataMappingListener.invoke,userInfo={}",userInfo);
        if(userInfo==null || !userInfo.isSuccess()) {
            importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s121, lang, tenantId));
            return;
        }
        //userId = openUserId
        outUserId = userInfo.getData().get(0).getUserId();
        if(StringUtils.isEmpty(outUserId)) {
            importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s122, lang, tenantId));
            return;
        }

        Result<EmployeeBindEntity> empData = employeeBindService.queryEmpData(ChannelEnum.feishu,
                fsEa,
                data.getFsUserId()+"",
                outEa,
                null);
        log.info("EmployeeDataMappingListener.invoke,empData={}", empData);
        if(empData.getData()!=null) {
            importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s123, lang, tenantId)+data.getFsUserId());
            return;
        }
        empData = employeeBindService.queryEmpData(ChannelEnum.feishu,
                fsEa,
                null,
                outEa,
                outUserId);
        log.info("EmployeeDataMappingListener.invoke,empData2={}", empData);
        if(empData.getData()!=null) {
            importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s124, lang, tenantId)+data.getOutUserMobile());
            return;
        }

        empData = employeeBindService.queryEmpData(ChannelEnum.feishu,
                fsEa,
                data.getFsUserId()+"",
                outEa,
                outUserId);
        log.info("EmployeeDataMappingListener.invoke,empData3={}", empData);
        if(empData.getData()==null) {
            EmployeeBindEntity entity = new EmployeeBindEntity();
            entity.setChannel(ChannelEnum.feishu);
            entity.setFsEa(fsEa);
            entity.setFsUserId(data.getFsUserId()+"");
            entity.setOutEa(outEa);
            entity.setOutUserId(outUserId);
            entity.setBindType(BindTypeEnum.manual);
            entity.setBindStatus(BindStatusEnum.normal);
            log.info("EmployeeDataMappingListener.invoke,employeeBindEntity={}", entity);
            try {
                Result<Integer> result = employeeBindService.insertEmpData(entity);
                log.info("EmployeeDataMappingListener.invoke,result={}", result);
                if(result.isSuccess() && result.getData() > 0) {
                    importResult.incrInsert(1);
                } else {
                    importResult.addImportError(rowNo,result.getMsg());
                }
            } catch (Exception e) {
                log.info("EmployeeDataMappingListener.invoke,exception={}", e.getMessage(),e);
                importResult.addInvokeExceptionRow(rowNo,e.getMessage());
            }
        } else {
            importResult.incrUpdate(1);
        }
        long costTime = System.currentTimeMillis() - startTime;
        log.info("EmployeeDataMappingListener.invoke,cost time={}", costTime);
        try {
            Thread.sleep(500);
        } catch (Exception e) {

        }
    }
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        super.doAfterAllAnalysed(context);
        //组装打印信息
        StringBuffer printMsg = new StringBuffer();
        printMsg.append(i18NStringManager.get(I18NStringEnum.s125, lang, tenantId));
        printMsg.append(String.format(i18NStringManager.get(I18NStringEnum.s126, lang, tenantId), importResult.getInvokedNum(),
                        importResult.getInsertNum(),
                        importResult.getUpdateNum()))
                .append("\n");

        printMsg.append(String.format(i18NStringManager.get(I18NStringEnum.s127, lang, tenantId), importResult.getImportErrorRows().size()))
                .append("\n");

        Map<String, List<Integer>> importErrorMap = importResult.getImportErrorRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        importErrorMap.forEach((k, v) -> {
            printMsg.append(String.format(i18NStringManager.get(I18NStringEnum.s128, lang, tenantId), Joiner.on(",").join(v), k))
                    .append("\n");
        });
        printMsg.append(String.format(i18NStringManager.get(I18NStringEnum.s129, lang, tenantId), importResult.getInvokeExceptionRows().size()))
                .append("\n");

        Map<String, List<Integer>> invokeExceptionMap = importResult.getInvokeExceptionRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        invokeExceptionMap.forEach((k, v) -> {
            printMsg.append(String.format(i18NStringManager.get(I18NStringEnum.s128, lang, tenantId), Joiner.on(",").join(v), k))
                    .append("\n");
        });
        importResult.setPrintMsg(printMsg.toString());
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        importResult.addInvokeExceptionRow(context.readRowHolder().getRowIndex(), exception.toString());
    }
}
