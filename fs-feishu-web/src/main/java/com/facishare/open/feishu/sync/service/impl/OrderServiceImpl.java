package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.sync.manager.*;
import com.facishare.open.feishu.syncapi.arg.CreateCustomerAndUpdateMappingArg;
import com.facishare.open.feishu.syncapi.arg.CreateOrderArg;
import com.facishare.open.feishu.syncapi.arg.SendTextNoticeArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.config.GlobalValue;
import com.facishare.open.feishu.syncapi.entity.AppInfoEntity;
import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.entity.OrderInfoEntity;
import com.facishare.open.feishu.syncapi.enums.*;
import com.facishare.open.feishu.syncapi.model.EnterpriseExtendModel;
import com.facishare.open.feishu.syncapi.model.OAConnectorOpenDataModel;
import com.facishare.open.feishu.syncapi.model.config.VersionModel;
import com.facishare.open.feishu.syncapi.model.connect.FeishuAppConnectParams;
import com.facishare.open.feishu.syncapi.model.event.FeishuOrderPaidEvent;
import com.facishare.open.feishu.syncapi.model.info.EnterpriseTrialInfo;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.service.*;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCrmOrderArg;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCustomerArg;
import com.facishare.open.order.contacts.proxy.api.enums.CustomerSourceEnum;
import com.facishare.open.order.contacts.proxy.api.service.FsOrderServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.EnterpriseUtils;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.stream.Collectors;

@Service("orderService")
public class OrderServiceImpl implements OrderService {
    @Resource
    private FsOrderServiceProxy fsOrderServiceProxy;
    @Resource
    private OrderInfoManager orderInfoManager;
    @Resource
    private CorpInfoManager corpInfoManager;
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Resource
    private AppInfoManager appInfoManager;
    @Resource
    private EmployeeBindManager employeeBindManager;
    @Resource
    private ContactsService contactsService;
    @Resource
    private CorpService corpService;
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private FeishuMessageService feishuMessageService;
    @Resource
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;
    @Resource
    private NotificationService notificationService;

    private ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(10);
    private static Map<String,Integer> retryMap = new ConcurrentHashMap<>();

    @Override
    public Result<List<EnterpriseBindEntity>> getEnterpriseBindList(String outEa) {
        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(outEa);
        LogUtils.info("OrderServiceImpl.getEnterpriseBindList,enterpriseBindList={}", enterpriseBindList);
        return Result.newSuccess(enterpriseBindList);
    }

    @Override
    public Result<OrderInfoEntity> saveOrder(FeishuOrderPaidEvent event) {
        Long orderStartTime = event.getPayTime();
        Long orderEndTime = event.getPayTime();
        FeishuCrmEditionEnum crmEditionEnum = null;
        Integer orderDays = null;

        //先确认下订单类型是否是定向方案
        VersionModel versionModel = ConfigCenter.getFirstVersionProductId(event.getAppId(),event.getPricePlanId());
        if(ObjectUtils.isNotEmpty(versionModel)) {
            crmEditionEnum = FeishuCrmEditionEnum.valueOf(versionModel.getVersion());
            if(ObjectUtils.isEmpty(crmEditionEnum)) {
                LogUtils.info("OrderServiceImpl.saveOrder,price plan id not support={}",event.getPricePlanId());
                return Result.newError(-1,FAIL);
            }
        } else {
            crmEditionEnum = FeishuCrmEditionEnum.FEISHU_SPECIFIC;
        }

        //试用版seats的值为0，我们默认把它改成10000，对客户来说，人数不会超过这个数，也就是人数不受限制
        //定向版本直接取seats的值，可能为0
        Integer userCount = 0;

        //正常订单属于客户下单
        //定向方案属于服务商下单
        OrderFromEnum orderFrom = null;

        if(crmEditionEnum != FeishuCrmEditionEnum.FEISHU_SPECIFIC) {
            switch (event.getPricePlanType()) {
                case trial:
                    //试用期15天
                    orderDays = GlobalValue.ALLOW_TRIAL_DAYS;
                    orderEndTime += orderDays * GlobalValue.TOTAL_MS_IN_ONE_DAY;
                    break;
                case per_seat_per_year:
                    //一年按365天计算
                    orderDays = GlobalValue.DAYS_OF_ONE_YEAR;
                    if(event.getBuyType()==BuyTypeEnum.renew) {
                        OrderInfoEntity latestOrder = orderInfoManager.getLatestOrder(event.getTenantKey());
                        orderStartTime = latestOrder.getEndTime().getTime();
                        orderEndTime = orderStartTime + orderDays * GlobalValue.TOTAL_MS_IN_ONE_DAY;
                    } else if(event.getBuyType()==BuyTypeEnum.upgrade) {
                        //升级包括增购升级和版本升级，升级不改变原始订单的开始日期和结束日期
                        OrderInfoEntity srcOrder = orderInfoManager.getEntity(event.getSrcOrderId());
                        orderStartTime = srcOrder.getBeginTime().getTime();
                        orderEndTime = srcOrder.getEndTime().getTime();
                    } else {
                        orderEndTime += orderDays * GlobalValue.TOTAL_MS_IN_ONE_DAY;
                    }
                    break;
                case active_end_date:
                    //定向方案
                    //由于定向方案没有返回过期时间或者有效时间，所以订单开始时间和结束时间都为支付时间，订单日期为0
                    orderDays = 0;
                    break;
                default:
                    LogUtils.info("OrderServiceImpl.onOrderPaid,order type not support={}",event.getPricePlanType());
                    return Result.newError(-1,FAIL);
            }
            userCount = event.getSeats()==0 ? ConfigCenter.createCrmAccount: event.getSeats();
            orderFrom = OrderFromEnum.customer;
        } else {
            //定向方案统一不设置过期时间，原因是飞书不返回此信息
            orderDays = 0;
            userCount = event.getSeats();
            orderFrom = OrderFromEnum.isv;
        }

        //飞书订单表，用tenantKey当corpId
        String corpId = event.getTenantKey();

        OrderInfoEntity entity = OrderInfoEntity.builder()
                .appId(event.getAppId())
                .orderId(event.getOrderId())
                .srcOrderId(event.getSrcOrderId())
                .channel(ChannelEnum.feishu)
                .buyType(event.getBuyType())
                .orderStatus(OrderStatusEnum.normal)
                .orderDays(orderDays)
                .orderCorpId(corpId)
                .paidCorpId(corpId)
                .price(event.getOrderPayPrice())
                .payPrice(event.getOrderPayPrice())
                .pricePlanType(event.getPricePlanType())
                .editionId(event.getPricePlanId())
                .editionName(crmEditionEnum.name())
                .userCount(userCount)
                .orderTime(new Timestamp(event.getCreateTime()))
                .payTime(new Timestamp(event.getPayTime()))
                .beginTime(new Timestamp(orderStartTime))
                .endTime(new Timestamp(orderEndTime))
                .orderFrom(orderFrom)
                .build();

        LogUtils.info("OrderServiceImpl.saveOrder,entity={}", JSONObject.toJSONString(entity));
        int count = orderInfoManager.insertOrUpdateOrderInfo(entity);
        LogUtils.info("OrderServiceImpl.saveOrder,count={}",count);
        if(crmEditionEnum == FeishuCrmEditionEnum.FEISHU_SPECIFIC) {
            LogUtils.info("OrderServiceImpl.saveOrder,crm edition is FEISHU_SPECIFIC");
            return Result.newError(-1,SUCCESS);
        }
        String traceId = TraceUtils.getTraceId();
        if(StringUtils.isEmpty(traceId)) {
            traceId = UUID.randomUUID().toString();
            TraceUtils.initTraceId(traceId);
        }
        final String traceId2 = traceId;
        new Thread(() -> {
            TraceUtils.initTraceId(traceId2);
            enterpriseOpenMonitor(corpId, event.getAppId());
        }).start();
        return new Result<>(entity);
    }

    @Override
    public Result<Void> createOrder(CreateOrderArg arg) {
        LogUtils.info("OrderServiceImpl.createOrder,arg={}", arg);
        OrderInfoEntity entity = orderInfoManager.getEntity(arg.getOrderId());
        LogUtils.info("OrderServiceImpl.createOrder,entity={}", entity);
        CreateCrmOrderArg createCrmOrderArg = buildCreateCrmOrderArg(entity, arg.getFsEa());
        LogUtils.info("OrderServiceImpl.createOrder,createCrmOrderArg={}", createCrmOrderArg);
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.createCrmOrder(createCrmOrderArg);
        LogUtils.info("OrderServiceImpl.createOrder,result={}", result);
        return Result.newError(result.getCode(),result.getMsg());
    }

    @Override
    public Result<CorpInfoEntity> getCorpInfoEntity(String appId, String outEa) {
        CorpInfoEntity corpInfoEntity = corpInfoManager.getEntityByTenantKey(outEa);
        if(corpInfoEntity==null) {
            Result<Void> result = corpService.updateCorpInfo(appId, outEa);
            LogUtils.info("OrderServiceImpl.getCorpInfoEntity,updateCorpInfo,result={}", result);
            corpInfoEntity = corpInfoManager.getEntityByTenantKey(outEa);
        }
        return Result.newSuccess(corpInfoEntity);
    }

    @Override
    public Result<UserData.User> getAppInstallerInfo(String appId, String outEa) {
        return employeeBindService.getAppInstallerInfo(appId, outEa);
    }

    @Override
    public Result<String> genFsEa(String enterpriseName) {
        String fsEa = EnterpriseUtils.genEA(enterpriseName,"feishu");
        LogUtils.info("OrderServiceImpl.genFsEa,fsEa={}", fsEa);
        return new Result<>(fsEa);
    }

    @Override
    public Result<Void> createCustomerAndUpdateMapping(CreateCustomerAndUpdateMappingArg arg) {
        LogUtils.info("OrderServiceImpl.createCustomerAndUpdateMapping,arg={}",arg);
        String fsEa = arg.getFsEa();

        //新购订单，开通全新的纷享企业
        CreateCustomerArg customerArg = CreateCustomerArg.builder()
                .source(CustomerSourceEnum.FEISHU.getSource())
                .outEid(arg.getOutEid())
                .managerName(arg.getInstallerName())
                .managerMobile(null)//通过飞书商店应用获取不到手机号
                .enterpriseName(arg.getEnterpriseName())
                .enterpriseAccount(fsEa)
                .build();

        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.createCustomer(customerArg);
        LogUtils.info("OrderServiceImpl.createCustomerAndUpdateMapping,result={}",result);
        if(!result.isSuccess()) {
            return Result.newError(result.getCode(), result.getMsg());
        }
        FeishuAppConnectParams feishuAppConnectParams = FeishuAppConnectParams.builder().appId(arg.getAppId()).baseUrl(ConfigCenter.FEISHU_ISV_REQUEST_MAP.get(arg.getAppId()))
                .encryKey(ConfigCenter.feishuEncryptKey)
                .build();

        //保存企业绑定关系
        int insertEnt = enterpriseBindManager.insert(ChannelEnum.feishu,
                fsEa,
                arg.getOutEa(),
                ConfigCenter.crm_domain,
                BindTypeEnum.auto,
                BindStatusEnum.create,JSONObject.toJSONString(feishuAppConnectParams),arg.getAppId());

        //保存管理员绑定关系
        int insertEmp = employeeBindManager.insert(ChannelEnum.feishu,
                fsEa,
                GlobalValue.FS_ADMIN_USER_ID + "",
                arg.getOutEa(),
                arg.getInstallerUserId(),
                BindTypeEnum.auto,
                BindStatusEnum.create);

        LogUtils.info("OrderServiceImpl.createCustomerAndUpdateMapping,insertEnt={},insertEmp={}",insertEnt,insertEmp);

        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> isEnterpriseBind(String ea) {
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.feishu,ea, BindStatusEnum.create);
        LogUtils.info("FsEventServiceImpl.isEnterpriseBind,entity={}", entity);
        return Result.newSuccess(entity!=null);
    }

    @Override
    public Result<Void> updateEnterpriseAndAdminMapping(String ea, String adminUserId) {
        enterpriseBindManager.updateBindStatus(ea, null, BindStatusEnum.normal);
        employeeBindManager.batchUpdateBindStatus(ea,
                Lists.newArrayList(adminUserId),
                BindStatusEnum.normal,
                null);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> sendWelcomeMsg(String ea) {
        EnterpriseBindEntity enterpriseBindEntity = enterpriseBindManager.getEntity(ea);
        AppInfoEntity appInfoEntity = appInfoManager.getEntity(enterpriseBindEntity.getOutEa());
        //发送欢迎消息给应用安装人员
        feishuMessageService.sendWelcomeMsg(appInfoEntity.getAppId(),appInfoEntity.getTenantKey(),appInfoEntity.getInstallerOpenId());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> initEnterpriseContacts(String ea) {
        com.facishare.open.feishu.syncapi.result.Result<Void> result = contactsService.initContactsAsync(ea);
        return Result.newError(result.getCode(),result.getMsg());
    }

//    @Override
//    public Result<String> onOrderPaid(FeishuOrderPaidEvent event) {
//        String traceId = TraceUtils.getTraceId();
//        if(StringUtils.isEmpty(traceId)) {
//            traceId = UUID.randomUUID().toString();
//            TraceUtils.initTraceId(traceId);
//        }
//        //飞书订单表，用tenantKey当corpId
//        String corpId = event.getTenantKey();
//
//        Result<OrderInfoEntity> result = saveOrder(event);
//        LogUtils.info("OrderServiceImpl.onOrderPaid,result={}",result);
//        if(!result.isSuccess()) {
//            return Result.newError(result.getCode(),result.getMsg());
//        }
//
//        if(result.isSuccess()) {
//            OrderInfoEntity entity = result.getData();
//
//            //非首次下单，不需要获取人员信息
//            List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(corpId);
//            Result<UserData.User> singleUserInfo = null;
//            final String traceId2 = TraceUtils.getTraceId();
//            if(CollectionUtils.isEmpty(enterpriseBindList)) {
//                //创建一个线程，一分钟后检查企业是否创建成功
//                new Thread(() -> {
//                    TraceUtils.initTraceId(traceId2);
//                    enterpriseOpenMonitor(corpId, event.getAppId());
//                }).start();
//                singleUserInfo = employeeBindService.getAppInstallerInfo(event.getAppId(),event.getTenantKey());
//                LogUtils.info("OrderServiceImpl.onOrderPaid,singleUserInfo={}", singleUserInfo);
//                if (!singleUserInfo.isSuccess()) {
//                    int retryCount = 0;
//                    if(retryMap.containsKey(event.getTenantKey())) {
//                        retryCount = retryMap.get(event.getTenantKey());
//                    }
//                    //客户首次安装应用并且下试用单，有可能无法立即获取到飞书的管理员，这里做一下重试，正常情况下，重试一次即可成功
//                    if (retryCount < 10) {
//                        retryCount++;
//                        retryMap.put(event.getTenantKey(),retryCount);
//                        scheduledExecutorService.schedule(()->{
//                            TraceUtils.initTraceId(traceId2);
//                            LogUtils.info("OrderServiceImpl.onOrderPaid,begin retry onOrderPaid");
//                            Result<String> retryResult = onOrderPaid(event);
//                            LogUtils.info("OrderServiceImpl.onOrderPaid,end retry onOrderPaid,retryResult={}",retryResult);
//                        },2000, TimeUnit.MILLISECONDS);
//                    } else {
//                        LogUtils.info("OrderServiceImpl.onOrderPaid,retry 10 times, cannot get app installer info,cannot run openEnterprise func, event={}",event);
//                    }
//
//                    return Result.newSuccess(FAIL);
//                }
//            }
//            Result<Void> openEnterprise = openEnterprise(entity,
//                    ObjectUtils.isNotEmpty(singleUserInfo) ? singleUserInfo.getData().getOpenId() : null,
//                    ObjectUtils.isNotEmpty(singleUserInfo) ? singleUserInfo.getData().getName() : null);
//            LogUtils.info("OrderServiceImpl.onOrderPaid,openEnterprise={}",openEnterprise);
//        }
//        return Result.newSuccess(SUCCESS);
//    }

    private CreateCrmOrderArg buildCreateCrmOrderArg(OrderInfoEntity entity, String fsEa) {
        int userCount = entity.getUserCount();
        LogUtils.info("OrderServiceImpl.openEnterprise,userCount={}", userCount);
        if(entity.getBuyType()== BuyTypeEnum.upgrade) {
            //增购或版本升级，飞书返回的是总的用户数，我们应该减去升级前的用户数
            OrderInfoEntity srcOrder = orderInfoManager.getEntity(entity.getSrcOrderId());
            userCount = userCount - srcOrder.getUserCount();
        } else if(entity.getBuyType() == BuyTypeEnum.renew) {
            //续订人数不变，只增加年限
            userCount = 0;
        }
        LogUtils.info("OrderServiceImpl.openEnterprise,userCount2={}", userCount);

        VersionModel versionModel = ConfigCenter.getFirstVersionProductId(entity.getAppId(), entity.getEditionId());
        CreateCrmOrderArg.CrmOrderDetailInfo orderDetailInfo = CreateCrmOrderArg.CrmOrderDetailInfo.builder()
                .enterpriseAccount(fsEa)
                .orderId(entity.getOrderId())
                .orderTime(entity.getPayTime().getTime())
                .orderTpye(entity.getPricePlanType() == PricePlanTypeEnum.trial ? CreateCrmOrderArg.CrmOrderDetailInfo.ORDER_TYPE_TRY : CreateCrmOrderArg.CrmOrderDetailInfo.ORDER_TYPE_BUY)
                .build();

        //订单金额单位是 分，需要转换成 元
        BigDecimal orderAmount = BigDecimal.valueOf(entity.getPayPrice() / 100.0);
        CreateCrmOrderArg.CrmOrderProductInfo orderProductInfo = CreateCrmOrderArg.CrmOrderProductInfo.builder()
                .beginTime(entity.getBeginTime().getTime())
                .endTime(entity.getEndTime().getTime())
                .quantity(userCount)
                .allResourceCount(userCount)
                .orderAmount(orderAmount + "")
                .productId(versionModel.getProductId())
                .build();

        CreateCrmOrderArg orderArg = CreateCrmOrderArg.builder()
                .crmOrderDetailInfo(orderDetailInfo)
                .crmOrderProductInfo(orderProductInfo)
                .build();

        return orderArg;
    }

//    @Override
//    public Result<Void> openEnterprise(OrderInfoEntity entity,String installUserId,String installerName) {
//        CorpInfoEntity corpInfoEntity = getCorpInfoEntity(entity.getAppId(),entity.getPaidCorpId()).getData();
//        CreateCrmOrderArg orderArg = buildCreateCrmOrderArg(entity,corpInfoEntity);
//
//        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(corpInfoEntity.getTenantKey());
//        LogUtils.info("OrderServiceImpl.openEnterprise,enterpriseBindList={}", enterpriseBindList);
//        if(CollectionUtils.isEmpty(enterpriseBindList)) {
//            String fsEa = orderArg.getCrmOrderDetailInfo().getEnterpriseAccount();
//
//            //新购订单，开通全新的纷享企业
//            CreateCustomerArg customerArg = CreateCustomerArg.builder()
//                    .source(CustomerSourceEnum.FEISHU.getSource())
//                    .outEid(corpInfoEntity.getDisplayId())
//                    .managerName(installerName)
//                    .managerMobile(null)//通过飞书商店应用获取不到手机号
//                    .enterpriseName(corpInfoEntity.getTenantName())
//                    .enterpriseAccount(fsEa)
//                    .build();
//
//            com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.openEnterprise(customerArg, orderArg);
//            if(!result.isSuccess()) {
//                return Result.newError(result.getCode(), result.getMsg());
//            }
//
//            //保存企业绑定关系
//            enterpriseBindManager.insert(entity.getChannel(),
//                    fsEa,
//                    entity.getPaidCorpId(),
//                    ConfigCenter.crm_domain,
//                    BindTypeEnum.auto,
//                    BindStatusEnum.create);
//
//            //保存管理员绑定关系
//            employeeBindManager.insert(entity.getChannel(),
//                    fsEa,
//                    GlobalValue.FS_ADMIN_USER_ID+"",
//                    entity.getPaidCorpId(),
//                    installUserId,
//                    BindTypeEnum.auto,
//                    BindStatusEnum.create);
//        }
//        return Result.newSuccess();
//    }

    @Override
    public Result<Void> saveOrderAndAddCrmOrder(FeishuOrderPaidEvent event) {
        LogUtils.info("OrderServiceImpl.createAndSaveOrder,event={}", event);
        Result<OrderInfoEntity> saveOrder = saveOrder(event);
        LogUtils.info("OrderServiceImpl.createAndSaveOrder,saveOrder={}", saveOrder);
        if(saveOrder.isSuccess()) {
            List<EnterpriseBindEntity> enterpriseBindList = getEnterpriseBindList(event.getTenantKey()).getData();
            LogUtils.info("OrderServiceImpl.createAndSaveOrder,enterpriseBindList={}", enterpriseBindList);
            if(CollectionUtils.isNotEmpty(enterpriseBindList)) {
                if(enterpriseBindList.size()==1) {
                    OrderInfoEntity entity = saveOrder.getData();
                    //自动绑定的企业，重新下单逻辑
                    if(enterpriseBindList.get(0).getBindType()== BindTypeEnum.auto) {
                        String fsEa = enterpriseBindList.get(0).getFsEa();
                        LogUtils.info("OrderServiceImpl.createAndSaveOrder,fsEa={}", fsEa);
                        CreateCrmOrderArg orderArg = buildCreateCrmOrderArg(entity,fsEa);
                        LogUtils.info("OrderServiceImpl.createAndSaveOrder,orderArg={}", orderArg);
                        //升级订单或续订订单，更新纷享企业
                        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.createCrmOrder(orderArg);
                        LogUtils.info("OrderServiceImpl.createAndSaveOrder,createCrmOrder,result={}", result);
                        return Result.newInstance(result.getCode(),result.getMsg());
                    } else {
                        //手动绑定地企业，重新下单，前面保存完订单信息，到这里就结束了。
                        return Result.newSuccess();
                    }
                }
                //多对一场景，多个纷享企业对一个飞书企业
                if(enterpriseBindList.size()>1) {
                    //只有手动绑定场景，才支持多个纷享企业对一个飞书企业，重新下单，前面保存完订单信息，到这里就结束了。
                    return Result.newSuccess();
                }
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<EnterpriseTrialInfo> getEnterpriseTrialInfo(String fsEa, String outEa) {
        //查询企业outEa
        EnterpriseBindEntity enterpriseBindEntity = enterpriseBindManager.getEntity(fsEa,outEa);
        if(enterpriseBindEntity==null) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }

        EnterpriseTrialInfo enterpriseTrialInfo = new EnterpriseTrialInfo();
        enterpriseTrialInfo.setOutEa(enterpriseBindEntity.getOutEa());
        enterpriseTrialInfo.setBindType(enterpriseBindEntity.getBindType());
        //反绑定的企业不赋值
        if(enterpriseBindEntity.getBindType() == BindTypeEnum.manual) {
            return Result.newSuccess(enterpriseTrialInfo);
        }

        if(StringUtils.isNotEmpty(enterpriseBindEntity.getExtend())) {
            enterpriseTrialInfo.setExtend(enterpriseBindEntity.getExtend());
        } else {
            String enterpriseExtend = GlobalValue.enterprise_extend;
            enterpriseTrialInfo.setExtend(enterpriseExtend);
        }

        EnterpriseExtendModel extendModel = new Gson().fromJson(enterpriseTrialInfo.getExtend(), EnterpriseExtendModel.class);
        if(extendModel.getIsFirstLand() == Boolean.TRUE) {
            //更新至数据库
            extendModel.setIsFirstLand(Boolean.FALSE);
            enterpriseBindManager.updateExtend(fsEa, enterpriseBindEntity.getOutEa(), new Gson().toJson(extendModel));
        }

        //查询最新的订单
        OrderInfoEntity latestOrderEntity = orderInfoManager.getLatestOrder(enterpriseBindEntity.getOutEa());
        if(latestOrderEntity==null) {
            return Result.newError(ResultCodeEnum.ORDER_INFO_NOT_EXISTS);
        }

        //判断该订单是不是客户下单并且是不是试用订单
        if(latestOrderEntity.getOrderFrom() == OrderFromEnum.customer && latestOrderEntity.getPricePlanType() == PricePlanTypeEnum.trial) {
            enterpriseTrialInfo.setIsTrial(Boolean.TRUE);
            enterpriseTrialInfo.setBeginTime(latestOrderEntity.getBeginTime());
            enterpriseTrialInfo.setEndTime(latestOrderEntity.getEndTime());
        } else {
            enterpriseTrialInfo.setIsTrial(Boolean.FALSE);
        }
        return Result.newSuccess(enterpriseTrialInfo);
    }

    private void enterpriseOpenMonitor(String outEa, String appId) {
        LogUtils.info("OrderServiceImpl.enterpriseOpenMonitor,outEa={},appId={}", outEa, appId);
        //睡眠一分钟 呼呼大睡
        try {
            Thread.sleep(60 * 1000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        //查库
        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(outEa);
        if(CollectionUtils.isNotEmpty(enterpriseBindList)) {
            EnterpriseBindEntity enterpriseBindEntity = enterpriseBindList.get(0);
            if(enterpriseBindEntity.getBindStatus().equals(BindStatusEnum.create)) {
                //上报
                OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                        .ea(enterpriseBindEntity.getFsEa())
                        .appId(appId)
                        .channelId(ChannelEnum.feishu.name())
                        .dataTypeId(DataTypeEnum.ENTERPRISE_CREATE.getDataType())
                        .corpId(outEa)
                        .errorCode("100")
                        .errorMsg("超过一分钟，该企业还未创建成功，请及时关注！") //ignorei18n
                        .build();
                oaConnectorOpenDataManager.send(model);
                //告警
                SendTextNoticeArg arg = new SendTextNoticeArg();
                arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                List<String> receivers = new LinkedList<>(ConfigCenter.ENTERPRISE_OPEN_NOTIFICATION_MEMBERS);
                arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                arg.setMsgTitle("飞书企业开通失败告警"); //ignorei18n
                String msg = String.format("超过一分钟，该企业还未创建成功\n纷享企业ea=%s\n请及时关注！", enterpriseBindEntity.getFsEa()); //ignorei18n
                arg.setMsg(msg);
                notificationService.sendNotice(arg);
            }
        } else {
            //没有记录，自己接收告警
            //上报
            OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                    .appId(appId)
                    .channelId(ChannelEnum.feishu.name())
                    .dataTypeId(DataTypeEnum.ENTERPRISE_CREATE.getDataType())
                    .corpId(outEa)
                    .errorCode("101")
                    .errorMsg("超过一分钟，该企业没有绑定记录，请及时关注！") //ignorei18n
                    .build();
            oaConnectorOpenDataManager.send(model);
            //告警
            SendTextNoticeArg arg = new SendTextNoticeArg();
            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
            arg.setMsgTitle("飞书企业开通失败告警"); //ignorei18n
            String msg = String.format("超过一分钟，该企业没有绑定记录\n飞书企业ea=%s\n请及时关注！", outEa); //ignorei18n
            arg.setMsg(msg);
            notificationService.sendNotice(arg);
        }
    }

    @Override
    public com.facishare.open.order.contacts.proxy.api.result.Result<Void> buyConnector(String fsEa,
                                                                                        String orderAmount,
                                                                                        Long beginTime,
                                                                                        Long endTime) {
        long orderTime = System.currentTimeMillis();
        if(orderTime >= endTime) {
            return com.facishare.open.order.contacts.proxy.api.result.Result.newError(com.facishare.open.order.contacts.proxy.api.result.ResultCodeEnum.PARAM_ILLEGAL);
        }
        BigDecimal amount = BigDecimal.valueOf(Double.valueOf(orderAmount));
        CreateCrmOrderArg.CrmOrderProductInfo orderProductInfo = new CreateCrmOrderArg.CrmOrderProductInfo();
        orderProductInfo.setProductId(ConfigCenter.feishuConnectorProductId);
        orderProductInfo.setQuantity(ConfigCenter.createCrmAccount);
        orderProductInfo.setOrderAmount(orderAmount);
        orderProductInfo.setAllResourceCount(ConfigCenter.createCrmAccount);
        orderProductInfo.setBeginTime(beginTime);
        orderProductInfo.setEndTime(endTime);

        CreateCrmOrderArg.CrmOrderDetailInfo orderDetailInfo = new CreateCrmOrderArg.CrmOrderDetailInfo();
        orderDetailInfo.setEnterpriseAccount(fsEa);
        orderDetailInfo.setOrderTime(orderTime);
        orderDetailInfo.setOrderTpye(amount.compareTo(BigDecimal.ZERO)==0 ? 3 : 1);
        orderDetailInfo.setOrderId("erpdss_feishu_connector_"+System.currentTimeMillis());

        CreateCrmOrderArg orderArg = new CreateCrmOrderArg();
        orderArg.setCrmOrderProductInfo(orderProductInfo);
        orderArg.setCrmOrderDetailInfo(orderDetailInfo);

        LogUtils.info("OrderServiceImpl.buyConnector,orderArg={}", JSONObject.toJSONString(orderArg));
        return fsOrderServiceProxy.createCrmOrder(orderArg);
    }
}
