package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.feishu.sync.manager.*;
import com.facishare.open.feishu.syncapi.model.connect.FeishuAppConnectParams;
import com.facishare.open.feishu.syncapi.model.login.LoginAuthModel;
import com.facishare.open.feishu.syncapi.arg.SendTextNoticeArg;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.enums.BindTypeEnum;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.feishu.syncapi.enums.DataTypeEnum;
import com.facishare.open.feishu.syncapi.model.OAConnectorOpenDataModel;
import com.facishare.open.feishu.syncapi.service.NotificationService;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.config.GlobalValue;
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity;
import com.facishare.open.feishu.syncapi.model.login.FsUserModel;
import com.facishare.open.feishu.syncapi.model.login.UserTicketModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.data.Code2UserInfoData;
import com.facishare.open.feishu.syncapi.service.LoginService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.webhook.common.util.MD5Util;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 飞书免登录服务
 * 生成飞书免登录二维码URL：https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=https://www.ceshi112.com/feishu/external/loginAuth&app_id=cli_a20192f6afb8d00c&state=cli_a20192f6afb8d00c
 * 这个URL使用场景：PC端纷享CRM登录页->更多入口  飞书登录
 */
@Service("loginService")
public class LoginServiceImpl implements LoginService {
    @Resource
    private ProxyHttpClient proxyHttpClient;
    @Resource
    private FeishuStoreAppManager feishuStoreAppManager;
    @Resource
    private EmployeeBindManager employeeBindManager;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private UrlManager urlManager;
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Autowired
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private FeishuAppManager feishuAppManager;


    @Override
    public Result<Code2UserInfoData> code2UserInfo(LoginAuthModel loginAuthModel) {

        LogUtils.info("LoginServiceImpl.code2UserInfo,code={}",loginAuthModel);
        String endPointUrl = "https://open.feishu.cn/open-apis/authen/v1/access_token";
        //替换地址
        endPointUrl = urlManager.getFeishuUrl(null, loginAuthModel.getAppId(), endPointUrl);
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Bearer " + feishuAppManager.getAppAccessToken(loginAuthModel.getAppId()).getData());
        headerMap.put("Content-Type","application/json; charset=utf-8");
        JSONObject body = new JSONObject();
        body.put("grant_type","authorization_code");
        body.put("code",loginAuthModel.getCode());

        Result<Code2UserInfoData> result = proxyHttpClient.postUrl(endPointUrl, body, headerMap, new TypeReference<Result<Code2UserInfoData>>() {
        });
        if(result==null) {
            return Result.newError(ResultCodeEnum.CALL_OUT_INTERFACE_FAILED);
        }
        //app access token失效，清理token，并重新获取数据
        if(result.getCode()!=0) {
            feishuStoreAppManager.clearAppAccessToken(loginAuthModel.getAppId());
            result = proxyHttpClient.postUrl(endPointUrl, body, headerMap, new TypeReference<Result<Code2UserInfoData>>() {
            });
        }
        LogUtils.info("LoginServiceImpl.code2UserInfo,result={}",result);
        return result;
    }

    @Override
    public Result<String> genFsTicket(String corpId, String appId, String userId,String fsEa) {
        String ticketFormat = "corpId=%s&appId=%s&userId=%s&timestamp=%s&fsEa=%s";
        long timestamp = System.currentTimeMillis();
        String ticket = String.format(ticketFormat, corpId, appId, userId, timestamp,fsEa);
        UserTicketModel ticketModel = new UserTicketModel(corpId, appId, userId, timestamp,fsEa);
        String ticketMd5 = MD5Util.getMD5(ticket);
        String key = GlobalValue.USER_TICKET_KEY_PREFIX + ticketMd5;
        redisDataSource.getRedisClient().set(key, JSONObject.toJSONString(ticketModel));
        redisDataSource.getRedisClient().expire(key, GlobalValue.USER_TICKET_EXPIRE_TIME);  //10分钟有效

        return new Result<>(ticketMd5);
    }

    @Override
    public Result<FsUserModel> getFsUser(String ticket) {
        String key = GlobalValue.USER_TICKET_KEY_PREFIX + ticket;
        String value = redisDataSource.getRedisClient().get(key);
        LogUtils.info("FeishuServiceImpl.getFsUser,key={},value={}",key,value);
        if(StringUtils.isEmpty(value)) {
            return Result.newError(ResultCodeEnum.TICKET_NOT_EXISTS);
        }
        UserTicketModel ticketModel = JSONObject.parseObject(value,UserTicketModel.class);
        LogUtils.info("FeishuServiceImpl.getFsUser,ticketModel={}",ticketModel);
        long offset = System.currentTimeMillis() - ticketModel.getTimestamp();
        LogUtils.info("FeishuServiceImpl.getFsUser,offset={}",offset);
        if(offset > GlobalValue.USER_TICKET_EXPIRE_TIME * 1000L) {
            LogUtils.info("FeishuServiceImpl.getFsUser,ticket expired");
            return Result.newError(ResultCodeEnum.TICKET_EXPIRED);
        }
        EmployeeBindEntity entity = employeeBindManager.getEntity(ticketModel.getCorpId(), ticketModel.getUserId(), ticketModel.getFsEa());
        LogUtils.info("FeishuServiceImpl.getFsUser,entity={}",entity);
        if(ObjectUtils.isEmpty(entity)) {
            List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(ticketModel.getCorpId());
            EnterpriseBindEntity enterpriseBindEntity = enterpriseBindList.get(0);
            if(enterpriseBindEntity.getBindType().equals(BindTypeEnum.auto)) {
                //上报
                OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                        .ea(ticketModel.getFsEa())
                        .channelId(ChannelEnum.feishu.name())
                        .dataTypeId(DataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                        .corpId(ticketModel.getCorpId())
                        .outUserId(ticketModel.getUserId())
                        .errorCode("103")
                        .errorMsg("ticket换取纷享员工身份失败，请及时关注！") //ignorei18n
                        .build();
                oaConnectorOpenDataManager.send(model);
                //告警
                SendTextNoticeArg arg = new SendTextNoticeArg();
                arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
                arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                arg.setMsgTitle("人员从飞书登陆纷享失败告警"); //ignorei18n
                String msg = String.format("ticket换取纷享员工身份失败\nticket=%s，info=%s\n请及时关注！", ticket, value); //ignorei18n
                arg.setMsg(msg);
                notificationService.sendNotice(arg);
            }
            return Result.newError(ResultCodeEnum.EMPLOYEE_NO_BIND_INFO);
        }
        FsUserModel fsUserModel = new FsUserModel(entity.getFsEa(),entity.getFsUserId(),ticketModel.getAppId());
        LogUtils.info("FeishuServiceImpl.getFsUser,fsUserModel={}",fsUserModel);
        return Result.newSuccess(fsUserModel);
    }
}
