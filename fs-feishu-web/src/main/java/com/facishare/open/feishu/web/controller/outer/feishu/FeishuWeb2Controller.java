package com.facishare.open.feishu.web.controller.outer.feishu;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.enums.BindStatusEnum;
import com.facishare.open.feishu.syncapi.info.FeishuCreateEmployeeInfo;
import com.facishare.open.feishu.syncapi.model.info.EmployeesBindSyncInfo;
import com.facishare.open.feishu.syncapi.model.info.EnterpriseTrialInfo;
import com.facishare.open.feishu.syncapi.model.info.FsEmployeeDetailInfo;
import com.facishare.open.feishu.syncapi.model.jsapi.JsApiSignatureModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.service.EmployeeBindService;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.feishu.syncapi.service.OrderService;
import com.facishare.open.feishu.web.enums.UserContextSingleton;
import com.facishare.open.feishu.web.excel.BaseListener;
import com.facishare.open.feishu.web.excel.FileManager;
import com.facishare.open.feishu.web.excel.ReadExcel;
import com.facishare.open.feishu.web.template.inner.jsapi.FeishuJsApiTemplate;
import com.facishare.open.feishu.web.template.model.JsApiModel;
import com.facishare.open.feishu.web.utils.SecurityUtil;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 飞书WEB2服务接口，不需要管理员身份调用
 *
 * <AUTHOR>
 * @date 2023.06.06
 */
@Slf4j
@RestController
@RequestMapping(value = "/feishu/web2")
public class FeishuWeb2Controller {
    @Resource
    private EnterpriseBindService enterpriseBindService;
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private OrderService orderService;
    @Resource
    private FeishuJsApiTemplate feishuJsApiTemplate;
    @Autowired
    private FileManager fileManager;
    @Resource
    private I18NStringManager i18NStringManager;

    private static final String VER = "V1_";

    /**
     * 获取飞书签名
     * 1、前端调用
     * @param appId
     * @param url
     * @return
     */
    @RequestMapping(value="/getJsApiSignature",method = RequestMethod.GET)
    @ResponseBody
    public Result<JsApiSignatureModel> getJsApiSignature(@RequestParam String appId,
                                                         @RequestParam String url,
                                                         @RequestParam(required = false) String outEa) {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        LogUtils.info("FeishuWebController.getJsApiSignature,appId={},fsEa={},outEa={},url={}",appId,fsEa,outEa,url);

        JsApiModel jsApiModel = new JsApiModel();
        jsApiModel.setAppId(appId);
        jsApiModel.setFsEa(fsEa);
        jsApiModel.setOutEa(outEa);
        jsApiModel.setUrl(url);

        MethodContext context = MethodContext.newInstance(jsApiModel);
        feishuJsApiTemplate.getJsApiSignature(context);
        Result<JsApiSignatureModel> result = (Result<JsApiSignatureModel>) context.getResult().getData();

        LogUtils.info("FeishuWebController.getJsApiSignature,result={}",result);
        return result;
    }

    /**
     * 查询企业存在某种绑定状态
     * @param outEa 外部企业账号id
     * @return 企业绑定状态，1是normal状态，2是其他状态
     */
    @RequestMapping(value = "/enterprise/getEnterpriseStatus", method = RequestMethod.POST)
    @ResponseBody
    @CrossOrigin
    public Result<Integer> getEnterpriseStatus(@RequestParam String outEa) {
        outEa = decodeData(outEa);
        if(StringUtils.isEmpty(outEa)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        Result<List<EnterpriseBindEntity>> enterpriseBindListResult = enterpriseBindService.getEnterpriseBindList(outEa);
        if(!enterpriseBindListResult.isSuccess() || CollectionUtils.isEmpty(enterpriseBindListResult.getData())) {
            return Result.newSuccess(2);
        }
        //判断有没有normal状态的企业
        List<EnterpriseBindEntity> normalEnterpriseBindList = enterpriseBindListResult.getData().stream()
                .filter(v -> v.getBindStatus().equals(BindStatusEnum.normal))
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(normalEnterpriseBindList)) {
            //存在normal状态的企业
            return Result.newSuccess(1);
        }
        return Result.newSuccess(2);
    }

    /**
     * 留资功能使用的接口，得到企业的最新订单情况和绑定类型
     * @return
     */
    @RequestMapping(value = "/order/getEnterpriseTrialInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<EnterpriseTrialInfo> getEnterpriseTrialInfo(@RequestParam(required = false) String outEa) {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        return orderService.getEnterpriseTrialInfo(fsEa, outEa);
    }

    /**
     * 留资功能使用的接口，得到员工在纷享crm的一些信息
     * @return
     */
    @RequestMapping(value = "/employee/getFsCurEmployeeDetailInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<FsEmployeeDetailInfo> getFsCurEmployeeDetailInfo() {
        Integer ei = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseId();
        Integer userId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
        if(ei==null || userId==null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return employeeBindService.getFsCurEmployeeDetailInfo(ei, userId);
    }

    /**
     * 更新企业绑定表拓展字段
     * 1、留资功能：函数调用此接口更新拓展字段的是否已留资的字段
     * @return
     */
    @Deprecated
    @RequestMapping(value = "/enterprise/updateEnterpriseExtend", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> updateEnterpriseExtend(@RequestParam String fsEa,
                                               @RequestParam String extendField,
                                               @RequestParam Object extendValue,
                                               @RequestParam(required = false) String outEa) {
        if (StringUtils.isEmpty(fsEa) || StringUtils.isEmpty(extendField) || ObjectUtils.isEmpty(extendValue)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return enterpriseBindService.updateEnterpriseExtend(fsEa, outEa, extendField, extendValue);
    }

    @RequestMapping(value = "/uploadEmployeesBindSyncFile", method = RequestMethod.POST)
    @Deprecated
    public Result<Void> uploadEmployeesBindSyncFile(MultipartFile file) {
        byte[] bytes = new byte[0];
        try {
            bytes = file.getBytes();
            InputStream inputStream = new ByteArrayInputStream(bytes);
            ReadExcel.Arg<Map<Integer, String>> arg = new ReadExcel.Arg<>();
            BaseListener<Map<Integer, String>> listen = new BaseListener<Map<Integer, String>>() {
            };
            arg.setExcelListener(listen);
            arg.setInputStream(inputStream);
            fileManager.readExcelBySheetName(arg);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(listen.getDataList())) {
                return new Result<>();
            }
            List<EmployeesBindSyncInfo> employeesBindSyncInfos = listen.getDataList().stream().map(v -> {
                EmployeesBindSyncInfo employeesBindSyncInfo = new EmployeesBindSyncInfo();
                employeesBindSyncInfo.setFsEa(v.get(0));
                employeesBindSyncInfo.setDisplayId(v.get(1));
                employeesBindSyncInfo.setFsUserId(v.get(2));
                employeesBindSyncInfo.setMobile(v.get(4));
                return employeesBindSyncInfo;
            }).collect(Collectors.toList());
            log.info("FeishuWebController.uploadEmployeesBindSyncFile,employeesBindSyncInfos={}.", employeesBindSyncInfos);
            new Thread(() -> employeeBindService.uploadEmployeesBindSyncFile(employeesBindSyncInfos)).start();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new Result<>();
    }

    /**
     * 创建员工接口
     * @return
     */
    @RequestMapping(value = "/employee/createEmployeeInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> createEmployeeInfo(@RequestBody FeishuCreateEmployeeInfo createEmployeeInfo) {
        if(ObjectUtils.isEmpty(createEmployeeInfo) || org.apache.commons.lang.StringUtils.isEmpty(createEmployeeInfo.getToken()) || !createEmployeeInfo.getToken().equals(ConfigCenter.ERROR_PAGE_TOKEN)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return employeeBindService.createEmployeeInfo(createEmployeeInfo.getOutEa(), createEmployeeInfo.getAppId(), createEmployeeInfo.getOutUserId(), createEmployeeInfo.getFsEa());
    }

    /**
     * 跳转到飞书工具集页面
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/feishutools",method = RequestMethod.GET)
    public ModelAndView feishutools(HttpServletResponse response,
                                    HttpServletRequest request) throws IOException, ServletException {
        //进行身份校验
        if(ObjectUtils.isEmpty(UserContextSingleton.INSTANCE)
                || ObjectUtils.isEmpty(UserContextSingleton.INSTANCE.getUserContext())
                || StringUtils.isEmpty(UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount())
                || UserContextSingleton.INSTANCE.getUserContext().getEmployeeId() == null) {
            request.setAttribute("errorMsg", "没有登陆纷享crm"); //ignorei18n
            request.setAttribute("propose", "请登录纷享crm，再刷新页面"); //ignorei18n
            request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
            return null;
        }
        Map<String, List<Integer>> userToolsAccountMap = new Gson().fromJson(ConfigCenter.USE_TOOLS_ACCOUNT, new TypeToken<Map<String, List<Integer>>>() {
        }.getType());
        String ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        Integer ei = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseId();
        Integer userId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
        if(userToolsAccountMap.containsKey(ea) && userToolsAccountMap.get(ea).contains(userId)) {
            ModelAndView mv = new ModelAndView("/WEB-INF/tools/index.html");
            return mv;
        }
        request.setAttribute("errorMsg", i18NStringManager.getByEi(I18NStringEnum.s80, String.valueOf(ei)));
        request.setAttribute("propose", i18NStringManager.getByEi(I18NStringEnum.s81, String.valueOf(ei)));
        request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
        return null;
    }

    private String decodeData(String data) {
        Pattern pattern = Pattern.compile(VER+"(.*)");
        Matcher matcher = pattern.matcher(SecurityUtil.decryptStr(data));
        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return null;
        }
    }
}
