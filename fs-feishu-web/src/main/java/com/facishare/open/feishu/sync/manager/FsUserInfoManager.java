package com.facishare.open.feishu.sync.manager;

import com.facishare.open.feishu.sync.mongo.dao.FsUserInfoMongoDao;
import com.facishare.open.feishu.sync.mongo.document.FsUserInfoDoc;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component("fsUserInfoManager")
public class FsUserInfoManager {
    @Autowired
    private FsUserInfoMongoDao fsUserInfoMongoDao;

    public BulkWriteResult batchReplace(List<FsUserInfoDoc> docs) {
        BulkWriteResult bulkWriteResult = fsUserInfoMongoDao.batchReplace(docs);
        return bulkWriteResult;
    }

    public Long countDocuments(String fsEa) {
        Long counts = fsUserInfoMongoDao.countDocuments(fsEa);
        return counts;
    }

    public List<FsUserInfoDoc> queryUserInfos(String fsEa, Integer status) {
        List<FsUserInfoDoc> docs = fsUserInfoMongoDao.queryUserInfos(fsEa, status);
        return docs;
    }

    public List<FsUserInfoDoc> queryUserInfosByNotIds(String fsEa, Integer status, List<Integer> userIds) {
        List<FsUserInfoDoc> docs = fsUserInfoMongoDao.queryUserInfosByNotIds(fsEa, status, userIds);
        return docs;
    }

    public List<FsUserInfoDoc> queryUserInfosByIds(String fsEa, Integer status, List<Integer> userIds) {
        List<FsUserInfoDoc> docs = fsUserInfoMongoDao.queryUserInfosByIds(fsEa, status, userIds);
        return docs;
    }

    public DeleteResult deleteNotInCollectionDocs(String fsEa, List<FsUserInfoDoc> docs) {
        return fsUserInfoMongoDao.deleteNotInCollectionDocs(fsEa, docs);
    }
}
