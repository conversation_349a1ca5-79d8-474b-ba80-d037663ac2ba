package com.facishare.open.feishu.sync.service.impl.whatsapp;

import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NGetFileMetaData;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.sync.manager.FileTableManager;
import com.facishare.open.feishu.syncapi.data.whatsapp.*;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.entity.FileEntity;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.whatsapp.*;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappService;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappTemplateService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("whatsappTemplateService")
public class WhatsappTemplateServiceImpl implements WhatsappTemplateService {
    @Resource
    private WhatsappService whatsappService;
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Resource
    private FileTableManager fileTableManager;
    @Resource
    private NFileStorageService nFileStorageService;

    private static final Long max_image_size = 5 * 1024 * 1024L;
    private static final Long max_video_file_size = 16 * 1024 * 1024L;
    private static final Long max_audio_file_size = 16 * 1024 * 1024L;
    private static final Long max_document_file_size = 100 * 1024 * 1024L;
    private static final Long max_sticker_file_size = 100L;

    @Override
    public Result<WhatsappGetTemplateResult> getTemplateData(WhatsappGetTemplate getTemplate) {
        //查看是否有绑定关系
        if(StringUtils.isAnyEmpty(getTemplate.getFsEa(), getTemplate.getBusiness_phone())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, getTemplate.getFsEa(), null);
        if(ObjectUtils.isEmpty(entity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }

        //获取模板
        WhatsappGetTemplate whatsappGetTemplate = new WhatsappGetTemplate();
        BeanUtils.copyProperties(getTemplate, whatsappGetTemplate);
        whatsappGetTemplate.setFsEa(null);
        whatsappGetTemplate.setAppkey(entity.getAppKey());
        com.facishare.open.feishu.syncapi.result.whatsapp.Result<WhatsappGetTemplateResult> getTemplateResultResult = whatsappService.whatsappGetTemplate(entity, whatsappGetTemplate);
        if(!getTemplateResultResult.isSuccess()) {
            return Result.newError(ResultCodeEnum.getCodeByOutCode(String.valueOf(getTemplateResultResult.getCode()), ResultCodeEnum.GET_TEMPLATE_ERROR.getCode()), getTemplateResultResult.getMessage());
        }

        //查看是否有需要处理的媒体id
        WhatsappGetTemplateResult getTemplateResult = getTemplateResultResult.getData();
        //不用转换，这个返回的是一个地址
//        if(CollectionUtils.isNotEmpty(getTemplateResult.getData())) {
//            for(WhatsappGetTemplateResult.TemplateInfo templateInfo : getTemplateResult.getData()) {
//                if(CollectionUtils.isNotEmpty(templateInfo.getComponents())) {
//                    for(Component component : templateInfo.getComponents()) {
//                        if(ObjectUtils.isNotEmpty(component.getExample()) && StringUtils.isNotEmpty(component.getExample().getHeader_handle())) {
//                            //转换
//                            List<FileEntity> entities = fileManager.queryEntities(entity.getFsEa(), null, component.getExample().getHeader_handle());
//                            if(CollectionUtils.isNotEmpty(entities)) {
//                                LogUtils.info("WhatsappTemplateServiceImpl.getTemplateData.entities.size={}", entities.size());
//                                component.getExample().setHeader_handle(entities.get(0).getNpath());
//                            }
//                        }
//                    }
//                }
//            }
//        }
        return Result.newSuccess(getTemplateResult);
    }

    @Override
    public Result<WhatsappCreateTemplateResult> createTemplateData(WhatsappCreateTemplate createTemplate) {
        //查看是否有绑定关系
        if(StringUtils.isAnyEmpty(createTemplate.getFsEa(), createTemplate.getBusiness_phone())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, createTemplate.getFsEa(), null);
        if(ObjectUtils.isEmpty(entity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }

        //创建模板
        WhatsappCreateTemplate whatsappCreateTemplate = new WhatsappCreateTemplate();
        BeanUtils.copyProperties(createTemplate, whatsappCreateTemplate);
        whatsappCreateTemplate.setFsEa(null);
        whatsappCreateTemplate.setAppkey(entity.getAppKey());

        //查看是否有需要处理的媒体id
        if(ObjectUtils.isNotEmpty(whatsappCreateTemplate)) {
            if (CollectionUtils.isNotEmpty(whatsappCreateTemplate.getComponents())) {
                for (Component component : whatsappCreateTemplate.getComponents()) {
                    if (ObjectUtils.isNotEmpty(component.getExample()) && StringUtils.isNotEmpty(component.getExample().getNpath())) {
                        //先查看是否已存在过
                        List<FileEntity> fileEntities = fileTableManager.queryEntities(entity.getFsEa(), component.getExample().getNpath(), null);
                        if(CollectionUtils.isNotEmpty(fileEntities)) {
                            component.getExample().setHeader_handle(fileEntities.get(0).getMediaId());
                        } else {
                            Result<String> whatsappUploadFileResultResult = getMediaId(entity, component.getExample().getNpath(), component.getExample().getType(), component.getExample().getFilename(), createTemplate.getBusiness_phone());
                            if(!whatsappUploadFileResultResult.isSuccess()) {
                                return Result.newError(whatsappUploadFileResultResult.getCode(), whatsappUploadFileResultResult.getMsg());
                            }
                            component.getExample().setHeader_handle(whatsappUploadFileResultResult.getData());
                        }
                        component.getExample().setNpath(null);
                        component.getExample().setType(null);
                        component.getExample().setFilename(null);
                    }
                }
            }
        }

        com.facishare.open.feishu.syncapi.result.whatsapp.Result<WhatsappCreateTemplateResult> createTemplateResultResult = whatsappService.whatsappCreateTemplate(entity, whatsappCreateTemplate);
        if(!createTemplateResultResult.isSuccess()) {
            return Result.newError(ResultCodeEnum.CREATE_TEMPLATE_ERROR.getCode(), createTemplateResultResult.getMessage());
        }
        return Result.newSuccess(createTemplateResultResult.getData());
    }

    @Override
    public Result<WhatsappUpdateTemplateResult> updateTemplateData(WhatsappUpdateTemplate updateTemplate) {
        //查看是否有绑定关系
        if(StringUtils.isAnyEmpty(updateTemplate.getFsEa(), updateTemplate.getBusiness_phone())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, updateTemplate.getFsEa(), null);
        if(ObjectUtils.isEmpty(entity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }

        //创建模板
        WhatsappUpdateTemplate whatsappUpdateTemplate = new WhatsappUpdateTemplate();
        BeanUtils.copyProperties(updateTemplate, whatsappUpdateTemplate);
        whatsappUpdateTemplate.setFsEa(null);
        whatsappUpdateTemplate.setAppkey(entity.getAppKey());

        //查看是否有需要处理的媒体id
        if(ObjectUtils.isNotEmpty(whatsappUpdateTemplate)) {
            if (CollectionUtils.isNotEmpty(whatsappUpdateTemplate.getComponents())) {
                for (Component component : whatsappUpdateTemplate.getComponents()) {
                    if (ObjectUtils.isNotEmpty(component.getExample()) && StringUtils.isNotEmpty(component.getExample().getNpath()) && component.getExample().getNpath().startsWith("N_")) {
                        //先查看是否已存在过
                        List<FileEntity> fileEntities = fileTableManager.queryEntities(entity.getFsEa(), component.getExample().getNpath(), null);
                        if(CollectionUtils.isNotEmpty(fileEntities)) {
                            component.getExample().setHeader_handle(fileEntities.get(0).getMediaId());
                        } else {
                            //上传文件
                            Result<String> whatsappUploadFileResultResult = getMediaId(entity, component.getExample().getNpath(), component.getExample().getType(), component.getExample().getFilename(), updateTemplate.getBusiness_phone());
                            if(!whatsappUploadFileResultResult.isSuccess()) {
                                return Result.newError(whatsappUploadFileResultResult.getCode(), whatsappUploadFileResultResult.getMsg());
                            }
                            component.getExample().setHeader_handle(whatsappUploadFileResultResult.getData());
                        }
                        component.getExample().setNpath(null);
                        component.getExample().setType(null);
                        component.getExample().setFilename(null);
                    }
                }
            }
        }

        com.facishare.open.feishu.syncapi.result.whatsapp.Result<WhatsappUpdateTemplateResult> updateTemplateResultResult = whatsappService.whatsappUpdateTemplate(entity, whatsappUpdateTemplate);
        if(!updateTemplateResultResult.isSuccess()) {
            return Result.newError(ResultCodeEnum.UPDATE_TEMPLATE_ERROR.getCode(), updateTemplateResultResult.getMessage());
        }
        return Result.newSuccess(updateTemplateResultResult.getData());
    }

    @Override
    public Result<WhatsappDeleteTemplateResult> deleteTemplateData(WhatsappDeleteTemplate deleteTemplate) {
        //查看是否有绑定关系
        if(StringUtils.isAnyEmpty(deleteTemplate.getFsEa(), deleteTemplate.getBusiness_phone())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, deleteTemplate.getFsEa(), null);
        if(ObjectUtils.isEmpty(entity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }

        //删除模板
        WhatsappDeleteTemplate whatsappDeleteTemplate = new WhatsappDeleteTemplate();
        BeanUtils.copyProperties(deleteTemplate, whatsappDeleteTemplate);
        whatsappDeleteTemplate.setFsEa(null);
        whatsappDeleteTemplate.setAppkey(entity.getAppKey());

        com.facishare.open.feishu.syncapi.result.whatsapp.Result<WhatsappDeleteTemplateResult> deleteTemplateResultResult = whatsappService.whatsappDeleteTemplate(entity, whatsappDeleteTemplate);

        if(!deleteTemplateResultResult.isSuccess()) {
            return Result.newError(ResultCodeEnum.DELETE_TEMPLATE_ERROR.getCode(), deleteTemplateResultResult.getMessage());
        }
        return Result.newSuccess(deleteTemplateResultResult.getData());
    }

    private Result<String> getMediaId(EnterpriseBindEntity entity, String npath, String type, String filename, String businessPhone) {
        LogUtils.info("WhatsappTemplateServiceImpl.getMediaId.npath={},type={}，filename={}", npath, type, filename);

        List<FileEntity> fileEntities = fileTableManager.queryEntities(entity.getFsEa(), npath, null);
        if(CollectionUtils.isNotEmpty(fileEntities)) {
            return Result.newSuccess(fileEntities.get(0).getMediaId());
        }

        //上传
        WhatsappUploadMedia uploadMedia = new WhatsappUploadMedia();
        uploadMedia.setBusiness_phone(businessPhone);
        uploadMedia.setType(type);

        //上传文件
        NGetFileMetaData.Arg arg = new NGetFileMetaData.Arg();
        arg.setEa(entity.getFsEa());
        arg.setFileName(npath);
        NGetFileMetaData.Result fileMetaData = nFileStorageService.nGetFileMetaData(arg, entity.getFsEa());
        if(ObjectUtils.isEmpty(fileMetaData)) {
            return Result.newError(ResultCodeEnum.GET_FILE_META_DATA_FAILED.getCode(),ResultCodeEnum.GET_FILE_META_DATA_FAILED.getMsg());
        }

        uploadMedia.setFileName(StringUtils.isNotEmpty(filename) ? filename : fileMetaData.getFileName());

        if(type.equals("image/webp") && fileMetaData.getSize() > max_sticker_file_size) {
            return Result.newError(ResultCodeEnum.STICKER_OVERSIZE.getCode(),ResultCodeEnum.STICKER_OVERSIZE.getMsg());
        } else if(StringUtils.containsIgnoreCase(type, "image") && fileMetaData.getSize() > max_image_size) {
            return Result.newError(ResultCodeEnum.IMAGE_OVERSIZE.getCode(),ResultCodeEnum.GET_FILE_META_DATA_FAILED.getMsg());
        } else if(StringUtils.containsIgnoreCase(type, "audio") && fileMetaData.getSize() > max_audio_file_size) {
            return Result.newError(ResultCodeEnum.AUDIO_OVERSIZE.getCode(),ResultCodeEnum.AUDIO_OVERSIZE.getMsg());
        } else if(StringUtils.containsIgnoreCase(type, "video") && fileMetaData.getSize() > max_video_file_size) {
            return Result.newError(ResultCodeEnum.VIDEO_OVERSIZE.getCode(),ResultCodeEnum.VIDEO_OVERSIZE.getMsg());
        } else if(fileMetaData.getSize() > max_document_file_size) {
            return Result.newError(ResultCodeEnum.DOCUMENT_OVERSIZE.getCode(),ResultCodeEnum.DOCUMENT_OVERSIZE.getMsg());
        }

        NDownloadFile.Arg downloadFile = new NDownloadFile.Arg();
        downloadFile.setEa(entity.getFsEa());
        downloadFile.setnPath(npath);
        downloadFile.setDownloadSecurityGroup(fileMetaData.getSecurityGroup());
        NDownloadFile.Result downloadFileMetaData = nFileStorageService.nDownloadFile(downloadFile, entity.getFsEa());
        uploadMedia.setFile(downloadFileMetaData.getData());

        com.facishare.open.feishu.syncapi.result.whatsapp.Result<WhatsappUploadFileResult> whatsappUploadFileResultResult = whatsappService.whatsappUploadTemplateFile(entity, uploadMedia);
        if(!whatsappUploadFileResultResult.isSuccess()) {
            return Result.newError(ResultCodeEnum.getCodeByOutCode(String.valueOf(whatsappUploadFileResultResult.getCode()), ResultCodeEnum.UPLOAD_TEMPLATE_FILE_ERROR.getCode()), whatsappUploadFileResultResult.getMessage());
        }
        FileEntity fileEntity = new FileEntity();
        fileEntity.setFsEa(entity.getFsEa());
        fileEntity.setMediaId(whatsappUploadFileResultResult.getData().getId());
        fileEntity.setNpath(npath);
        fileEntity.setType(type);
        fileEntity.setFileSize(fileMetaData.getSize());
        Integer count = fileTableManager.insert(fileEntity);
        LogUtils.info("WhatsappTemplateServiceImpl.getMediaId.count={}", count);

        return Result.newSuccess(whatsappUploadFileResultResult.getData().getId());
    }
}
