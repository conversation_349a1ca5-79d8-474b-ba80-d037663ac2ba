package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.sync.limiter.CrmMessagePullLimiter;
import com.facishare.open.feishu.sync.manager.*;
import com.facishare.open.feishu.sync.mongo.dao.CrmTodoRetryMongoDao;
import com.facishare.open.feishu.sync.mongo.document.CrmTodoRetryDoc;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.config.OAMessageTag;
import com.facishare.open.feishu.syncapi.entity.*;
import com.facishare.open.feishu.syncapi.enums.*;
import com.facishare.open.feishu.syncapi.model.SendMessage.PostMassageModel;
import com.facishare.open.feishu.syncapi.model.event.ExternalDealTodoEvent;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalApprovalsTemplate;
import com.facishare.open.feishu.syncapi.result.ExternalApprovalsTemplateResult;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.service.ExternalApprovalsService;
import com.facishare.open.feishu.syncapi.service.ExternalTodoMsgService;
import com.facishare.open.feishu.syncapi.service.MsgService;
import com.facishare.open.order.contacts.proxy.api.arg.DealCrmTodoArg;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsObjServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.message.extrnal.platform.model.arg.*;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mongodb.bulk.BulkWriteResult;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.ObjectUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Service("externalTodoMsgService")
public class ExternalTodoMsgServiceImpl implements ExternalTodoMsgService {
    @Autowired
    private MsgService msgService;
    @Autowired
    private EnterpriseBindManager enterpriseBindManager;
    @Autowired
    private EmployeeBindManager employeeBindManager;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;
    @Resource
    private CrmMessagePullLimiter crmMessagePullLimiter;
    @Resource
    private ExternalApprovalsTemplateManager externalApprovalsTemplateManager;
    @Resource
    private ExternalApprovalsService externalApprovalsService;
    @Resource
    private ExternalTodoManager externalTodoManager;
    @Resource
    private FsObjServiceProxy fsObjServiceProxy;
    @Resource
    private ExternalTodoTaskManager externalTodoTaskManager;
    @Resource
    private ExternalTodoInstanceManager externalTodoInstanceManager;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private CrmTodoRetryMongoDao crmTodoRetryMongoDao;
    @Resource
    private I18NStringManager i18NStringManager;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;

    private static final String approvalBizType = "452";

    @Override
    public Result<Void> createTodo(CreateTodoArg arg) {
        List<String> fsUserIdList = arg.getReceiverIds().stream().map(v -> v + "").collect(Collectors.toList());
        List<EnterpriseBindEntity> enterpriseBindEntities = enterpriseBindManager.getEntityList(arg.getEa(), BindStatusEnum.normal);
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newSuccess();
        }
        //函数过滤
        Map<String, String> filterMessagesEaMap = new Gson().fromJson(ConfigCenter.FILTER_MESSAGES_EA, new TypeToken<Map<String, String>>() {
        });
        if (filterMessagesEaMap.containsKey(arg.getEa())) {
            Boolean isPull = crmMessagePullLimiter.messageIsPull(arg.getEi(), filterMessagesEaMap.get(arg.getEa()), new Gson().toJson(arg));
            if (!isPull) {
                return Result.newSuccess();
            }
        }

        for (EnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            //判断人员信息
            List<EmployeeBindEntity> employeeBindEntities = employeeBindManager.getEntityList(arg.getEa(), enterpriseBindEntity.getOutEa(), fsUserIdList);
            if (CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            //待处理的审批流程
            if (arg.getBizType().equals(approvalBizType) && ConfigCenter.TODO_GRAY_EA.contains(arg.getEa())) {
                createExternalApprovalTodo(arg, enterpriseBindEntity, employeeBindEntities);
            } else {
                //推送到纷享机器人
                createExternalCrmBot(arg, enterpriseBindEntity, employeeBindEntities);
            }
        }
        return Result.newSuccess();
    }

    private Result<Void> createExternalApprovalTodo(CreateTodoArg arg,
                                                  EnterpriseBindEntity enterpriseBindEntity,
                                                  List<EmployeeBindEntity> employeeBindEntities) {
        LogUtils.info("ExternalMsgServiceImpl.createExternalApprovalTodo,arg={},enterpriseBindEntity={},employeeBindEntities={}.", arg, enterpriseBindEntity, employeeBindEntities);

        //审批定义
        //审批机器人是依托审批中心存在，只有审批中心推送成功，才会尝试去推送审批机器人，即使单独推送审批机器人也是可以的
        ExternalApprovalsTemplateEntity templateEntity = externalApprovalsTemplateManager.queryEntity(ChannelEnum.feishu, enterpriseBindEntity.getOutEa());
        if(ObjectUtils.isEmpty(templateEntity)) {
            //创建
            String appId=ObjectUtils.isEmpty(enterpriseBindEntity.getAppId())?ConfigCenter.feishuCrmAppId:enterpriseBindEntity.getAppId();
            Result<ExternalApprovalsTemplateResult> approvalsTemplateResultResult = createExternalApprovalsTemplate(enterpriseBindEntity.getOutEa(), appId);
            LogUtils.info("ExternalTodoMsgServiceImpl.createTodo,approvalsTemplateResultResult={}", approvalsTemplateResultResult);
            if (approvalsTemplateResultResult.isSuccess()) {
                ExternalApprovalsTemplateEntity approvalsTemplateEntity = ExternalApprovalsTemplateEntity.builder()
                        .channel(ChannelEnum.feishu)
                        .outEa(enterpriseBindEntity.getOutEa())
                        .approvalCode(approvalsTemplateResultResult.getData().getApprovalCode())
                        .status(0)
                        .build();
                Integer count = externalApprovalsTemplateManager.insert(approvalsTemplateEntity);
                LogUtils.info("ExternalTodoMsgServiceImpl.createTodo,approvalsTemplateResultResult,count={}", count);
                if(count == 0) {
                    //新增模板失败
                    return Result.newSuccess();
                }
                //创建好了就获取下
                templateEntity = externalApprovalsTemplateManager.queryEntity(ChannelEnum.feishu, enterpriseBindEntity.getOutEa());
            }
        } else {
            //查看状态是否正常
            if(templateEntity.getStatus() != 0) {
                //已经停用，走crm推送的模式
                //推送到纷享机器人
                LogUtils.info("ExternalTodoMsgServiceImpl.createTodo,approvalsTemplateResult is stop");
                createExternalCrmBot(arg, enterpriseBindEntity, employeeBindEntities);
                return Result.newSuccess();
            }
        }
        Gson gson = new Gson();

        //推送到审批中心
        List<CrmTodoRetryDoc> crmTodoRetryDocs = new LinkedList<>();
        Result<Void> externalApprovalTodoResult = externalTodoManager.createExternalApprovalTodo(arg, templateEntity, enterpriseBindEntity, employeeBindEntities);
        if(!externalApprovalTodoResult.isSuccess()) {
            //没有成功，存起来重试
            CrmTodoRetryDoc crmTodoRetryDoc = new CrmTodoRetryDoc();
            crmTodoRetryDoc.setId(ObjectId.get());
            crmTodoRetryDoc.setFsEa(arg.getEa());
            crmTodoRetryDoc.setOutEa(enterpriseBindEntity.getOutEa());
            crmTodoRetryDoc.setSourceId(arg.getSourceId());
            crmTodoRetryDoc.setEvent(OAMessageTag.CREATE_TO_DO_TAG);
            crmTodoRetryDoc.setEventType(ExternalMsgTypeEnum.CreateApproval.name());
            crmTodoRetryDoc.setStatus(0);
            crmTodoRetryDoc.setRetryTimes(0);
            crmTodoRetryDoc.setContent(gson.toJson(arg));
            crmTodoRetryDoc.setResult(externalApprovalTodoResult.getMsg());
            long currentTimeMillis = System.currentTimeMillis();
            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
            crmTodoRetryDoc.setCreateTime(currentTimeMillis);
            crmTodoRetryDocs.add(crmTodoRetryDoc);
        }

        //推送到审批机器人
        Result<Void> externalTodoTaskResult = externalTodoManager.createExternalTodoTask(arg, enterpriseBindEntity, employeeBindEntities);
        if(!externalTodoTaskResult.isSuccess()) {
            //没有成功，存起来重试，失败后就不处理下面的逻辑了
            CrmTodoRetryDoc crmTodoRetryDoc = new CrmTodoRetryDoc();
            crmTodoRetryDoc.setId(ObjectId.get());
            crmTodoRetryDoc.setFsEa(arg.getEa());
            crmTodoRetryDoc.setOutEa(enterpriseBindEntity.getOutEa());
            crmTodoRetryDoc.setSourceId(arg.getSourceId());
            crmTodoRetryDoc.setEvent(OAMessageTag.CREATE_TO_DO_TAG);
            crmTodoRetryDoc.setEventType(ExternalMsgTypeEnum.CreateApprovalTask.name());
            crmTodoRetryDoc.setStatus(0);
            crmTodoRetryDoc.setRetryTimes(0);
            crmTodoRetryDoc.setContent(gson.toJson(arg));
            crmTodoRetryDoc.setResult(externalTodoTaskResult.getMsg());
            long currentTimeMillis = System.currentTimeMillis();
            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
            crmTodoRetryDoc.setCreateTime(currentTimeMillis);
            crmTodoRetryDocs.add(crmTodoRetryDoc);
        }

        if(CollectionUtils.isNotEmpty(crmTodoRetryDocs)) {
            BulkWriteResult bulkWriteResult = crmTodoRetryMongoDao.batchReplace(crmTodoRetryDocs);
            LogUtils.info("ExternalMsgServiceImpl.createTodo,bulkWriteResult={}.", bulkWriteResult);
        }
        return Result.newSuccess();
    }

    private Result<Void> createExternalCrmBot(CreateTodoArg arg,
                                          EnterpriseBindEntity enterpriseBindEntity,
                                          List<EmployeeBindEntity> employeeBindEntities) {
        LogUtils.info("ExternalMsgServiceImpl.createExternalCrmBot,arg={},enterpriseBindEntity={},employeeBindEntities={}.", arg, enterpriseBindEntity, employeeBindEntities);

        //拼接文本内容
        PostMassageModel postMessageModel = new PostMassageModel();
        PostMassageModel.LanguageModel languageModel = new PostMassageModel.LanguageModel();
        PostMassageModel.LanguageModel.TextMessage textMessage = new PostMassageModel.LanguageModel.TextMessage();
        PostMassageModel.LanguageModel.TextMessage.Content content = new PostMassageModel.LanguageModel.TextMessage.Content();
        content.setTag(MsgTypeEnum.text.name());
        StringBuilder markdown = new StringBuilder();
        if (CollectionUtils.isNotEmpty(arg.getForm())) {
            for (int i = 0; i < arg.getForm().size(); i++) {
                markdown.append(arg.getForm().get(i).getKey()).append("：").append(arg.getForm().get(i).getValue()).append("\n");
            }
        } else {
            markdown.append(arg.getContent()).append("\n");
        }
        content.setText(markdown.toString());
        textMessage.setTitle(arg.getTitle());
        PostMassageModel.LanguageModel.TextMessage.Content hrefContent = new PostMassageModel.LanguageModel.TextMessage.Content();
        hrefContent.setTag("a");
        hrefContent.setText("\n查看详情");
        //处理url,如果有自建应用，此处的appId需要改成从库取
        String todoUrl;
        if(arg.getGenerateUrlType() == BaseExternalArg.BPM_TASK_URL) {
            if(ObjectUtils.isEmpty(arg.getExtraDataMap())
                    || StringUtils.isEmpty(arg.getExtraDataMap().get("workflowInstanceId"))
                    || StringUtils.isEmpty(arg.getExtraDataMap().get("activityId"))) {
                //暂无跳转链接，返回，打印日志，不发送：https://wiki.firstshare.cn/pages/viewpage.action?pageId=214734110
                LogUtils.info("ExternalMsgServiceImpl.createExternalCrmBot,param is null,arg={}.", arg);
                return Result.newSuccess();
            }
            //bpm
//            todoUrl = getEnterpriseInfo(arg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FUNCTION_URL + ConfigCenter.CRM_BPM_URL
//                    .replace("{workflowInstanceId}", arg.getExtraDataMap().get("workflowInstanceId"))
//                    .replace("{activityId}", arg.getExtraDataMap().get("activityId"))
//                    .replace("{apiname}", arg.getExtraDataMap().get("objectApiName"))
//                    .replace("{id}", arg.getExtraDataMap().get("objectId"));
            //bpm业务流，默认先跳转到对象详情页面，等BPM团队做完业务流详情页面，再改成跳转到业务流详情页面
            todoUrl = getEnterpriseInfo(arg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FUNCTION_URL + ConfigCenter.CRM_TODO_URL
                    .replace("{apiname}", arg.getExtraDataMap().get("objectApiName"))
                    .replace("{id}", arg.getExtraDataMap().get("objectId"))
                    .replace("{ea}", arg.getEa());
        } else {
            if(StringUtils.isNotEmpty(arg.getUrl())) {
                todoUrl = getEnterpriseInfo(arg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FUNCTION_URL + arg.getUrl();
            } else if(ObjectUtils.isNotEmpty(arg.getExtraDataMap())
                    && StringUtils.isNotEmpty(arg.getExtraDataMap().get("objectApiName"))
                    && StringUtils.isNotEmpty(arg.getExtraDataMap().get("objectId"))
                    && ConfigCenter.FEISHU_IS_ADD_TODO) {
                todoUrl = getEnterpriseInfo(arg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FUNCTION_URL + ConfigCenter.CRM_TODO_URL.replace("{apiname}", arg.getExtraDataMap().get("objectApiName")).replace("{id}", arg.getExtraDataMap().get("objectId")).replace("{ea}", arg.getEa());
            } else {
                //暂无跳转链接，返回，打印日志，不发送：https://wiki.firstshare.cn/pages/viewpage.action?pageId=214734110
                LogUtils.info("ExternalMsgServiceImpl.createExternalCrmBot,param is null,arg={}.", arg);
                return Result.newSuccess();
            }
        }
        LogUtils.info("ExternalMsgServiceImpl,createExternalCrmBot,todoUrl={}",todoUrl);
        byte[] todoUrlBytes = todoUrl.getBytes();
        String param = new String(Base64.encodeBase64(todoUrlBytes));
        String appId = ObjectUtils.isNotEmpty(enterpriseBindEntity.getAppId())?enterpriseBindEntity.getAppId():ConfigCenter.feishuCrmAppId;
        String authUrl = ConfigCenter.FEISHU_AUTHEN_URL
                .replace("{app_id}", appId)
                .replace("{state}", appId )
                .replace("{redirect_uri}", URLEncoder.encode(ConfigCenter.CRM_AUTH_URL + "?param=" + param + "&fsEa=" + arg.getEa()));
        String url = ConfigCenter.FEISHU_WEB_URL
                .replace("{mode}", "window")
                .replace("{url}", URLEncoder.encode(authUrl));
        hrefContent.setHref(url);
        List<PostMassageModel.LanguageModel.TextMessage.Content> contentList = new LinkedList<>();
        contentList.add(content);
        contentList.add(hrefContent);
        List<List<PostMassageModel.LanguageModel.TextMessage.Content>> messageContentList = new LinkedList<>();
        messageContentList.add(contentList);
        textMessage.setContent(messageContentList);
        languageModel.setZh_cn(textMessage);
        postMessageModel.setPost(languageModel);
        LogUtils.info("ExternalTodoMsgServiceImpl.createExternalCrmBot,ea={},postMessageModel={}.", arg.getEa(), postMessageModel);
        List<String> receiverIds = employeeBindEntities.stream().map(EmployeeBindEntity::getOutUserId).collect(Collectors.toList());
        Result<Void> msgResult = msgService.batchSend(appId, enterpriseBindEntity.getOutEa(), MsgTypeEnum.post, receiverIds, postMessageModel);
        LogUtils.info("ExternalTodoMsgServiceImpl.createExternalCrmBot,msgResult={}.", msgResult);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> dealTodo(DealTodoArg arg) {
        //判断类型
        if(!arg.getBizType().equals(approvalBizType)) {
            return Result.newSuccess();
        }

        //判断是否有绑定关系
        List<String> fsUserIdList = arg.getOperators().stream().map(v -> v + "").collect(Collectors.toList());
        List<EnterpriseBindEntity> enterpriseBindEntities = enterpriseBindManager.getEntityList(arg.getEa(), BindStatusEnum.normal);
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newSuccess();
        }
        Gson gson = new Gson();
        for(EnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            //判断人员信息
            List<EmployeeBindEntity> employeeBindEntities = employeeBindManager.getEntityList(arg.getEa(), enterpriseBindEntity.getOutEa(), fsUserIdList);
            if (CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            if(isDealTodo(arg.getEa(), enterpriseBindEntity.getOutEa(), arg.getSourceId())) {
                //审批中心
                List<CrmTodoRetryDoc> crmTodoRetryDocs = new LinkedList<>();
                Result<Void> dealExternalApprovalsResult = externalTodoManager.dealExternalApprovalTodo(arg, employeeBindEntities, enterpriseBindEntity);
                if(!dealExternalApprovalsResult.isSuccess()) {
                    CrmTodoRetryDoc crmTodoRetryDoc = new CrmTodoRetryDoc();
                    crmTodoRetryDoc.setId(ObjectId.get());
                    crmTodoRetryDoc.setFsEa(arg.getEa());
                    crmTodoRetryDoc.setOutEa(enterpriseBindEntity.getOutEa());
                    crmTodoRetryDoc.setSourceId(arg.getSourceId());
                    crmTodoRetryDoc.setEvent(OAMessageTag.DEAL_TO_DO_TAG);
                    crmTodoRetryDoc.setEventType(ExternalMsgTypeEnum.DealApproval.name());
                    crmTodoRetryDoc.setStatus(0);
                    crmTodoRetryDoc.setRetryTimes(0);
                    crmTodoRetryDoc.setContent(gson.toJson(arg));
                    crmTodoRetryDoc.setResult(dealExternalApprovalsResult.getMsg());
                    long currentTimeMillis = System.currentTimeMillis();
                    crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                    crmTodoRetryDoc.setCreateTime(currentTimeMillis);
                    crmTodoRetryDocs.add(crmTodoRetryDoc);
                }
                //审批机器人
                Result<Void> dealExternalApprovalTodoTaskResult = externalTodoManager.dealExternalApprovalTodoTask(arg, employeeBindEntities, enterpriseBindEntity);
                if(!dealExternalApprovalTodoTaskResult.isSuccess()) {
                    CrmTodoRetryDoc crmTodoRetryDoc = new CrmTodoRetryDoc();
                    crmTodoRetryDoc.setId(ObjectId.get());
                    crmTodoRetryDoc.setFsEa(arg.getEa());
                    crmTodoRetryDoc.setOutEa(enterpriseBindEntity.getOutEa());
                    crmTodoRetryDoc.setSourceId(arg.getSourceId());
                    crmTodoRetryDoc.setEvent(OAMessageTag.DEAL_TO_DO_TAG);
                    crmTodoRetryDoc.setEventType(ExternalMsgTypeEnum.DealApprovalTask.name());
                    crmTodoRetryDoc.setStatus(0);
                    crmTodoRetryDoc.setRetryTimes(0);
                    crmTodoRetryDoc.setContent(gson.toJson(arg));
                    crmTodoRetryDoc.setResult(dealExternalApprovalTodoTaskResult.getMsg());
                    long currentTimeMillis = System.currentTimeMillis();
                    crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                    crmTodoRetryDoc.setCreateTime(currentTimeMillis);
                    crmTodoRetryDocs.add(crmTodoRetryDoc);
                }

                if(CollectionUtils.isNotEmpty(crmTodoRetryDocs)) {
                    BulkWriteResult bulkWriteResult = crmTodoRetryMongoDao.batchReplace(crmTodoRetryDocs);
                    LogUtils.info("ExternalMsgServiceImpl.dealTodo,bulkWriteResult={}.", bulkWriteResult);
                }
            }
        }
        return Result.newSuccess();
    }


    @Override
    public Result<Void> deleteTodo(DeleteTodoArg arg) {
        //判断类型
        if(!arg.getBizType().equals(approvalBizType)) {
            return Result.newSuccess();
        }

        //判断是否有绑定关系
        List<String> fsUserIdList = arg.getDeleteEmployeeIds().stream().map(v -> v + "").collect(Collectors.toList());
        List<EnterpriseBindEntity> enterpriseBindEntities = enterpriseBindManager.getEntityList(arg.getEa(), BindStatusEnum.normal);
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newSuccess();
        }
        Gson gson = new Gson();
        for(EnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            //判断人员信息
            List<EmployeeBindEntity> employeeBindEntities = employeeBindManager.getEntityList(arg.getEa(), enterpriseBindEntity.getOutEa(), fsUserIdList);
            if (CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            if(isDealTodo(arg.getEa(), enterpriseBindEntity.getOutEa(), arg.getSourceId())) {
                //审批中心
                List<CrmTodoRetryDoc> crmTodoRetryDocs = new LinkedList<>();
                Result<Void> deleteExternalApprovalsResult = externalTodoManager.deleteExternalApprovals(arg, employeeBindEntities, enterpriseBindEntity);
                if(!deleteExternalApprovalsResult.isSuccess()) {
                    CrmTodoRetryDoc crmTodoRetryDoc = new CrmTodoRetryDoc();
                    crmTodoRetryDoc.setId(ObjectId.get());
                    crmTodoRetryDoc.setFsEa(arg.getEa());
                    crmTodoRetryDoc.setOutEa(enterpriseBindEntity.getOutEa());
                    crmTodoRetryDoc.setSourceId(arg.getSourceId());
                    crmTodoRetryDoc.setEvent(OAMessageTag.DELETE_TO_DO);
                    crmTodoRetryDoc.setEventType(ExternalMsgTypeEnum.DeleteApproval.name());
                    crmTodoRetryDoc.setStatus(0);
                    crmTodoRetryDoc.setRetryTimes(0);
                    crmTodoRetryDoc.setContent(gson.toJson(arg));
                    crmTodoRetryDoc.setResult(deleteExternalApprovalsResult.getMsg());
                    long currentTimeMillis = System.currentTimeMillis();
                    crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                    crmTodoRetryDoc.setCreateTime(currentTimeMillis);
                    crmTodoRetryDocs.add(crmTodoRetryDoc);
                }
                //审批机器人
                Result<Void> deleteExternalApprovalTodoTaskResult = externalTodoManager.deleteExternalApprovalTodoTask(arg, employeeBindEntities, enterpriseBindEntity);
                if(!deleteExternalApprovalTodoTaskResult.isSuccess()) {
                    CrmTodoRetryDoc crmTodoRetryDoc = new CrmTodoRetryDoc();
                    crmTodoRetryDoc.setId(ObjectId.get());
                    crmTodoRetryDoc.setFsEa(arg.getEa());
                    crmTodoRetryDoc.setOutEa(enterpriseBindEntity.getOutEa());
                    crmTodoRetryDoc.setSourceId(arg.getSourceId());
                    crmTodoRetryDoc.setEvent(OAMessageTag.DELETE_TO_DO);
                    crmTodoRetryDoc.setEventType(ExternalMsgTypeEnum.DeleteApprovalTask.name());
                    crmTodoRetryDoc.setStatus(0);
                    crmTodoRetryDoc.setRetryTimes(0);
                    crmTodoRetryDoc.setContent(gson.toJson(arg));
                    crmTodoRetryDoc.setResult(deleteExternalApprovalTodoTaskResult.getMsg());
                    long currentTimeMillis = System.currentTimeMillis();
                    crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                    crmTodoRetryDoc.setCreateTime(currentTimeMillis);
                    crmTodoRetryDocs.add(crmTodoRetryDoc);
                }

                if(CollectionUtils.isNotEmpty(crmTodoRetryDocs)) {
                    BulkWriteResult bulkWriteResult = crmTodoRetryMongoDao.batchReplace(crmTodoRetryDocs);
                    LogUtils.info("ExternalMsgServiceImpl.deleteTodo,bulkWriteResult={}.", bulkWriteResult);
                }
            }
        }

        return Result.newSuccess();
    }

    private GetEnterpriseDataResult getEnterpriseInfo(String fsEa) {
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(fsEa);
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        LogUtils.info("ExternalTodoMsgServiceImpl.GetEnterpriseDataArg,result={}",result);
        return result;
    }

    private Result<ExternalApprovalsTemplateResult> createExternalApprovalsTemplate(String outEa, String appId) {
        LogUtils.info("ExternalTodoMsgServiceImpl.createExternalApprovalsTemplate,outEa={},appId={}", outEa, appId);
        ExternalApprovalsTemplate template = new ExternalApprovalsTemplate();
        template.setApprovalName("@i18n@1");
        template.setApprovalCode("fxiaoke_crm_approval_code");
        template.setGroupName("@i18n@2");
        template.setGroupCode("fxiaoke_crm_group_code");
        ExternalApprovalsTemplate.External external = new ExternalApprovalsTemplate.External();
        external.setBizName("@i18n@3");
        external.setSupportPc(Boolean.TRUE);
        external.setSupportMobile(Boolean.TRUE);
        external.setSupportBatchRead(Boolean.FALSE);
        template.setExternal(external);
        ExternalApprovalsTemplate.I18nResource zhResource = new ExternalApprovalsTemplate.I18nResource();
        zhResource.setLocale(ApprovalLocaleEnum.ZH_CN.getCode());
        zhResource.setIsDefault(Boolean.FALSE);
        ExternalApprovalsTemplate.I18nResource.Text zhText1 = new ExternalApprovalsTemplate.I18nResource.Text();
        zhText1.setKey("@i18n@1");
        zhText1.setValue("纷享销客CRM-审批"); //ignorei18n
        ExternalApprovalsTemplate.I18nResource.Text zhText2 = new ExternalApprovalsTemplate.I18nResource.Text();
        zhText2.setKey("@i18n@2");
        zhText2.setValue("纷享销客CRM"); //ignorei18n
        ExternalApprovalsTemplate.I18nResource.Text zhText3 = new ExternalApprovalsTemplate.I18nResource.Text();
        zhText3.setKey("@i18n@3");
        zhText3.setValue("纷享销客CRM"); //ignorei18n
        zhResource.setTexts(Lists.newArrayList(zhText1, zhText2, zhText3));

        ExternalApprovalsTemplate.I18nResource enResource = new ExternalApprovalsTemplate.I18nResource();
        enResource.setLocale(ApprovalLocaleEnum.EN_US.getCode());
        enResource.setIsDefault(Boolean.TRUE);
        ExternalApprovalsTemplate.I18nResource.Text enText1 = new ExternalApprovalsTemplate.I18nResource.Text();
        enText1.setKey("@i18n@1");
        enText1.setValue("ShareCRM-approval");
        ExternalApprovalsTemplate.I18nResource.Text enText2 = new ExternalApprovalsTemplate.I18nResource.Text();
        enText2.setKey("@i18n@2");
        enText2.setValue("ShareCRM");
        ExternalApprovalsTemplate.I18nResource.Text enText3 = new ExternalApprovalsTemplate.I18nResource.Text();
        enText3.setKey("@i18n@3");
        enText3.setValue("ShareCRM");
        enResource.setTexts(Lists.newArrayList(enText1, enText2, enText3));

        ExternalApprovalsTemplate.I18nResource jaResource = new ExternalApprovalsTemplate.I18nResource();
        jaResource.setLocale(ApprovalLocaleEnum.JA_JP.getCode());
        jaResource.setIsDefault(Boolean.FALSE);
        ExternalApprovalsTemplate.I18nResource.Text jaText1 = new ExternalApprovalsTemplate.I18nResource.Text();
        jaText1.setKey("@i18n@1");
        jaText1.setValue("ShareCRM-approval");
        ExternalApprovalsTemplate.I18nResource.Text jaText2 = new ExternalApprovalsTemplate.I18nResource.Text();
        jaText2.setKey("@i18n@2");
        jaText2.setValue("ShareCRM");
        ExternalApprovalsTemplate.I18nResource.Text jaText3 = new ExternalApprovalsTemplate.I18nResource.Text();
        jaText3.setKey("@i18n@3");
        jaText3.setValue("ShareCRM");
        jaResource.setTexts(Lists.newArrayList(jaText1, jaText2, jaText3));


        template.setI18nResources(Lists.newArrayList(zhResource, enResource, jaResource));
        ExternalApprovalsTemplate.Viewer viewer = new ExternalApprovalsTemplate.Viewer();
        viewer.setViewerType(ApprovalViewerTypeEnum.NONE.name());
        template.setViewers(Lists.newArrayList(viewer));

        return externalApprovalsService.createExternalApprovalsTemplate(outEa, appId, UserIdTypeEnum.open_id, DepartmentIdTypeEnum.open_department_id, template);
    }

    @Override
    public Result<Void> dealCrmTodo(ExternalDealTodoEvent externalDealTodoEvent) {
        String msgId = externalDealTodoEvent.getMessageId();
        //根据message_id查找是哪个审批的
        ExternalTodoTaskEntity externalTodoTaskEntity = externalTodoTaskManager.queryEntityByTaskId(msgId, null);
        if(ObjectUtils.isEmpty(externalTodoTaskEntity)) {
            LogUtils.info("ExternalTodoMsgServiceImpl.dealCrmTodo,externalDealTodoEvent={}",externalDealTodoEvent);
            //有问题处理失败
            return Result.newError(ResultCodeEnum.DEAL_APPROVAL_TODO_ERROR);
        }
        if(externalTodoTaskEntity.getStatus() != 0) {
            return Result.newSuccess();
        }

        String sourceId = externalTodoTaskEntity.getSourceId();
        String fsEa = externalTodoTaskEntity.getFsEa();
        Integer ei = eieaConverter.enterpriseAccountToId(fsEa);
        String tenantId = String.valueOf(ei);
        String outEa = externalTodoTaskEntity.getOutEa();
        String outUserId = externalTodoTaskEntity.getOutUserId();
        String entLang = i18NStringManager.getDefaultLang(tenantId);

        //找出人员映射
        EmployeeBindEntity employeeBindEntity = employeeBindManager.getEntity(outEa, outUserId, fsEa);
        if(ObjectUtils.isEmpty(employeeBindEntity)) {
            return Result.newError(ResultCodeEnum.EMPLOYEE_NO_BIND_INFO.getCode(), i18NStringManager.get(I18NStringEnum.s21, entLang, tenantId));
        }
        String userId = employeeBindEntity.getFsUserId();

        //通过人员对象获取
        com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> empDetailResult = fsEmployeeServiceProxy.detail(tenantId, userId);
        if(!empDetailResult.isSuccess() && ObjectUtils.isEmpty(empDetailResult.getData())) {
            return Result.newError(ResultCodeEnum.CRM_USER_QUERY_ERROR.getCode(), i18NStringManager.get(I18NStringEnum.s130, entLang, tenantId));
        }

        String empLang = ObjectUtils.isNotEmpty(empDetailResult.getData().get("language")) ? empDetailResult.getData().get("language").toString() : "zh-CN";

        //再去instance找详细数据，不必两张表存储重复的数据
        ExternalTodoInstanceEntity externalTodoInstanceEntity = externalTodoInstanceManager.queryEntity(fsEa, outEa, sourceId);
        if(ObjectUtils.isEmpty(externalTodoInstanceEntity)) {
            LogUtils.info("ExternalTodoMsgServiceImpl.dealCrmTodo,externalDealTodoEvent1={}",externalDealTodoEvent);
            //有问题处理失败
            return Result.newError(ResultCodeEnum.DEAL_APPROVAL_TODO_ERROR.getCode(), i18NStringManager.get(I18NStringEnum.s62, entLang, tenantId));
        }
        if(externalTodoInstanceEntity.getStatus() != 0) {
            return Result.newSuccess();
        }

        String taskId = externalTodoInstanceEntity.getTaskId();

        DealCrmTodoArg arg = new DealCrmTodoArg();
        arg.setTaskId(taskId);
        String actionType = externalDealTodoEvent.getActionType().equals("APPROVE") ? CrmDealTodoStatusEnum.agree.name() : CrmDealTodoStatusEnum.reject.name();
        arg.setActionType(actionType);
        String opinion = StringUtils.isEmpty(externalDealTodoEvent.getReason())
                ? (externalDealTodoEvent.getActionType().equals("APPROVE")
                ? i18NStringManager.get(I18NStringEnum.s93, empLang, tenantId) : i18NStringManager.get(I18NStringEnum.s94, empLang, tenantId)) : externalDealTodoEvent.getReason();
        arg.setOpinion(opinion);
        arg.setBlockWithLayout(Boolean.TRUE);

        com.facishare.open.order.contacts.proxy.api.result.ResultV2<Boolean> dealCrmTodoResult = fsObjServiceProxy.dealCrmTodo(ei, userId, arg);

        if(!dealCrmTodoResult.isSuccess()) {
            return Result.newError(ResultCodeEnum.CRM_DEAL_TODO_ERROR.getCode(), i18NStringManager.get(I18NStringEnum.s131, entLang, tenantId));
        } else {
            return Result.newSuccess();
        }
    }

    @Override
    public Result<Void> retryCrmTodo(EnterpriseBindEntity entity) {
        TraceUtils.initTraceId("retry_todo_" + entity.getFsEa() + "_" + entity.getOutEa());

        String fsEa = entity.getFsEa();
        String outEa = entity.getOutEa();

        //看看是否有失败的待办
        List<CrmTodoRetryDoc> crmTodoRetryDocs = crmTodoRetryMongoDao.queryCrmTodoInfos(fsEa, outEa, null, 0);
        if(CollectionUtils.isEmpty(crmTodoRetryDocs)) {
            return Result.newSuccess();
        }

        //查询模板
        ExternalApprovalsTemplateEntity templateEntity = externalApprovalsTemplateManager.queryEntity(ChannelEnum.feishu, outEa);

        // 将 List<CrmTodoRetryDoc> 转换成 Map<String, List<CrmTodoRetryDoc>>，并按 createTime 排序
        Map<String, List<CrmTodoRetryDoc>> resultMap = crmTodoRetryDocs.stream()
                .collect(Collectors.groupingBy(
                        CrmTodoRetryDoc::getSourceId, // 以 sourceId 作为键
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(CrmTodoRetryDoc::getCreateTime)) // 根据 createTime 排序
                                        .collect(Collectors.toList())
                        )
                ));

        //逐个重试
        Gson gson = new Gson();
        Set<String> retryTodoSet = resultMap.keySet();//将key作为元素转存入一个set集合
        for (String sourceId : retryTodoSet) {
            LogUtils.info("ExternalTodoMsgServiceImpl.retryCrmTodo,sourceId={}",sourceId);
            List<CrmTodoRetryDoc> crmTodoRetryDocLinkedList = new LinkedList<>();

            for(CrmTodoRetryDoc crmTodoRetryDoc : resultMap.get(sourceId)) {
                int retryTimes = crmTodoRetryDoc.getRetryTimes() + 1;
                if(crmTodoRetryDoc.getEvent().equals(OAMessageTag.CREATE_TO_DO_TAG)) {
                    CreateTodoArg arg = gson.fromJson(crmTodoRetryDoc.getContent(), CreateTodoArg.class);
                    List<String> fsUserIdList = arg.getReceiverIds().stream().map(v -> v + "").collect(Collectors.toList());
                    List<EmployeeBindEntity> employeeBindEntities = employeeBindManager.getEntityList(arg.getEa(), outEa, fsUserIdList);

                    if(crmTodoRetryDoc.getEventType().equals(ExternalMsgTypeEnum.CreateApproval.name())) {
                        //审批
                        Result<Void> externalApprovalTodoResult = externalTodoManager.createExternalApprovalTodo(arg,
                                templateEntity,
                                entity,
                                employeeBindEntities);
                        if(!externalApprovalTodoResult.isSuccess()) {
                            //没有成功，存起来重试
                            if(retryTimes == 3) {
                                crmTodoRetryDoc.setStatus(2);
                            }
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(externalApprovalTodoResult.getMsg());
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);
                        } else {
                            crmTodoRetryDoc.setStatus(1);
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(null);
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);

                        }
                    } else if(crmTodoRetryDoc.getEventType().equals(ExternalMsgTypeEnum.CreateApprovalTask.name())) {
                        //审批机器人
                        Result<Void> externalTodoTaskResult = externalTodoManager.createExternalTodoTask(arg, entity, employeeBindEntities);
                        if(!externalTodoTaskResult.isSuccess()) {
                            //没有成功，存起来重试
                            if(retryTimes == 3) {
                                crmTodoRetryDoc.setStatus(2);
                            }
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(externalTodoTaskResult.getMsg());
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);
                        } else {
                            crmTodoRetryDoc.setStatus(1);
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(null);
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);

                        }
                    }
                } else if(crmTodoRetryDoc.getEvent().equals(OAMessageTag.DEAL_TO_DO_TAG)) {
                    DealTodoArg arg = gson.fromJson(crmTodoRetryDoc.getContent(), DealTodoArg.class);
                    List<String> fsUserIdList = arg.getOperators().stream().map(v -> v + "").collect(Collectors.toList());

                    List<EmployeeBindEntity> employeeBindEntities = employeeBindManager.getEntityList(arg.getEa(), outEa, fsUserIdList);

                    if(crmTodoRetryDoc.getEventType().equals(ExternalMsgTypeEnum.DealApproval.name())) {
                        //审批
                        Result<Void> dealExternalApprovalsResult = externalTodoManager.dealExternalApprovalTodo(arg, employeeBindEntities, entity);
                        if(!dealExternalApprovalsResult.isSuccess()) {
                            if(retryTimes == 3) {
                                crmTodoRetryDoc.setStatus(2);
                            }
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(dealExternalApprovalsResult.getMsg());
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);
                        } else {
                            crmTodoRetryDoc.setStatus(1);
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(null);
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);

                        }
                    } else if(crmTodoRetryDoc.getEventType().equals(ExternalMsgTypeEnum.DealApprovalTask.name())) {
                        //审批机器人
                        Result<Void> dealExternalApprovalTodoTaskResult = externalTodoManager.dealExternalApprovalTodoTask(arg, employeeBindEntities, entity);
                        if(!dealExternalApprovalTodoTaskResult.isSuccess()) {
                            if(retryTimes == 3) {
                                crmTodoRetryDoc.setStatus(2);
                            }
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(dealExternalApprovalTodoTaskResult.getMsg());
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);
                        } else {
                            crmTodoRetryDoc.setStatus(1);
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(null);
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);

                        }
                    }
                } else if(crmTodoRetryDoc.getEvent().equals(OAMessageTag.DELETE_TO_DO)) {
                    DeleteTodoArg arg = gson.fromJson(crmTodoRetryDoc.getContent(), DeleteTodoArg.class);
                    List<String> fsUserIdList = arg.getDeleteEmployeeIds().stream().map(v -> v + "").collect(Collectors.toList());

                    List<EmployeeBindEntity> employeeBindEntities = employeeBindManager.getEntityList(arg.getEa(), outEa, fsUserIdList);

                    if(crmTodoRetryDoc.getEventType().equals(ExternalMsgTypeEnum.DeleteApproval.name())) {
                        //审批
                        Result<Void> deleteExternalApprovalsResult = externalTodoManager.deleteExternalApprovals(arg, employeeBindEntities, entity);
                        if(!deleteExternalApprovalsResult.isSuccess()) {
                            if(retryTimes == 3) {
                                crmTodoRetryDoc.setStatus(2);
                            }
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(deleteExternalApprovalsResult.getMsg());
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);
                        } else {
                            crmTodoRetryDoc.setStatus(1);
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(null);
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);

                        }
                    } else if(crmTodoRetryDoc.getEventType().equals(ExternalMsgTypeEnum.DeleteApprovalTask.name())) {
                        //审批机器人
                        Result<Void> deleteExternalApprovalTodoTaskResult = externalTodoManager.deleteExternalApprovalTodoTask(arg, employeeBindEntities, entity);
                        if(!deleteExternalApprovalTodoTaskResult.isSuccess()) {
                            if(retryTimes == 3) {
                                crmTodoRetryDoc.setStatus(2);
                            }
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(deleteExternalApprovalTodoTaskResult.getMsg());
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);
                        } else {
                            crmTodoRetryDoc.setStatus(1);
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(null);
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);

                        }
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(crmTodoRetryDocLinkedList)) {
                BulkWriteResult bulkWriteResult = crmTodoRetryMongoDao.batchReplace(crmTodoRetryDocLinkedList);
                LogUtils.info("ExternalMsgServiceImpl.deleteTodo,bulkWriteResult={}.", bulkWriteResult);
            }
        }
        return Result.newSuccess();
    }

    private Boolean isDealTodo(String fsEa, String outEa, String sourceId) {
        ExternalApprovalsTemplateEntity templateEntity = externalApprovalsTemplateManager.queryEntity(ChannelEnum.feishu, outEa);
        LogUtils.info("ExternalTodoMsgServiceImpl.isDealTodo,templateEntity={}", templateEntity);
        //取消灰度或者不想推送到审批中心去的时候，遗留待办需要处理下
        boolean isDeal = Boolean.FALSE;
        //不等于空才是创建过审批的
        if(ObjectUtils.isNotEmpty(templateEntity)) {
            if(ConfigCenter.TODO_GRAY_EA.contains(fsEa) && templateEntity.getStatus() == 0) {
                isDeal = Boolean.TRUE;
            } else {
                //需要查看是否还存有审批，审批机器人的不需要处理，只查看审批中心的审批，因为这个会在未办中，处理审批中心的时候审批机器人也会处理
                ExternalTodoInstanceEntity externalTodoInstanceEntity = externalTodoInstanceManager.queryEntity(fsEa, outEa, sourceId);
                if(ObjectUtils.isNotEmpty(externalTodoInstanceEntity)) {
                    isDeal = Boolean.TRUE;
                }
            }
        }
        LogUtils.info("ExternalTodoMsgServiceImpl.isDealTodo,sourceId={},isDeal={}", sourceId, isDeal);
        return isDeal;
    }
}
