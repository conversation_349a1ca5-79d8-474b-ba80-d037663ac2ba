package com.facishare.open.feishu.sync.service.impl;

import com.facishare.open.feishu.sync.manager.ServiceAuthManager;
import com.facishare.open.feishu.syncapi.entity.ServiceAuthEntity;
import com.facishare.open.feishu.syncapi.info.FsServiceAuthInfo;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.ServiceAuthService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("serviceAuthService")
public class ServiceAuthServiceImpl implements ServiceAuthService {
    @Resource
    private ServiceAuthManager serviceAuthManager;

    @Override
    public Result<FsServiceAuthInfo> getServiceAuth(String fsService) {
        List<ServiceAuthEntity> serviceAuthEntities = serviceAuthManager.query(fsService);
        if(CollectionUtils.isEmpty(serviceAuthEntities)) {
            return Result.newSuccess();
        }
        FsServiceAuthInfo fsServiceAuthInfo = FsServiceAuthInfo.builder()
                .fsService(serviceAuthEntities.get(0).getFsService())
                .fsKey(serviceAuthEntities.get(0).getFsKey())
                .fsSecret(serviceAuthEntities.get(0).getFsSecret())
                .status(serviceAuthEntities.get(0).getStatus())
                .build();
        return Result.newSuccess(fsServiceAuthInfo);
    }

    @Override
    public Result<Integer> addServiceAuth(FsServiceAuthInfo fsServiceAuthInfo) {
        List<ServiceAuthEntity> serviceAuthEntities = serviceAuthManager.query(fsServiceAuthInfo.getFsService());
        if(CollectionUtils.isNotEmpty(serviceAuthEntities)) {
            return Result.newSuccess();
        }
        ServiceAuthEntity entity = new ServiceAuthEntity();
        entity.setFsService(fsServiceAuthInfo.getFsService());
        entity.setFsKey(fsServiceAuthInfo.getFsKey());
        entity.setFsSecret(fsServiceAuthInfo.getFsSecret());

        Integer insert = serviceAuthManager.insert(entity);
        return Result.newSuccess(insert);
    }
}
