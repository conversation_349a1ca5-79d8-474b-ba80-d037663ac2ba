package com.facishare.open.feishu.sync.service.impl;

import com.facishare.open.feishu.sync.manager.OAConnectorOpenDataManager;
import com.facishare.open.feishu.syncapi.model.OAConnectorOpenDataModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.MonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("monitorService")
public class MonitorServiceImpl implements MonitorService {
    @Autowired
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;

    @Override
    public Result<Void> uploadOaConnectorOpenData(OAConnectorOpenDataModel oaConnectorOpenDataModel) {
        oaConnectorOpenDataManager.send(oaConnectorOpenDataModel);
        return Result.newSuccess();
    }
}
