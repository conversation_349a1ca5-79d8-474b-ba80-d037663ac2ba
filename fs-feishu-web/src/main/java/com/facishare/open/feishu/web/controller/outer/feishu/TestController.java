package com.facishare.open.feishu.web.controller.outer.feishu;

import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.feishu.syncapi.service.SuperAdminService;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 服务接口测试类
 * <AUTHOR>
 * @date 20221020
 */
@RestController
@RequestMapping(value="/feishu/test")
public class TestController {
    @Resource
    private RedisDataSource redisDataSource;
    @Resource
    private EnterpriseBindService enterpriseBindService;

    @Resource
    private SuperAdminService superAdminService;

    /**
     * 测试接口
     *
     * @param content 请求参数
     */
    @RequestMapping(value = "/hello", method = RequestMethod.GET)
    @ResponseBody
    public Result<String> hello(String content) {
        return Result.newSuccess("content="+content);
    }

    /**
     * 设置redis值
     * @param key
     * @param value
     * @return
     */
    @RequestMapping(value="/setRedisValue",method = RequestMethod.GET)
    @ResponseBody
    public Result<String> setRedisValue(@RequestParam String key,
                                        @RequestParam String value) {
        return Result.newSuccess(redisDataSource.getRedisClient().set(key,value));
    }

    /**
     * 获取redis值
     * @param key
     * @return
     */
    @RequestMapping(value="/getRedisValue",method = RequestMethod.GET)
    @ResponseBody
    public Result<String> getRedisValue(@RequestParam String key) {
        return Result.newSuccess(redisDataSource.getRedisClient().get(key));
    }

    /**
     * 获取企业绑定列表
     * @param outEa
     * @return
     */
    @RequestMapping(value="/getEnterpriseBindList",method = RequestMethod.GET)
    @ResponseBody
    public Result<List<EnterpriseBindEntity>> getEnterpriseBindList(@RequestParam String outEa) {
        return enterpriseBindService.getEnterpriseBindList(outEa);
    }

//    @RequestMapping(value = "/superInsert", method = RequestMethod.POST)
//    public Result<Integer> superInsert(@RequestBody String sql){
//        return superAdminService.superInsertSql(sql);
//    }
}