package com.facishare.open.feishu.web.controller.outer.feishu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.model.event.ExternalDealTodoEvent;
import com.facishare.open.feishu.syncapi.model.event.FeishuEncryptMsgModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.Result3;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.service.ExternalTodoMsgService;
import com.facishare.open.feishu.web.handler.FeishuEventHandler;
import com.facishare.open.feishu.web.template.FeishuOuterEventHandlerTemplate;
import com.facishare.open.feishu.web.utils.FeishuDecrypt;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 飞书商店应用和自建应用事件回调接口
 * <AUTHOR>
 * @date 2022-05-26
 */
@RestController
@RequestMapping(value="/feishu/event")
public class FeishuEventController {
    @Resource
    private ExternalTodoMsgService externalTodoMsgService;
    @Resource
    private FeishuOuterEventHandlerTemplate feishuOuterEventHandlerTemplate;

    /**
     * 飞书商店应用和自建应用事件回调
     * @param body
     * @return
     */
    @RequestMapping(value="/push",method = RequestMethod.POST)
    @ResponseBody
    public String push(@RequestBody String body) {
        LogUtils.info("FeishuEventController.push,body={}",body);
        return feishuOuterEventHandlerTemplate.execute(body).getDataOrMsg();
    }

//    /**
//     * 消息卡片请求网址
//     * 每当用户操作消息卡片时，飞书服务器会向你配置的网址发出请求
//     * @param body
//     * @return
//     */
//    @RequestMapping(value="/robot/push",method = RequestMethod.POST)
//    @ResponseBody
//    public String robotPush(@RequestBody String body) {
//        LogUtils.info("FeishuEventController.robotPush,body={}",body);
//
//        try {
//            //验证网页推送事件
//            String verifyResult = feishuOuterEventHandlerTemplate.verifyUrl(body);
//            if(!StringUtils.equalsIgnoreCase(verifyResult,FeishuEventHandler.FAIL)) {
//                return verifyResult;
//            }
//        } catch (Exception e) {
//            LogUtils.info("FeishuEventController.robotPush,exception={}",e.getMessage());
//        }
//        return FeishuEventHandler.FAIL;
//    }

    @RequestMapping(value="/approval/deal",method = RequestMethod.POST)
    @ResponseBody
    public Result3<Void> approvalDeal(@RequestBody String body,
                                      HttpServletRequest req,
                                      HttpServletResponse resp) throws ServletException, IOException {
        LogUtils.info("FeishuEventController.approvalDeal,body={}",body);
        FeishuEncryptMsgModel model = JSONObject.parseObject(body,FeishuEncryptMsgModel.class);
        LogUtils.info("FeishuEventController.approvalDeal,model={}",model);
        if(model==null || StringUtils.isEmpty(model.getEncrypt())) {
            // 设置状态码
            resp.setStatus(HttpServletResponse.SC_ACCEPTED);
            return Result3.newError(ResultCodeEnum.EXTERNAL_DEAL_TODO_ERROR);
        }
        FeishuDecrypt feishuDecrypt = new FeishuDecrypt(ConfigCenter.feishuEncryptKey);
        try {
            String plainText = feishuDecrypt.decrypt(model.getEncrypt());
            LogUtils.info("FeishuEventController.approvalDeal,plainText={}",plainText);
            if(StringUtils.isEmpty(plainText)) {
                // 设置状态码
                resp.setStatus(HttpServletResponse.SC_ACCEPTED);
                return Result3.newError(ResultCodeEnum.EXTERNAL_DEAL_TODO_ERROR);
            }

            //获取token
            JSONObject eventJsonObject = JSONObject.parseObject(plainText);
            Object tokenObject = eventJsonObject.get("token");
            if(ObjectUtils.isNotEmpty(tokenObject) && String.valueOf(tokenObject).equals(ConfigCenter.APPROVAL_ACTION_CALLBACK_TOKEN)) {
                ExternalDealTodoEvent externalDealTodoEvent = JSON.parseObject(plainText, ExternalDealTodoEvent.class);
                Result<Void> dealCrmTodoResult = externalTodoMsgService.dealCrmTodo(externalDealTodoEvent);
                if(dealCrmTodoResult.isSuccess()) {
                    return Result3.newSuccess();
                } else {
                    // 设置状态码
                    resp.setStatus(HttpServletResponse.SC_ACCEPTED);
                    return Result3.newError(dealCrmTodoResult.getCode(), dealCrmTodoResult.getMsg());
                }
            }

            // 设置状态码
            resp.setStatus(HttpServletResponse.SC_ACCEPTED);
            return Result3.newError(ResultCodeEnum.EXTERNAL_DEAL_TODO_ERROR);
        } catch (Exception e) {
            LogUtils.info("FeishuEventController.approvalDeal,exception={}",e.getMessage());
            // 设置状态码
            resp.setStatus(HttpServletResponse.SC_ACCEPTED);
            return Result3.newError(ResultCodeEnum.EXTERNAL_DEAL_TODO_ERROR);
        }
    }
}
