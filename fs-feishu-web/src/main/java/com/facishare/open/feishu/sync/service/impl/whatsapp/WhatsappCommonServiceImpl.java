package com.facishare.open.feishu.sync.service.impl.whatsapp;

import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.syncapi.data.whatsapp.WhatsappBindInfo;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.whatsapp.WhatsappBalanceResult;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappCommonService;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("whatsappCommonService")
public class WhatsappCommonServiceImpl implements WhatsappCommonService {
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Resource
    private WhatsappService whatsappService;

    @Override
    public Result<WhatsappBalanceResult> getBalance(String fsEa) {
        EnterpriseBindEntity enterpriseBindEntity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, fsEa, null);
        if(ObjectUtils.isEmpty(enterpriseBindEntity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        WhatsappBindInfo bindInfo = new WhatsappBindInfo();
        bindInfo.setFsEa(fsEa);
        bindInfo.setAppKey(enterpriseBindEntity.getAppKey());
        bindInfo.setAccessKey(enterpriseBindEntity.getAccessKey());
        bindInfo.setAccessSecret(enterpriseBindEntity.getAccessSecret());
        bindInfo.setNxAppKey(enterpriseBindEntity.getNxAppKey());
        bindInfo.setNxSecretKey(enterpriseBindEntity.getNxSecretKey());
        return getBalance(bindInfo);
    }

    @Override
    public Result<WhatsappBalanceResult> getBalance(WhatsappBindInfo bindInfo) {
        WhatsappBindInfo whatsappBindInfo = new WhatsappBindInfo();
        BeanUtils.copyProperties(bindInfo, whatsappBindInfo);
        whatsappBindInfo.setFsEa(null);
        com.facishare.open.feishu.syncapi.result.whatsapp.Result<WhatsappBalanceResult> whatsappPhoneResult = whatsappService.whatsappGetBalance(whatsappBindInfo);

        if(!whatsappPhoneResult.isSuccess()) {
            return Result.newError(ResultCodeEnum.getCodeByOutCode(String.valueOf(whatsappPhoneResult.getCode()), ResultCodeEnum.GET_NX_BALANCE_ERROR.getCode()), whatsappPhoneResult.getMessage());
        }
        return Result.newSuccess(whatsappPhoneResult.getData());
    }
}
