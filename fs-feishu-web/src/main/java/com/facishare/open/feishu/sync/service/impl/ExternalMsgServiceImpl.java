package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.sync.limiter.CrmMessagePullLimiter;
import com.facishare.open.feishu.sync.manager.EmployeeBindManager;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.enums.BindStatusEnum;
import com.facishare.open.feishu.syncapi.enums.CrmMessageEnum;
import com.facishare.open.feishu.syncapi.enums.MsgTypeEnum;
import com.facishare.open.feishu.syncapi.model.SendMessage.PostMassageModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.ExternalMsgService;
import com.facishare.open.feishu.syncapi.service.MsgService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.message.extrnal.platform.model.arg.BaseExternalArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("externalMsgService")
public class ExternalMsgServiceImpl implements ExternalMsgService {
    @Autowired
    private MsgService msgService;
    @Autowired
    private EnterpriseBindManager enterpriseBindManager;
    @Autowired
    private EmployeeBindManager employeeBindManager;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;
    @Resource
    private CrmMessagePullLimiter crmMessagePullLimiter;

    @Override
    public Result<Void> sendTextMessage(SendTextMessageArg arg) {
        if(CollectionUtils.isEmpty(arg.getReceiverIds())) {
            return Result.newSuccess();
        }
        List<String> fsUserIdList = arg.getReceiverIds().stream().map(v -> v + "").collect(Collectors.toList());

        List<EnterpriseBindEntity> enterpriseBindEntities = enterpriseBindManager.getEntityList(arg.getEa(), BindStatusEnum.normal);
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newSuccess();
        }

        //针对企业，过滤消息
        Map<String, String> filterMessagesEaMap = new Gson().fromJson(ConfigCenter.FILTER_MESSAGES_EA, new TypeToken<Map<String, String>>() {
        });
        if(filterMessagesEaMap.containsKey(arg.getEa())) {
            Boolean isPull = crmMessagePullLimiter.messageIsPull(arg.getEi(), filterMessagesEaMap.get(arg.getEa()), new Gson().toJson(arg));
            if(!isPull) {
                return Result.newSuccess();
            }
        }

        for(EnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            //消息推送有可能把专属云的待办推送到纷享云
            if(!enterpriseBindEntity.getDomain().equals(ConfigCenter.crm_domain)) {
                return Result.newSuccess();
            }
            List<EmployeeBindEntity> employeeBindEntities = employeeBindManager.getEntityList(arg.getEa(), fsUserIdList);
            if(CollectionUtils.isEmpty(employeeBindEntities)) {
                return Result.newSuccess();
            }
            String feishuAppId=ObjectUtils.isEmpty(enterpriseBindEntity.getAppId())?ConfigCenter.feishuCrmAppId:enterpriseBindEntity.getAppId();
            //拼接文本内容
            JSONObject jsonObject  = JSONObject.parseObject(arg.getReceiverChannelData());
            Object appId = jsonObject.get("appId");
            CrmMessageEnum messageEnum = CrmMessageEnum.getAppId(appId.toString());
            PostMassageModel postMessageModel = new PostMassageModel();
            PostMassageModel.LanguageModel languageModel = new PostMassageModel.LanguageModel();
            PostMassageModel.LanguageModel.TextMessage textMessage = new PostMassageModel.LanguageModel.TextMessage();
            PostMassageModel.LanguageModel.TextMessage.Content content = new PostMassageModel.LanguageModel.TextMessage.Content();
            content.setTag(MsgTypeEnum.text.name());
            content.setText(arg.getMessageContent());
            textMessage.setTitle(messageEnum.getMessageType());
            List<PostMassageModel.LanguageModel.TextMessage.Content> contentList = new LinkedList<>();
            contentList.add(content);
            List<List<PostMassageModel.LanguageModel.TextMessage.Content>> messageContentList = new LinkedList<>();
            messageContentList.add(contentList);
            textMessage.setContent(messageContentList);
            languageModel.setZh_cn(textMessage);
            postMessageModel.setPost(languageModel);
            LogUtils.info("ExternalMsgServiceImpl.sendTextMessage,ea={},fsUserIdList={},postMessageModel={}.", arg.getEa(), fsUserIdList, postMessageModel);
            List<String> receiverIds = employeeBindEntities.stream().map(EmployeeBindEntity::getOutUserId).collect(Collectors.toList());
            Result<Void> msgResult = msgService.batchSend(feishuAppId, enterpriseBindEntity.getOutEa(), MsgTypeEnum.post, receiverIds, postMessageModel);
            LogUtils.info("ExternalMsgServiceImpl.sendTextMessage,msgResult={}.", msgResult);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> sendTextCardMessage(SendTextCardMessageArg arg) {
        if(CollectionUtils.isEmpty(arg.getReceiverIds())) {
            return Result.newSuccess();
        }
        List<String> fsUserIdList = arg.getReceiverIds().stream().map(v -> v + "").collect(Collectors.toList());
        List<EnterpriseBindEntity> enterpriseBindEntities = enterpriseBindManager.getEntityList(arg.getEa(), BindStatusEnum.normal);
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newSuccess();
        }

        //针对企业，过滤消息
        Map<String, String> filterMessagesEaMap = new Gson().fromJson(ConfigCenter.FILTER_MESSAGES_EA, new TypeToken<Map<String, String>>() {
        });
        if(filterMessagesEaMap.containsKey(arg.getEa())) {
            Boolean isPull = crmMessagePullLimiter.messageIsPull(arg.getEi(), filterMessagesEaMap.get(arg.getEa()), new Gson().toJson(arg));
            if(!isPull) {
                return Result.newSuccess();
            }
        }

        for(EnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            if(!enterpriseBindEntity.getDomain().equals(ConfigCenter.crm_domain)) {
                return Result.newSuccess();
            }
            List<EmployeeBindEntity> employeeBindEntities = employeeBindManager.getEntityList(arg.getEa(), fsUserIdList);
            if(CollectionUtils.isEmpty(employeeBindEntities)) {
                return Result.newSuccess();
            }
            String feishuAppId=ObjectUtils.isEmpty(enterpriseBindEntity.getAppId())?ConfigCenter.feishuCrmAppId:enterpriseBindEntity.getAppId();

            //拼接文本内容
            JSONObject jsonObject  = JSONObject.parseObject(arg.getReceiverChannelData());
            Object appId = jsonObject.get("appId");
            CrmMessageEnum messageEnum = CrmMessageEnum.getAppId(ObjectUtils.isNotEmpty(appId) ? appId.toString() : null);
            PostMassageModel postMessageModel = new PostMassageModel();
            PostMassageModel.LanguageModel languageModel = new PostMassageModel.LanguageModel();
            PostMassageModel.LanguageModel.TextMessage textMessage = new PostMassageModel.LanguageModel.TextMessage();
            PostMassageModel.LanguageModel.TextMessage.Content content = new PostMassageModel.LanguageModel.TextMessage.Content();
            StringBuilder markdown = new StringBuilder();
            if (CollectionUtils.isNotEmpty(arg.getForm())) {
                for (int i = 0; i < arg.getForm().size(); i++) {
                    markdown.append(arg.getForm().get(i).getKey()).append("：").append(arg.getForm().get(i).getValue()).append("\n");
                }
            } else {
                markdown.append(arg.getMessageContent()).append("\n");
            }
            content.setTag(MsgTypeEnum.text.name());
            content.setText(markdown.toString());
            textMessage.setTitle(messageEnum.getMessageType() + (StringUtils.isNotEmpty(arg.getTitle()) ? "：" + arg.getTitle() : ""));
            PostMassageModel.LanguageModel.TextMessage.Content hrefContent = new PostMassageModel.LanguageModel.TextMessage.Content();
            hrefContent.setTag("a");
            hrefContent.setText("\n查看详情");
            //处理url,如果有自建应用，此处的appId需要改成从库取
            String todoUrl;
            if(arg.getGenerateUrlType() == BaseExternalArg.BPM_TASK_URL) {
                if(ObjectUtils.isEmpty(arg.getExtraDataMap())
                        || StringUtils.isEmpty(arg.getExtraDataMap().get("workflowInstanceId"))
                        || StringUtils.isEmpty(arg.getExtraDataMap().get("activityId"))) {
                    //暂无跳转链接，返回，打印日志，不发送：https://wiki.firstshare.cn/pages/viewpage.action?pageId=214734110
                    LogUtils.info("ExternalMsgServiceImpl.sendTextCardMessage,param is null,arg={}.", arg);
                    return Result.newSuccess();
                }
                //bpm
//            todoUrl = getEnterpriseInfo(arg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FUNCTION_URL + ConfigCenter.CRM_BPM_URL
//                    .replace("{workflowInstanceId}", arg.getExtraDataMap().get("workflowInstanceId"))
//                    .replace("{activityId}", arg.getExtraDataMap().get("activityId"))
//                    .replace("{apiname}", arg.getExtraDataMap().get("objectApiName"))
//                    .replace("{id}", arg.getExtraDataMap().get("objectId"));
                //bpm业务流，默认先跳转到对象详情页面，等BPM团队做完业务流详情页面，再改成跳转到业务流详情页面
                todoUrl = getEnterpriseInfo(arg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FUNCTION_URL + ConfigCenter.CRM_TODO_URL
                        .replace("{apiname}", arg.getExtraDataMap().get("objectApiName"))
                        .replace("{id}", arg.getExtraDataMap().get("objectId"))
                        .replace("{ea}", arg.getEa());
            } else if(arg.getGenerateUrlType() == 5
                    && StringUtils.isNotEmpty(arg.getReceiverChannelData())
                    && ObjectUtils.isNotEmpty(appId)
                    && appId.toString().equals("WJTZ")
                    && ObjectUtils.isNotEmpty(arg.getExtraDataMap())
                    && StringUtils.isNotEmpty(arg.getExtraDataMap().get("filePath"))) {
                //不能直接使用
                String fileUrl = getEnterpriseInfo(arg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FILE_URL.replace("{path}", arg.getExtraDataMap().get("filePath"));
                //encode 4 次
                fileUrl = URLEncoder.encode(URLEncoder.encode(URLEncoder.encode(URLEncoder.encode(fileUrl))));
                todoUrl = getEnterpriseInfo(arg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FUNCTION_URL + ConfigCenter.CRM_COMMON_WEBVIEW + fileUrl;
            } else {
                todoUrl = getEnterpriseInfo(arg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FUNCTION_URL + arg.getUrl();
            }
            LogUtils.info("sendTextCardMessage,todoUrl={}",todoUrl);
            byte[] todoUrlBytes = todoUrl.getBytes();
            String param = new String(Base64.encodeBase64(todoUrlBytes));
            String authUrl = ConfigCenter.FEISHU_AUTHEN_URL
                    .replace("{app_id}", feishuAppId)
                    .replace("{state}", feishuAppId )
                    .replace("{redirect_uri}", URLEncoder.encode(ConfigCenter.CRM_AUTH_URL + "?param=" + param + "&fsEa=" + arg.getEa()));
            String url = ConfigCenter.FEISHU_WEB_URL
                    .replace("{mode}", "window")
                    .replace("{url}", URLEncoder.encode(authUrl));
            hrefContent.setHref(url);
            List<PostMassageModel.LanguageModel.TextMessage.Content> contentList = new LinkedList<>();
            contentList.add(content);
            contentList.add(hrefContent);
            List<List<PostMassageModel.LanguageModel.TextMessage.Content>> messageContentList = new LinkedList<>();
            messageContentList.add(contentList);
            textMessage.setContent(messageContentList);
            languageModel.setZh_cn(textMessage);
            postMessageModel.setPost(languageModel);
            LogUtils.info("ExternalMsgServiceImpl.sendTextCardMessage,ea={},fsUserIdList={},postMessageModel={}.", arg.getEa(), fsUserIdList, postMessageModel);
            List<String> receiverIds = employeeBindEntities.stream().map(EmployeeBindEntity::getOutUserId).collect(Collectors.toList());
            Result<Void> msgResult = msgService.batchSend(feishuAppId, enterpriseBindEntity.getOutEa(), MsgTypeEnum.post, receiverIds, postMessageModel);
            LogUtils.info("ExternalMsgServiceImpl.sendTextCardMessage,msgResult={}.", msgResult);
        }

        return Result.newSuccess();
    }

    private GetEnterpriseDataResult getEnterpriseInfo(String fsEa) {
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(fsEa);
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        LogUtils.info("ExternalMsgServiceImpl.getEnterpriseInfo,result={}",result);
        return result;
    }
}
