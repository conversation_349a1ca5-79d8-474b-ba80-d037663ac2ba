package com.facishare.open.feishu.sync.manager;

import com.facishare.open.feishu.sync.mongo.dao.OaConnectorOutUserInfoMongoDao;
import com.facishare.open.feishu.sync.mongo.document.OaConnectorOutUserInfoDoc;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

@Slf4j
@Component("oaConnectorOutUserInfoManager")
public class OaConnectorOutUserInfoManager {
    @Autowired
    private OaConnectorOutUserInfoMongoDao oaConnectorOutUserInfoMongoDao;

    public BulkWriteResult batchReplace(List<OaConnectorOutUserInfoDoc> docs) {
        BulkWriteResult bulkWriteResult = oaConnectorOutUserInfoMongoDao.batchReplace(docs);
        return bulkWriteResult;
    }

    public DeleteResult deleteUserInfoByUserId(ChannelEnum channel, String outEa, String outUserId) {
        DeleteResult deleteResult = oaConnectorOutUserInfoMongoDao.deleteUserInfoByUserId(channel, outEa, outUserId);
        return deleteResult;
    }

    public List<OaConnectorOutUserInfoDoc> queryUserInfos(ChannelEnum channel, String outEa) {
        List<OaConnectorOutUserInfoDoc> docs = oaConnectorOutUserInfoMongoDao.queryUserInfos(channel, outEa);
        return docs;
    }

    public List<OaConnectorOutUserInfoDoc> queryUserInfosByIds(ChannelEnum channel, String outEa, List<String> outUserIds) {
        List<OaConnectorOutUserInfoDoc> docs = oaConnectorOutUserInfoMongoDao.queryUserInfosByIds(channel, outEa, outUserIds);
        return docs;
    }

    public DeleteResult deleteNotInCollectionDocs(ChannelEnum channel, String outEa, Collection<OaConnectorOutUserInfoDoc> docs) {
        DeleteResult deleteResult = oaConnectorOutUserInfoMongoDao.deleteNotInCollectionDocs(channel, outEa, docs);
        return deleteResult;
    }

    public Long countDocuments(ChannelEnum channel, String outEa) {
        Long counts = oaConnectorOutUserInfoMongoDao.countDocuments(channel, outEa);
        return counts;
    }
}
