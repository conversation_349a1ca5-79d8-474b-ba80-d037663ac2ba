package com.facishare.open.feishu.web.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.open.feishu.syncapi.model.event2.FeishuContactUserUpdatedV3Event;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.facishare.open.feishu.syncapi.service.EmployeeBindService;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 员工信息变更处理器
 * <AUTHOR>
 * @date 20220818
 */
@Slf4j
@Component
public class ContactUserUpdatedV3EventHandler extends FeishuEventHandler {
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private EnterpriseBindService enterpriseBindService;
    @Resource
    private ContactsService contactsService;

    @Override
    public String getSupportEventType() {
        return "contact.user.updated_v3";
    }

    @Override
    public String handle(FeishuEventModel2.EventModelHeader header, String eventData) {
        LogUtils.info("ContactUserUpdatedV3EventHandler.handle,eventData={}",eventData);
        FeishuContactUserUpdatedV3Event event = JSON.parseObject(eventData, FeishuContactUserUpdatedV3Event.class);
        LogUtils.info("ContactUserUpdatedV3EventHandler.handle,event={}",event);
        Result<Boolean> booleanResult = enterpriseBindService.hasManualBind(header.getTenantKey());
        if(!booleanResult.isSuccess()) {
            return SUCCESS;
        }
        if(booleanResult.getData()) {
            log.info("ContactUserUpdatedV3EventHandler.handle,outEa={},手动绑定的企业，不支持更新人员",header.getTenantKey());
            contactsService.saveOrUpdateContactUser(header.getEventType(), header.getAppId(),
                    header.getTenantKey(),
                    event.getObject(), event.getOldObject(), eventData);
            return SUCCESS;
        }
        Result<Void> result = employeeBindService.addOrUpdateEmployee(header, event);
        LogUtils.info("ContactUserUpdatedV3EventHandler.handle,addOrUpdateEmployee,result={}",result);
        return SUCCESS;
    }
}
