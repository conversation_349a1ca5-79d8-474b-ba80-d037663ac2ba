package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.facishare.open.feishu.sync.manager.FeishuAppManager;
import com.facishare.open.feishu.sync.manager.UrlManager;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.order.contacts.proxy.api.enums.FeishuUrlEnum;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.feishu.syncapi.config.GlobalValue;
import com.facishare.open.feishu.syncapi.enums.MsgTypeEnum;
import com.facishare.open.feishu.syncapi.enums.ReceiverIdTypeEnum;
import com.facishare.open.feishu.syncapi.model.SendMessage.SendMessageErrorResult;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.FeishuMessageService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

@Service("feishuMessageService")
public class FeishuMessageServiceImpl implements FeishuMessageService {
    @Autowired
    private ProxyHttpClient proxyHttpClient;

    @Autowired
    private FeishuAppManager feishuAppManager;
    @Autowired
    private UrlManager urlManager;

    @ReloadableProperty("connector_domain")
    private String connectorDomain; //比如 https://www.fxiaoke.com/erpdss

    @Override
    public Result<Void> send(String appId,
                             String tenantKey,
                             MsgTypeEnum msgType,
                             String receiveId,
                             String content) {
        return send(appId, tenantKey, msgType, ReceiverIdTypeEnum.open_id, receiveId, content);
    }

    @Override
    public Result<Void> send(String appId,
                             String tenantKey,
                             MsgTypeEnum msgType,
                             ReceiverIdTypeEnum receiveIdType,
                             String receiveId,
                             String content) {
        String messageUrl=urlManager.getFeishuUrl(tenantKey,appId,FeishuUrlEnum.im_v1_messages.getUrl()).concat("?receive_id_type=")+ receiveIdType.toString();
        HashMap<String, String> bodyParams = new HashMap<>();
        bodyParams.put("receive_id", receiveId);
        bodyParams.put("content", content);
        bodyParams.put("msg_type", msgType.name());

        Result<String> tenantAccessToken = feishuAppManager.getTenantAccessToken(appId, tenantKey);
        if(tenantAccessToken.isSuccess()==false) {
            return Result.newError(tenantAccessToken.getCode(),tenantAccessToken.getMsg());
        }

        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Bearer " + tenantAccessToken.getData());
        headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimit, String.format(FeishuUrlEnum.im_v1_messages.getName(), appId, tenantKey));
        headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimitTimes, ConfigCenter.OUT_LIMIT_TIMES);
        SendMessageErrorResult result = proxyHttpClient.postUrl(messageUrl, bodyParams, headerMap, new TypeReference<SendMessageErrorResult>() {
        });
        return Result.newError(result.getCode(), result.getMsg() + "->" + result.getError());
    }

    @Override
    public Result<String> batchSend(String appId,
                                    String tenantKey,
                                    MsgTypeEnum msgType,
                                    List<String> openIdList,
                                    String content) {
        return batchSend(appId, tenantKey, msgType, ReceiverIdTypeEnum.open_id, openIdList, content);
    }

    @Override
    public Result<String> batchSend(String appId,
                                    String tenantKey,
                                    MsgTypeEnum msgType,
                                    ReceiverIdTypeEnum receiveIdType,
                                    List<String> openIdList,
                                    String content) {
        return null;
    }

    @Override
    public Result<Void> sendWelcomeMsg(String appId, String tenantKey, String openUserId) {
        String json = GlobalValue.WELCOME_MESSAGE_MODEL;
        String url = "https://applink.feishu.cn/client/web_url/open?mode=window&url={url}";
        String loginUrl = "https://open.feishu.cn/open-apis/authen/v1/index?app_id={app_id}&state={app_id}&redirect_uri={connector_domain}/feishu/external/loginAuth";
        loginUrl = loginUrl.replace("{app_id}",appId).replace("{connector_domain}",connectorDomain);
        url = url.replace("{url}", URLEncoder.encode(loginUrl));
        json = json.replace("{crm_app_url}",url);
        return send(appId, tenantKey, MsgTypeEnum.interactive, openUserId, json);
    }

    @Override
    public Result<Void> reply(String appId,
                              String tenantKey,
                              String messageId,
                              String content,
                              MsgTypeEnum msgType) {

        String url = "https://open.feishu.cn/open-apis/im/v1/messages/" + messageId + "/reply";
        HashMap<String, String> bodyParams = new HashMap<>();
        bodyParams.put("content", content);
        bodyParams.put("msg_type", msgType.name());
        bodyParams.put("uuid", UUID.randomUUID().toString());

        Result<String> tenantAccessToken = feishuAppManager.getTenantAccessToken(appId, tenantKey);
        if(tenantAccessToken.isSuccess()==false) {
            return Result.newError(tenantAccessToken.getCode(),tenantAccessToken.getMsg());
        }

        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Bearer " + tenantAccessToken.getData());
        headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimit, String.format(FeishuUrlEnum.im_v1_messages_message_id_reply.getName(), appId, tenantKey));
        headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimitTimes, ConfigCenter.OUT_LIMIT_TIMES);
        SendMessageErrorResult result = proxyHttpClient.postUrl(url, bodyParams, headerMap, new TypeReference<SendMessageErrorResult>() {
        });
        return Result.newError(result.getCode(), result.getMsg() + "->" + result.getError());
    }
}
