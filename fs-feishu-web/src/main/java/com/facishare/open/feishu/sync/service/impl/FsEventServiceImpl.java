package com.facishare.open.feishu.sync.service.impl;

import com.facishare.open.feishu.sync.manager.AppInfoManager;
import com.facishare.open.feishu.sync.manager.EmployeeBindManager;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.syncapi.config.GlobalValue;
import com.facishare.open.feishu.syncapi.entity.AppInfoEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.enums.BindStatusEnum;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.facishare.open.feishu.syncapi.service.FeishuMessageService;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsEventService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Deprecated
@Service("fsEventService")
public class FsEventServiceImpl implements FsEventService {
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Resource
    private AppInfoManager appInfoManager;
    @Resource
    private EmployeeBindManager employeeBindManager;
    @Resource
    private ContactsService contactsService;
    @Resource
    private FeishuMessageService feishuMessageService;

    @Override
    public Result<Boolean> isEnterpriseBind(String ea) {
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.feishu,ea, BindStatusEnum.create);
        LogUtils.info("FsEventServiceImpl.isEnterpriseBind,entity={}", entity);
        return Result.newSuccess(entity!=null);
    }

    @Override
    public Result<Void> onEnterpriseOpened(Integer ei, String ea, String enterpriseName) {
        LogUtils.info("FsEventServiceImpl.onEnterpriseOpened,ei={},ea={},enterpriseName={}", ei,ea,enterpriseName);
        enterpriseBindManager.updateBindStatus(ea, null, BindStatusEnum.normal);
        employeeBindManager.batchUpdateBindStatus(ea, Lists.newArrayList(GlobalValue.FS_ADMIN_USER_ID+""),BindStatusEnum.normal,null);
        contactsService.initContactsAsync(ea);

        EnterpriseBindEntity enterpriseBindEntity = enterpriseBindManager.getEntity(ea);
        AppInfoEntity appInfoEntity = appInfoManager.getEntity(enterpriseBindEntity.getOutEa());
        //发送欢迎消息给应用安装人员
        feishuMessageService.sendWelcomeMsg(appInfoEntity.getAppId(),appInfoEntity.getTenantKey(),appInfoEntity.getInstallerOpenId());
        return Result.newSuccess();
    }
}
