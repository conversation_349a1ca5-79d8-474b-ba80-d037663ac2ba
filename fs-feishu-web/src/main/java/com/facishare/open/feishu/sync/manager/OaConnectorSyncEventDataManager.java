package com.facishare.open.feishu.sync.manager;

import com.facishare.open.feishu.sync.mongo.dao.OaConnectorSyncEventDataMongoDao;
import com.facishare.open.feishu.sync.mongo.document.OaConnectorSyncEventDataDoc;
import com.facishare.open.feishu.syncapi.arg.QueryOaConnectorSyncEventDataArg;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component("oaConnectorSyncEventDataManager")
public class OaConnectorSyncEventDataManager {
    @Autowired
    private OaConnectorSyncEventDataMongoDao oaConnectorSyncEventDataMongoDao;

    public BulkWriteResult batchReplace(List<OaConnectorSyncEventDataDoc> docs) {
        BulkWriteResult bulkWriteResult = oaConnectorSyncEventDataMongoDao.batchReplace(docs);
        return bulkWriteResult;
    }

    public DeleteResult deleteTableDataByDelArg(QueryOaConnectorSyncEventDataArg arg) {
        DeleteResult deleteResult = oaConnectorSyncEventDataMongoDao.deleteTableDataByDelArg(arg);
        return deleteResult;
    }

    public List<OaConnectorSyncEventDataDoc> pageByQuerySyncEventDataArg(QueryOaConnectorSyncEventDataArg arg) {
        List<OaConnectorSyncEventDataDoc> docs = oaConnectorSyncEventDataMongoDao.pageByQuerySyncEventDataArg(arg);
        return docs;
    }
}
