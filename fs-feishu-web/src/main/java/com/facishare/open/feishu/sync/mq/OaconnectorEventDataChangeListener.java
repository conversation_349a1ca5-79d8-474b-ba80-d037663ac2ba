package com.facishare.open.feishu.sync.mq;

import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.enums.BindTypeEnum;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.feishu.syncapi.proto.OaconnectorEventDateChangeProto;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("oaconnectorEventDataChangeListener")
public class OaconnectorEventDataChangeListener implements MessageListenerConcurrently {
    @Resource
    private EnterpriseBindService enterpriseBindService;
    @Resource
    private EnterpriseBindManager enterpriseBindManager;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : list) {
            TraceUtils.initTraceId(msg.getMsgId());
            try {
                //测试环境没有专属云
                String env = System.getProperty("process.profile");
                if(env.equals("fstest") || env.equals("fstest-gray")) {
                    continue;
                }

                //专属云不消费
                if(!ConfigCenter.MAIN_ENV) {
                    continue;
                }

                String receiveTags = msg.getTags();
                if(!receiveTags.equals(ChannelEnum.feishu.name())) {
                    continue;
                }

                OaconnectorEventDateChangeProto proto = new OaconnectorEventDateChangeProto();
                proto.fromProto(msg.getBody());
                LogUtils.info("OaconnectorEventDataChangeListener.consumeMessage,proto={}", proto);
                if(StringUtils.isNotEmpty(proto.getEventType()) && proto.getEventType().equals("oaconnector_enterprise_bind")) {
                    if(proto.getType().equals("add") || proto.getType().equals("update")) {
                        if(proto.getType().equals("add")) {
                            List<EnterpriseBindEntity> bindManagerEntities = enterpriseBindManager.getAllEnterpriseBindList(proto.getOutEa());
                            if(CollectionUtils.isNotEmpty(bindManagerEntities)) {
                                for(EnterpriseBindEntity entity : bindManagerEntities) {
                                    if(entity.getBindType() == BindTypeEnum.auto) {
                                        int delete = enterpriseBindManager.deleteByFsEa(entity.getFsEa(), entity.getOutEa());
                                        LogUtils.info("OaconnectorEventDataChangeListener,mapping={},result={}", entity, delete);
                                        break;
                                    }
                                }
                            }
                        }
                        enterpriseBindService.cloudFsBindWithFeishu(proto.getOutEa(), proto.getAppId(), proto.getFsEa(), proto.getDomain());
                    } else if(proto.getType().equals("deleteByFsEa")) {
                        enterpriseBindManager.deleteByFsEa(proto.getFsEa(), proto.getOutEa(), proto.getDomain());
                    }
                }
            } catch (Exception e) {
                LogUtils.info("OaconnectorEventDataChangeListener.consumeMessage,exception={}",e.getMessage(),e);
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
