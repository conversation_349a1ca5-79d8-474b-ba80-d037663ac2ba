package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.sync.manager.FeishuAppManager;
import com.facishare.open.feishu.sync.manager.UrlManager;
import com.facishare.open.feishu.sync.utils.XorUtils;
import com.facishare.open.feishu.syncapi.consts.OutUrlConsts;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.enums.FeishuUrlEnum;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.model.jsapi.JsApiSignatureModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.data.JsApiTicketData;
import com.facishare.open.feishu.syncapi.service.JsApiService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.RandomUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;

@Service("jsApiService")
public class JsApiServiceImpl implements JsApiService {
    @Resource
    private FeishuAppManager feishuAppManager;
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Resource
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private UrlManager urlManager;

    @Override
    public Result<JsApiSignatureModel> getJsApiSignature(String appId, String fsEa, String url) {
        return getJsApiSignature2(appId,fsEa,null,url);
    }

    @Override
    public Result<JsApiSignatureModel> getJsApiSignature2(String appId, String fsEa, String outEa, String url) {
        LogUtils.info("FeishuServiceImpl.getJsApiSignature,appId={},fsEa={},url={}",appId,fsEa,url);
        //这个appId=default来自俊文，默认为飞书CRM应用的ID，需要进行转换
        if(StringUtils.equalsIgnoreCase(appId,"default")) {
            appId = ConfigCenter.feishuCrmAppId;
        }
        //需要兼容其它应用
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(fsEa);
        if(ObjectUtils.isNotEmpty(entity)&&StringUtils.isNotEmpty(entity.getAppId())){
            appId=entity.getAppId();
        }
        String signature_format = "jsapi_ticket={jsApiTicket}&noncestr={nonceStr}&timestamp={timeStamp}&url={url}";
        Result<String> jsApiTicket = getJsApiTicket(appId,fsEa,outEa);
        if(jsApiTicket.isSuccess()==false) {
            return Result.newError(jsApiTicket.getCode(),jsApiTicket.getMsg());
        }
        if(StringUtils.isEmpty(jsApiTicket.getData())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        String nonceStr = RandomUtils.getRandomStr();
        long timeStamp = System.currentTimeMillis();

        String verifyStr = signature_format.replace("{jsApiTicket}",jsApiTicket.getData())
                .replace("{nonceStr}",nonceStr)
                .replace("{timeStamp}",timeStamp+"")
                .replace("{url}",url);
        LogUtils.info("FeishuServiceImpl.getJsApiSignature,verifyStr={}", XorUtils.EncodeByXor(verifyStr, ConfigCenter.XOR_SECRET_KEY));

        String signature = DigestUtils.sha1Hex(verifyStr);

        JsApiSignatureModel signatureModel = JsApiSignatureModel.builder()
                .appId(appId)
                .nonceStr(nonceStr)
                .timestamp(timeStamp)
                .url(url)
                .signature(signature)
                .build();
        return Result.newSuccess(signatureModel);
    }

    private Result<String> getJsApiTicket(String appId,String fsEa, String outEa) {
        LogUtils.info("FeishuServiceImpl.getJsApiTicket,appId={},fsEa={},outEa={}", appId, fsEa, outEa);

        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(fsEa,outEa);
        if(entity==null) return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);

        if(StringUtils.isEmpty(outEa)) {
            outEa = entity.getOutEa();
        }

        String key = "feishu-"+appId+"-"+outEa+"-jsapi_ticket";
        String jsApiTicket = redisDataSource.getRedisClient().get(key);
        if (StringUtils.isNotEmpty(jsApiTicket)) {
            if(ConfigCenter.isOpenLog) {
                LogUtils.info("FeishuStoreAppManager.getJsApiTicket,appId={},tenantKey={},tenantAccess={}", appId, outEa, XorUtils.EncodeByXor(jsApiTicket, ConfigCenter.XOR_SECRET_KEY));
            }
            return Result.newSuccess(jsApiTicket);
        }

        Result<String> tenantAccessToken = feishuAppManager.getTenantAccessToken(appId, outEa);
        if(tenantAccessToken.isSuccess()==false) {
            return Result.newError(tenantAccessToken.getCode(),tenantAccessToken.getMsg());
        }

        String url =urlManager.getFeishuUrl(outEa, appId, FeishuUrlEnum.jssdk_ticket_get.getUrl());
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Bearer " + tenantAccessToken.getData());
        headerMap.put("Content-Type","application/json; charset=utf-8");

        Result<JsApiTicketData> result;
        if(ConfigCenter.isOpenLog) {
            String postResult = proxyHttpClient.postUrl(url, new HashMap<>(), headerMap);
            result = JSONObject.parseObject(postResult, new TypeReference<Result<JsApiTicketData>>() {
            });
        } else {
            result = proxyHttpClient.postUrl(url, new HashMap<>(), headerMap, new TypeReference<Result<JsApiTicketData>>() {
            });
        }
        //tenant access token失效，清理token，并重新获取数据
        if(result.getCode()!=0) {
            feishuAppManager.clearAppAccessToken(ConfigCenter.feishuCrmAppId);
            result = proxyHttpClient.postUrl(url, new HashMap<>(), headerMap, new TypeReference<Result<JsApiTicketData>>() {
            });
        }
        LogUtils.info("FeishuServiceImpl.getJsApiTicket,result={}",result);

        jsApiTicket = result.getData().getTicket();
        LogUtils.info("FeishuServiceImpl.getJsApiTicket,jsApiTicket={},Authorization={}", XorUtils.EncodeByXor(jsApiTicket, ConfigCenter.XOR_SECRET_KEY), XorUtils.EncodeByXor(tenantAccessToken.getData(), ConfigCenter.XOR_SECRET_KEY));
        redisDataSource.getRedisClient().set(key, jsApiTicket);
        //jsApiTicket有效期是2小时，提前2分钟获取token
        long expire = result.getData().getExpireIn() - 2 * 60L;
        if (expire < 0) {
            expire = result.getData().getExpireIn();
        }
        redisDataSource.getRedisClient().expire(key, expire);
        return Result.newSuccess(jsApiTicket);
    }

    public static void main(String[] args) {
        String verifyStr = "jsapi_ticket=617bf955832a4d4d80d9d8d85917a427&noncestr=Y7a8KkqX041bsSwT&timestamp=1510045655000&url=https://m.haiwainet.cn/ttc/3541093/2018/0509/content_31312407_1.html";
        String signature = DigestUtils.sha1Hex(verifyStr);
        System.out.println(signature);
    }
}
