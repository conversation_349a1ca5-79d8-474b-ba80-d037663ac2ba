package com.facishare.open.feishu.sync.manager;

import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.model.connect.BaseConnectParams;
import com.facishare.open.feishu.syncapi.model.connect.FeishuAppConnectParams;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2024/11/25 11:43
 * 根据应用信息以及企业，返回对应的真实的url
 * @desc
 */
@Component
public class UrlManager {
    @Autowired
    private EnterpriseBindManager enterpriseBindManager;


    public String getFeishuUrlByPointDomain( String path,String pointDomain) {
        return path.replace("feishu.cn",pointDomain);
    }

    public String getFeishuUrl(String outEa, String appId, String path) {
        EnterpriseBindEntity entity = enterpriseBindManager.getEntityByOutEAaAppId(outEa, appId);
        if(ObjectUtils.isNotEmpty(entity)&& StringUtils.isNotEmpty(entity.getConnectParams())){
            FeishuAppConnectParams feishuAppConnectParams = entity.getChannel().getConnectParam(entity.getConnectParams());
            if(feishuAppConnectParams.getBaseUrl()!=null){
                String feishuUrl=path.replace("feishu.cn",feishuAppConnectParams.getBaseUrl());
                return feishuUrl;
            }
        }
        if(ConfigCenter.FEISHU_ISV_REQUEST_MAP.get(appId)!=null){
            return path.replace("feishu.cn",ConfigCenter.FEISHU_ISV_REQUEST_MAP.get(appId));
        }
        return path;
    }

    public String getFeishuUrlByFsEa(String fsEa, String appId, String path) {
        EnterpriseBindEntity entity = enterpriseBindManager.getEntityFsEaAppId(fsEa, appId);
        if(ObjectUtils.isNotEmpty(entity)&& StringUtils.isNotEmpty(entity.getConnectParams())){
            FeishuAppConnectParams feishuAppConnectParams = entity.getChannel().getConnectParam(entity.getConnectParams());
            if(feishuAppConnectParams.getBaseUrl()!=null){
                String feishuUrl=path.replace("feishu.cn",feishuAppConnectParams.getBaseUrl());
                return feishuUrl;
            }
        }
        if(ConfigCenter.FEISHU_ISV_REQUEST_MAP.get(appId)!=null){
            return path.replace("feishu.cn",ConfigCenter.FEISHU_ISV_REQUEST_MAP.get(appId));
        }
        return path;
    }
}
