package com.facishare.open.feishu.web.controller.outer.feishu;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.feishu.sync.manager.UrlManager;
import com.facishare.open.feishu.syncapi.arg.QueryEmployeeBindArg;
import com.facishare.open.feishu.syncapi.arg.SendTextNoticeArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.consts.OutUrlConsts;
import com.facishare.open.feishu.syncapi.entity.AppInfoEntity;
import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.enums.BindStatusEnum;
import com.facishare.open.feishu.syncapi.enums.BindTypeEnum;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.feishu.syncapi.enums.DataTypeEnum;
import com.facishare.open.feishu.syncapi.info.EmployeeBindInfo;
import com.facishare.open.feishu.syncapi.model.OAConnectorOpenDataModel;
import com.facishare.open.feishu.syncapi.model.login.LoginAuthModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.data.Code2UserInfoData;
import com.facishare.open.feishu.syncapi.result.data.GetAppAdminUserListData;
import com.facishare.open.feishu.syncapi.result.data.QueryTenantInfoData;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.service.*;
import com.facishare.open.feishu.web.model.AuthUserInfoModel;
import com.facishare.open.feishu.web.model.FeishuAuthModel;
import com.facishare.open.feishu.web.template.inner.login.FeishuLoginTemplate;
import com.facishare.open.feishu.web.template.model.GenFsTicketModel;
import com.facishare.open.feishu.web.utils.SecurityUtil;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 飞书外部服务接口
 *
 * <AUTHOR>
 * @date 20220726
 */
@RestController
@RequestMapping(value = "/feishu/external")
public class FeishuExternalController {
    @Resource
    private FeishuLoginTemplate feishuLoginTemplate;
    @Resource
    private EnterpriseBindService enterpriseBindService;
    @Resource
    private AppService appService;
    @Resource
    private CorpService corpService;
    @Resource
    private FeishuAppService feishuAppService;
    @Resource
    private FeishuTenantService feishuTenantService;
    @Autowired
    private UrlManager urlManager;

    @ReloadableProperty("feishu_redirect_uri")
    private String feishuRedirectUri;//{"cli_a20192f6afb8d00c":"/hcrm/feishu/login?ticket="}

    @ReloadableProperty("crm_domain")
    private String crmDomain; //比如 https://www.fxiaoke.com

    @ReloadableProperty("connector_domain")
    private String connectorDomain; //比如 https://www.fxiaoke.com/erpdss

    @Resource
    private EnterpriseEditionService enterpriseEditionService;

    @Resource
    private NotificationService notificationService;

    @Resource
    private MonitorService monitorService;

    @Resource
    private EmployeeBindService employeeBindService;

    private static final String VER = "V1_";

    @Resource
    private RedisDataSource redisDataSource;

    @Resource
    private I18NStringManager i18NStringManager;

    @Autowired
    private EIEAConverter eieaConverter;

    /**
     * 飞书免登录授权
     *
     * @param code     飞书预授权码，5分钟有效
     * @param state    这个值默认是appId
     * @param fsEa     纷享ea，用于一个飞书企业对多个CRM的场景
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/loginAuth", method = RequestMethod.GET)
    @ResponseBody
    public void loginAuth(@RequestParam String code,
                          @RequestParam String state,
                          @RequestParam(required = false) String fsEa,
                          @RequestParam(required = false) String param,
                          @RequestParam(required = false) String lang,
                          HttpServletResponse response,
                          HttpServletRequest request) throws Exception {
        LogUtils.info("FeishuExternalController.loginAuth,code={},state={}", code, state);
        //1.code换飞书用户信息  state在飞书上配置=APPID.appid飞书不会透传，需要使用state作为上下文
        LoginAuthModel feishuAuthModel=new LoginAuthModel(code,state);

        MethodContext context = MethodContext.newInstance(feishuAuthModel);
        feishuLoginTemplate.getOutUserInfoByCode(context);
        Result<Code2UserInfoData> result = context.getResultData();
        LogUtils.info("FeishuExternalController.loginAuth,result={}", result);
        if (result.isSuccess() == false) {
            throw new RuntimeException(result.getMsg());
        }

        String outEa = result.getData().getTenant_key();
        String appId = state;
        String userId = result.getData().getOpen_id();
        String doMain = crmDomain;

        Result<List<EnterpriseBindEntity>> getEnterpriseBindListResult = enterpriseBindService.getAllEnterpriseBindList(outEa);
        if (!getEnterpriseBindListResult.isSuccess() || CollectionUtils.isEmpty(getEnterpriseBindListResult.getData())) {
            //飞书刚安装crm，数据库还没有记录，也跳转到等待页面
            //上报
            OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                    .appId(appId)
                    .channelId(ChannelEnum.feishu.name())
                    .dataTypeId(DataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                    .corpId(outEa)
                    .outUserId(userId)
                    .errorCode("100")
                    .errorMsg("人员点击应用登陆，该企业还没有绑定关系，请及时关注！")  //ignorei18n
                    .build();
            monitorService.uploadOaConnectorOpenData(model);
            //告警
            SendTextNoticeArg arg = new SendTextNoticeArg();
            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
            arg.setMsgTitle("人员从飞书登陆纷享失败告警"); //ignorei18n
            String msg = String.format("人员点击应用登陆，该企业还没有绑定关系\n飞书企业outEa=%s，飞书人员OutUserId=%s，traceId=%s\n请及时关注！", outEa, userId, TraceUtils.getTraceId()); //ignorei18n
            arg.setMsg(msg);
            notificationService.sendNotice(arg);

            response.sendRedirect(doMain + "/hcrm/template/initpage?channel=feishu&outEa="+ SecurityUtil.encryptStr(VER+outEa)+"&appId="+SecurityUtil.encryptStr(VER+appId)+"&userId="+SecurityUtil.encryptStr(VER+userId)+"&fsEa="+SecurityUtil.encryptStr(VER+fsEa)+"&timeStamp="+System.currentTimeMillis());
            return;
        }

        //获取normal状态的企业
        List<EnterpriseBindEntity> normalEnterpriseBindList = getEnterpriseBindListResult.getData().stream()
                .filter(v -> v.getBindStatus().equals(BindStatusEnum.normal))
                .collect(Collectors.toList());

        if(StringUtils.isEmpty(param)) {
            //获取纷享企业的信息
//            if(CollectionUtils.isNotEmpty(normalEnterpriseBindList)) {
//                GetEnterpriseDataResult enterpriseInfo = getEnterpriseInfo(normalEnterpriseBindList.get(0).getFsEa());
//                if(ObjectUtils.isNotEmpty(enterpriseInfo)
//                        && ObjectUtils.isNotEmpty(enterpriseInfo.getEnterpriseData())
//                        && StringUtils.isNotEmpty(enterpriseInfo.getEnterpriseData().getDomain())) {
//                    doMain = enterpriseInfo.getEnterpriseData().getDomain();
//                }
//            }

            boolean hasMore = Boolean.FALSE;
            Result<List<EmployeeBindInfo>> employeeBindInfoListResult = null;
            if (normalEnterpriseBindList.size() > 1) {
                Optional<EnterpriseBindEntity> matchingMapping = normalEnterpriseBindList.stream()
                        .filter(mapping -> !mapping.getDomain().equals(ConfigCenter.crm_domain))
                        .findFirst();

                if(matchingMapping.isPresent()) {
                    hasMore = Boolean.TRUE;
                } else {
                    employeeBindInfoListResult = employeeBindService.queryEmployeeBindListByOutData(ChannelEnum.feishu, outEa, userId);
                    if(employeeBindInfoListResult.getData().size() > 1) {
                        hasMore = Boolean.TRUE;
                    }
                }
            }

            //如果一个飞书企业和多个CRM企业绑定
            if (normalEnterpriseBindList.size() > 1 && hasMore) {
                response.sendRedirect(doMain + "/pc-login/build/select_enterprise.html?channel=feishu&outEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+outEa), "utf-8")+"&appId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+appId), "utf-8")+"&userId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+userId), "utf-8"));
            } else if(CollectionUtils.isEmpty(normalEnterpriseBindList)) {
                //存在企业绑定，但可能是create或者stop状态，先获取到create状态的企业
                List<EnterpriseBindEntity> createEnterpriseBindList = getEnterpriseBindListResult.getData().stream()
                        .filter(v -> v.getBindStatus().equals(BindStatusEnum.create))
                        .collect(Collectors.toList());
                if(CollectionUtils.isEmpty(createEnterpriseBindList)) {
                    //只有停用状态的企业
                    throw new RuntimeException("飞书和crm的绑定状态为已停用"); //ignorei18n
                }

                //上报
                OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                        .appId(appId)
                        .channelId(ChannelEnum.feishu.name())
                        .dataTypeId(DataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                        .corpId(outEa)
                        .outUserId(userId)
                        .errorCode("101")
                        .errorMsg("人员点击应用登陆，该企业还没有开通完成，请及时关注！")  //ignorei18n
                        .build();
                monitorService.uploadOaConnectorOpenData(model);
                //告警
                SendTextNoticeArg arg = new SendTextNoticeArg();
                arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                List<String> receivers = new LinkedList<>(ConfigCenter.ENTERPRISE_OPEN_NOTIFICATION_MEMBERS);
                arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                arg.setMsgTitle("人员从飞书登陆纷享失败告警");  //ignorei18n
                String msg = String.format("人员点击应用登陆，该企业还没有开通完成\n飞书企业outEa=%s，飞书人员OutUserId=%s，纷享企业ea=%s，traceId=%s\n请及时关注！", outEa, userId, createEnterpriseBindList.get(0).getFsEa(), TraceUtils.getTraceId()); //ignorei18n
                arg.setMsg(msg);
                notificationService.sendNotice(arg);

                LogUtils.info("FeishuExternalController.loginAuth,enterprise is in creation state,outEa={},appId={},userId={},fsEa={}", outEa, appId, userId,fsEa);
                //企业处于create状态，跳转到等待页面
                response.sendRedirect(doMain + "/hcrm/template/initpage?channel=feishu&outEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+outEa), "utf-8")+"&appId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+appId), "utf-8")+"&userId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+userId), "utf-8")+"&fsEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+fsEa), "utf-8")+"&timeStamp="+System.currentTimeMillis());
            } else {
                //检查人员绑定关系
                EnterpriseBindEntity normalEnterpriseBind = normalEnterpriseBindList.get(0);
                if(normalEnterpriseBindList.size() > 1 && employeeBindInfoListResult != null && CollectionUtils.isNotEmpty(employeeBindInfoListResult.getData())) {
                    String finalFsEa = employeeBindInfoListResult.getData().get(0).getFsEa();
                    LogUtils.info("FeishuExternalController.loginAuth,finalFsEa={}", finalFsEa);
                    // 使用 Stream API 查找匹配项
                    Optional<EnterpriseBindEntity> matchingMapping = normalEnterpriseBindList.stream()
                            .filter(mapping -> mapping.getFsEa().equals(finalFsEa))
                            .findFirst();

                    if(matchingMapping.isPresent()) {
                        normalEnterpriseBind = matchingMapping.get();
                    }
                }

                doMain = normalEnterpriseBind.getDomain();
                fsEa = normalEnterpriseBind.getFsEa();

                if(!doMain.equals(crmDomain)) {
                    //转到对应的环境处理
                    String redirectUri = doMain + "/erpdss/feishu/external/loginCloudAuth?channel=feishu&outEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+outEa), "utf-8")+"&appId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+appId), "utf-8")+"&userId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+userId), "utf-8")+"&fsEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+fsEa), "utf-8");
                    response.sendRedirect(redirectUri);
                    return;
                }

                Integer ei = eieaConverter.enterpriseAccountToId(normalEnterpriseBind.getFsEa());
                if(StringUtils.isEmpty(lang)) {
                    lang = i18NStringManager.getDefaultLang(String.valueOf(ei));
                }
                if(StringUtils.isEmpty(lang)) {
                    lang = "zh-CN";
                }
                i18NStringManager.setDefaultRequestScope(request, lang);

                //这里只有该环境的免登地址
                QueryEmployeeBindArg queryEmployeeBindArg = new QueryEmployeeBindArg();
                queryEmployeeBindArg.setFsEa(normalEnterpriseBind.getFsEa());
                queryEmployeeBindArg.setOutEa(outEa);
                queryEmployeeBindArg.setOutUserId(userId);
                Result<EmployeeBindInfo> employeeBindInfoResult = employeeBindService.queryEmployeeBind(ChannelEnum.feishu.name(), queryEmployeeBindArg);
                if(!employeeBindInfoResult.isSuccess()) {
                    request.setAttribute("errorCode", "s320050002");
                    request.setAttribute("errorMsg", StringUtils.isNotEmpty(employeeBindInfoResult.getMsg()) ? employeeBindInfoResult.getMsg() : i18NStringManager.get(I18NStringEnum.s66, lang, String.valueOf(ei)));
                    request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s65, lang, String.valueOf(ei)));
                    request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
                    return;
                }

                if(ObjectUtils.isEmpty(employeeBindInfoResult.getData())) {
                    //历史失败客户，重新创建一次
                    if(normalEnterpriseBindList.get(0).getBindType() == BindTypeEnum.auto) {
                        Result<Void> employeeInfoResult = employeeBindService.createEmployeeInfo(outEa, appId, userId, normalEnterpriseBindList.get(0).getFsEa());
                        if(employeeInfoResult.isSuccess()) {
                            employeeBindInfoResult = employeeBindService.queryEmployeeBind(ChannelEnum.feishu.name(), queryEmployeeBindArg);
                        } else {
                            if(employeeInfoResult.getCode() == ResultCodeEnum.FEISHU_NOT_PRIVILEGE_ERROR.getCode()) {
                                request.setAttribute("errorCode", "s320050002");
                                request.setAttribute("errorMsg", String.format(i18NStringManager.get(I18NStringEnum.s67, lang, String.valueOf(ei)), outEa, userId));
                                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s68, lang, String.valueOf(ei)));
                                request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
                                return;
                            } else if(employeeInfoResult.getCode() == ResultCodeEnum.CRM_USER_UPPER_LIMIT_INITED.getCode()) {
                                request.setAttribute("errorCode", "s320050002");
                                request.setAttribute("errorMsg", String.format(i18NStringManager.get(I18NStringEnum.s67, lang, String.valueOf(ei)), outEa, userId));
                                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s69, lang, String.valueOf(ei)));
                                request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
                                return;
                            }
                        }
                    }
                }

                if(ObjectUtils.isEmpty(employeeBindInfoResult.getData())) {
                    if(normalEnterpriseBindList.get(0).getBindType() == BindTypeEnum.auto) {
                        //上报
                        OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                                .ea(normalEnterpriseBindList.get(0).getFsEa())
                                .channelId(ChannelEnum.feishu.name())
                                .dataTypeId(DataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                                .corpId(outEa)
                                .outUserId(userId)
                                .errorCode("102")
                                .errorMsg("人员点击应用登陆，该人员账号未创建成功，请及时关注！") //ignorei18n
                                .build();
                        monitorService.uploadOaConnectorOpenData(model);
                        //告警
                        SendTextNoticeArg arg = new SendTextNoticeArg();
                        arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                        List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
                        arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                        arg.setMsgTitle("人员从飞书登陆纷享失败告警"); //ignorei18n
                        String msg = String.format("人员点击应用登陆，该人员账号未创建成功\n飞书企业ea=%s，飞书人员id=%s，飞书企业ea=%s，traceId=%s\n请及时关注！", outEa, userId, normalEnterpriseBindList.get(0).getFsEa(), TraceUtils.getTraceId()); //ignorei18n
                        arg.setMsg(msg);
                        notificationService.sendNotice(arg);
                        request.setAttribute("errorCode", "s320050002");
                        request.setAttribute("errorMsg", String.format(i18NStringManager.get(I18NStringEnum.s70, lang, String.valueOf(ei)), outEa, userId));
                        request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s71, lang, String.valueOf(ei)));
                        request.setAttribute("errorPageOutEa", outEa);
                        request.setAttribute("errorPageFsEa", normalEnterpriseBindList.get(0).getFsEa());
                        request.setAttribute("errorPageOutUserId", userId);
                        request.setAttribute("errorPageToken", ConfigCenter.ERROR_PAGE_TOKEN);
                        request.setAttribute("errorPageAppId", appId);
                        request.setAttribute("createEmployee", Boolean.TRUE);
                        request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
                        return;
                    } else {
                        request.setAttribute("errorCode", "s320050002");
                        request.setAttribute("errorMsg", String.format(i18NStringManager.get(I18NStringEnum.s70, lang, String.valueOf(ei)), outEa, userId));
                        request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s72, lang, String.valueOf(ei)));
                        request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
                        return;
                    }
                }

                //2.生成CRM ticket
                GenFsTicketModel ticketModel = new GenFsTicketModel();
                ticketModel.setAppId(appId);
                ticketModel.setOutEa(outEa);
                ticketModel.setFsEa(fsEa);
                ticketModel.setFsUserId(userId);

                MethodContext context2 = MethodContext.newInstance(ticketModel);
                feishuLoginTemplate.genFsTicket(context2);
                Result<String> ticket = context2.getResultData();
                LogUtils.info("FeishuExternalController.loginAuth,ticket={}", ticket);



                //3.生成重定向uri
                String redirectUri = getEnterpriseInfo(fsEa).getEnterpriseData().getDomain() + getFeishuRedirectUri(state) + ticket.getData();
                LogUtils.info("FeishuExternalController.loginAuth,redirectUri={}", redirectUri);

                //4.执行重定向
                response.sendRedirect(redirectUri);
            }
        } else {
            //从飞书卡片消息等免登录授权后跳转过来
            String pramUrl = new String(Base64.decodeBase64(param.getBytes()));
            LogUtils.info("FeishuExternalController.loginAuth,pramUrl={}", pramUrl);

            GenFsTicketModel ticketModel = new GenFsTicketModel();
            ticketModel.setAppId(appId);
            ticketModel.setOutEa(outEa);
            ticketModel.setFsEa(fsEa);
            ticketModel.setFsUserId(userId);

            MethodContext context2 = MethodContext.newInstance(ticketModel);
            feishuLoginTemplate.genFsTicket(context2);
            Result<String> ticket = context2.getResultData();

            LogUtils.info("FeishuExternalController.loginAuth,ticket2={}", ticket);
            String redirectUrl = pramUrl + (pramUrl.contains("?") ? "&" : "?") + "ticket="+ticket.getData();
            LogUtils.info("FeishuExternalController.loginAuth,redirectUrl={}", redirectUrl);
            response.sendRedirect(redirectUrl);
        }
    }

    private String decodeData(String data) {
        Pattern pattern = Pattern.compile(VER+"(.*)");
        Matcher matcher = pattern.matcher(SecurityUtil.decryptStr(data));
        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return null;
        }
    }

    /**
     * 飞书免登录授权，仅用于飞书连接器扫码登录授权
     *
     * @param code     飞书预授权码，5分钟有效
     * @param state    这个值默认是appId
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/loginAuth2", method = RequestMethod.GET)
    @ResponseBody
    public void loginAuth2(@RequestParam String code,
                           @RequestParam String state,
                           @RequestParam(required = false) String fsEa,
                           @RequestParam(required = false) String lang,
                           HttpServletResponse response,
                           HttpServletRequest request) throws Exception {
        LogUtils.info("FeishuExternalController.loginAuth2,code={},state={}", code, state);
        //1.code换飞书用户信息
        //不知道为啥state不用appid。现在登录授权只有isv.先默认给feishuappId.后面再讨论
        LoginAuthModel feishuAuthModel=new LoginAuthModel(code,ConfigCenter.feishuCrmAppId);
        MethodContext context = MethodContext.newInstance(feishuAuthModel);
        feishuLoginTemplate.getOutUserInfoByCode(context);
        Result<Code2UserInfoData> result = context.getResultData();
        LogUtils.info("FeishuExternalController.loginAuth2,result={}", result);
        if (result.isSuccess() == false) {
            throw new RuntimeException(result.getMsg());
        }

        Integer ei = null;
        if(StringUtils.isNotEmpty(fsEa)) {
            ei = eieaConverter.enterpriseAccountToId(fsEa);
            if(StringUtils.isEmpty(lang)) {
                lang = i18NStringManager.getDefaultLang(String.valueOf(ei));
            }
        }
        if(StringUtils.isEmpty(lang)) {
            lang = "zh-CN";
        }

        String outEa = result.getData().getTenant_key();
        String openUserId = result.getData().getOpen_id();
        Result<AppInfoEntity> appInfo = appService.getAppInfo(outEa);
        LogUtils.info("FeishuExternalController.loginAuth2,appInfo={}", appInfo);
        Result<CorpInfoEntity> corpEntity = corpService.getCorpEntity(outEa);
        LogUtils.info("FeishuExternalController.loginAuth2,corpEntity={}", corpEntity);
        if(appInfo.getData()==null || corpEntity.getData()==null) {
            i18NStringManager.setDefaultRequestScope(request, lang);
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", ObjectUtils.isEmpty(ei) ? I18NStringEnum.s73.getI18nValue() : i18NStringManager.get(I18NStringEnum.s73, lang, String.valueOf(ei)));
            request.setAttribute("propose", ObjectUtils.isEmpty(ei) ? I18NStringEnum.s73.getI18nValue() : i18NStringManager.get(I18NStringEnum.s73, lang, String.valueOf(ei)));
            request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
            return;
        }
        Result<GetAppAdminUserListData> adminUserList = feishuAppService.getAdminUserList(appInfo.getData().getAppId(),
                outEa);
        LogUtils.info("FeishuExternalController.loginAuth2,adminUserList={}", adminUserList);
        if(!adminUserList.isSuccess()) {
            i18NStringManager.setDefaultRequestScope(request, lang);
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", ObjectUtils.isEmpty(ei) ? I18NStringEnum.s74.getI18nValue() : i18NStringManager.get(I18NStringEnum.s74, lang, String.valueOf(ei)));
            request.setAttribute("propose", ObjectUtils.isEmpty(ei) ? I18NStringEnum.s75.getI18nValue() : i18NStringManager.get(I18NStringEnum.s75, lang, String.valueOf(ei)));
            request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
            return;
        }
        boolean isAdmin = false;
        if(CollectionUtils.isNotEmpty(adminUserList.getData().getUserList())) {
            for(UserData.UserId user : adminUserList.getData().getUserList()) {
                if(StringUtils.equalsIgnoreCase(user.getOpenId(),openUserId)) {
                    isAdmin = true;
                    break;
                }
            }
        }
        if(!isAdmin) {
            LogUtils.info("FeishuExternalController.loginAuth2,userId={},不是飞书CRM应用管理员", openUserId);  //ignorei18n
            i18NStringManager.setDefaultRequestScope(request, lang);
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", ObjectUtils.isEmpty(ei) ? I18NStringEnum.s76.getI18nValue() : i18NStringManager.get(I18NStringEnum.s76, lang, String.valueOf(ei)));
            request.setAttribute("propose", ObjectUtils.isEmpty(ei) ? I18NStringEnum.s77.getI18nValue() : i18NStringManager.get(I18NStringEnum.s77, lang, String.valueOf(ei)));
            request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
            return;
        }

        Result<QueryTenantInfoData> tenantInfo = feishuTenantService.queryTenantInfo(ConfigCenter.feishuCrmAppId, outEa);

        AuthUserInfoModel authUserInfoModel = new AuthUserInfoModel();
        authUserInfoModel.setDisplayId(corpEntity.getData().getDisplayId());
        authUserInfoModel.setOutEa(outEa);
        authUserInfoModel.setOutUserId(openUserId);

        if(tenantInfo.isSuccess() && tenantInfo.getData()!=null && tenantInfo.getData().getTenant()!=null) {
            authUserInfoModel.setCorpName(tenantInfo.getData().getTenant().getName());
        } else {
            authUserInfoModel.setCorpName(corpEntity.getData().getTenantName());
        }

        String json = JSONObject.toJSONString(authUserInfoModel);

        redisDataSource.getRedisClient().set(state,json);
        redisDataSource.getRedisClient().expire(state,10 * 60);

        String url = ConfigCenter.feishu_scan_code_auth_success_redirect_url+"?code="+URLEncoder.encode(json);
        response.sendRedirect(url);
    }

    /**
     *飞书免登
     * 1、企业初始化中点击crm应用，等待企业初始化结束，调用此接口免登
     * @param outEa 外部企业id
     * @param appId 应用id
     * @param userId 用户id
     * @param fsEa 纷享ea，可为空
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/loginRedirect", method = RequestMethod.GET)
    @ResponseBody
    @CrossOrigin
    public void loginRedirect(@RequestParam String outEa,
                              @RequestParam String appId,
                              @RequestParam String userId,
                              @RequestParam(required = false) String fsEa,
                              HttpServletResponse response) throws IOException {
        outEa = decodeData(URLDecoder.decode(outEa, "utf-8"));
        if(StringUtils.isEmpty(outEa)) {
            return;
        }

        appId = decodeData(URLDecoder.decode(appId, "utf-8"));
        if(StringUtils.isEmpty(appId)) {
            return;
        }

        userId = decodeData(URLDecoder.decode(userId, "utf-8"));
        if(StringUtils.isEmpty(userId)) {
            return;
        }

        if(StringUtils.isNotEmpty(fsEa)) {
            fsEa = decodeData(URLDecoder.decode(fsEa, "utf-8"));
            if(StringUtils.isEmpty(fsEa)) {
                return;
            }
        }

        //1.生成CRM ticket
        GenFsTicketModel ticketModel = new GenFsTicketModel();
        ticketModel.setAppId(appId);
        ticketModel.setOutEa(outEa);
        ticketModel.setFsEa(fsEa);
        ticketModel.setFsUserId(userId);

        MethodContext context = MethodContext.newInstance(ticketModel);
        feishuLoginTemplate.genFsTicket(context);
        Result<String> ticket = context.getResultData();

        LogUtils.info("FeishuExternalController.loginRedirect,ticket={}", ticket);

        //获取纷享企业的信息
//        String doMain = crmDomain;
//        if(StringUtils.isNotEmpty(fsEa)) {
//            GetEnterpriseDataResult enterpriseInfo = getEnterpriseInfo(fsEa);
//            if(ObjectUtils.isNotEmpty(enterpriseInfo)
//                    && ObjectUtils.isNotEmpty(enterpriseInfo.getEnterpriseData())
//                    && StringUtils.isNotEmpty(enterpriseInfo.getEnterpriseData().getDomain())) {
//                doMain = enterpriseInfo.getEnterpriseData().getDomain();
//            }
//        }


        //2.生成重定向uri
        String redirectUri = crmDomain + getFeishuRedirectUri(appId) + ticket.getData();
        LogUtils.info("FeishuExternalController.loginRedirect,redirectUri={}", redirectUri);

        //3.执行重定向
        response.sendRedirect(redirectUri);
    }

    /**
     * 飞书auth授权接口
     *
     * @param appId 当前应用的ID
     * @param param 自定义页面跳转参数，必须base64编码
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/doAuth", method = RequestMethod.GET)
    @ResponseBody
    public void doAuth(@RequestParam String appId,
                       @RequestParam(required = false) String fsEa,
                       @RequestParam(required = false) String param,
                       HttpServletResponse response) throws IOException {
        //https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=https://crm.ceshi112.com/feishu/external/loginAuth&app_id=cli_a3ddeb52763b100c&state=cli_a3ddeb52763b100c
        String url = urlManager.getFeishuUrlByFsEa(fsEa,appId,"https://open.feishu.cn/open-apis/authen/v1/index?app_id={app_id}&state={state}&redirect_uri={redirect_uri}");
        //appId脱敏处理
        if(appId.equals("100")) {
            appId = ConfigCenter.feishuCrmAppId;
        }
        url = url.replace("{app_id}",appId).replace("{state}",appId);

        String redirect_url = connectorDomain + "/feishu/external/loginAuth?param={param}";
        redirect_url = redirect_url.replace("{param}",param);
        if(StringUtils.isNotEmpty(fsEa)) {
            redirect_url = redirect_url + "&fsEa=" + fsEa;
        }

        url = url.replace("{redirect_uri}", URLEncoder.encode(redirect_url,"utf-8"));
        LogUtils.info("FeishuExternalController.doAuth,url={}",url);
        response.sendRedirect(url);
    }

    /**
     * 跨云免登
     * @param outEa 外部企业id
     * @param appId 应用id
     * @param userId 用户id
     * @param fsEa 纷享ea
     * @param request
     * @throws Exception
     */
    @RequestMapping(value = "/loginCloudAuth", method = RequestMethod.GET)
    @ResponseBody
    public void loginCloudAuth(@RequestParam String channel,
                               @RequestParam String appId,
                               @RequestParam String fsEa,
                               @RequestParam String outEa,
                               @RequestParam String userId,
                               @RequestParam(required = false) String lang,
                               @RequestParam(required = false) String redirectUrl,
                               @RequestParam(required = false) String url,
                               HttpServletResponse response,
                               HttpServletRequest request) throws Exception {
        appId = decodeData(appId);
        if(StringUtils.isEmpty(appId)) {
            return;
        }

        fsEa = decodeData(fsEa);
        if(StringUtils.isEmpty(fsEa)) {
            return;
        }

        outEa = decodeData(outEa);
        if(StringUtils.isEmpty(outEa)) {
            return;
        }

        userId = decodeData(userId);
        if(StringUtils.isEmpty(userId)) {
            return;
        }

        Integer ei = eieaConverter.enterpriseAccountToId(fsEa);
        if(StringUtils.isEmpty(lang)) {
            lang = i18NStringManager.getDefaultLang(String.valueOf(ei));
        }
        if(StringUtils.isEmpty(lang)) {
            lang = "zh-CN";
        }
        i18NStringManager.setDefaultRequestScope(request, lang);

        Result<List<EnterpriseBindEntity>> getEnterpriseBindListResult = enterpriseBindService.getEnterpriseBindList(outEa);
        if(!getEnterpriseBindListResult.isSuccess()) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", StringUtils.isNotEmpty(getEnterpriseBindListResult.getMsg()) ? getEnterpriseBindListResult.getMsg() : i18NStringManager.get(I18NStringEnum.s66, lang, String.valueOf(ei)));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s65, lang, String.valueOf(ei)));
            request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
            return;
        }
        if(CollectionUtils.isEmpty(getEnterpriseBindListResult.getData())) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", String.format(i18NStringManager.get(I18NStringEnum.s79, lang, String.valueOf(ei)), outEa, userId));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s78, lang, String.valueOf(ei)));
            request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
            return;
        }

        QueryEmployeeBindArg queryEmployeeBindArg = new QueryEmployeeBindArg();
        queryEmployeeBindArg.setFsEa(fsEa);
        queryEmployeeBindArg.setOutEa(outEa);
        queryEmployeeBindArg.setOutUserId(userId);
        Result<EmployeeBindInfo> employeeBindInfoResult = employeeBindService.queryEmployeeBind(ChannelEnum.feishu.name(), queryEmployeeBindArg);
        if(!employeeBindInfoResult.isSuccess()) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", StringUtils.isNotEmpty(employeeBindInfoResult.getMsg()) ? employeeBindInfoResult.getMsg() : i18NStringManager.get(I18NStringEnum.s66, lang, String.valueOf(ei)));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s65, lang, String.valueOf(ei)));
            request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
            return;
        }
        if(ObjectUtils.isEmpty(employeeBindInfoResult.getData())) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", String.format(i18NStringManager.get(I18NStringEnum.s70, lang, String.valueOf(ei)), outEa, userId));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s72, lang, String.valueOf(ei)));
            request.getRequestDispatcher("/errorpage.jsp").forward(request, response);
            return;
        }

        //2.生成CRM ticket
        GenFsTicketModel ticketModel = new GenFsTicketModel();
        ticketModel.setAppId(appId);
        ticketModel.setOutEa(outEa);
        ticketModel.setFsEa(fsEa);
        ticketModel.setFsUserId(userId);

        MethodContext context = MethodContext.newInstance(ticketModel);
        feishuLoginTemplate.genFsTicket(context);
        Result<String> ticket = context.getResultData();
        LogUtils.info("FeishuExternalController.loginCloudAuth,ticket={}", ticket);

        //3.生成重定向uri
        String redirectUri = crmDomain + getFeishuRedirectUri(appId) + ticket.getData();
        LogUtils.info("FeishuExternalController.loginCloudAuth,redirectUri={}", redirectUri);

        //4.执行重定向
        response.sendRedirect(redirectUri);
    }

    private GetEnterpriseDataResult getEnterpriseInfo(String fsEa) {
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(fsEa);
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        LogUtils.info("ControllerQYWeixin.getEnterpriseInfo,result={}",result);
        return result;
    }

    private String getFeishuRedirectUri(String appId){
        JSONObject redirectUriJson = JSONObject.parseObject(feishuRedirectUri);
        String feishuAppidUrl = redirectUriJson.getString(appId);
        if(ObjectUtils.isNotEmpty(feishuAppidUrl)){
            return feishuAppidUrl;
        }
        return OutUrlConsts.feishu_crmurl;
    }
}
